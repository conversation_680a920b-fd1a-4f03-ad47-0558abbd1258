<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.myweb</groupId>
        <artifactId>common-parent</artifactId>
        <version>3.0-SNAPSHOT</version>
    </parent>
    <groupId>com.les.its</groupId>
    <artifactId>openles</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>openles</name>
    <description>城市道路交通信号控制系统-莱斯协议服务</description>

    <properties>
        <fastjson.version>2.0.56</fastjson.version>
        <scm.version>4.0-SNAPSHOT</scm.version>
        <orika.version>1.5.4</orika.version>
        <netty.version>4.1.119.Final</netty.version>
        <h2.version>2.3.232</h2.version>
        <groovy-all.version>4.0.26</groovy-all.version>
        <spring-boot-admin-starter-client.version>3.3.6</spring-boot-admin-starter-client.version>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin-starter-client.version}</version>
        </dependency>
        <!-- 缓存数据 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.myweb</groupId>
            <artifactId>common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.myweb</groupId>
            <artifactId>common-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.myweb</groupId>
            <artifactId>common-ops</artifactId>
        </dependency>

        <!-- 接口生成工具swagger配置-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        </dependency>

        <!-- 类型转换使用-->
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>${orika.version}</version><!-- or latest version -->
        </dependency>

        <dependency>
            <groupId>com.les.its.scm</groupId>
            <artifactId>data-struct</artifactId>
            <version>${scm.version}</version>
        </dependency>
        <dependency>
            <groupId>com.les.its.scm</groupId>
            <artifactId>common</artifactId>
            <version>${scm.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.57</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.netty/netty-all -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.h2database/h2 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>${h2.version}</version>
        </dependency>

        <!-- 规则引擎 -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
            <type>pom</type>
        </dependency>

        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        
        <!-- Mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Spring AOP -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 分布式锁-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.45.1</version>
        </dependency>
        <!-- 限流 -->
        <dependency>
            <groupId>com.bucket4j</groupId>
            <artifactId>bucket4j_jdk17-redis-common</artifactId>
            <version>8.14.0</version>
        </dependency>
        <dependency>
            <groupId>com.bucket4j</groupId>
            <artifactId>bucket4j_jdk17-redisson</artifactId>
            <version>8.14.0</version>
        </dependency>
        <dependency>
            <groupId>com.bucket4j</groupId>
            <artifactId>bucket4j_jdk17-core</artifactId>
            <version>8.14.0</version>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>wechatmx-public</id>
            <name>Maximum Nexus Repository</name>
            <url>http://192.168.110.228:8080/content/groups/wechatmx-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>http://192.168.110.228:8080/content/repositories/spring-milestones/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <processors>
                                <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                                <processor>lombok.launch.AnnotationProcessorHider$AnnotationProcessor</processor>
                            </processors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <dockerHost>tcp://**************:2375</dockerHost>
                    <images>
                        <image>
                            <name>lesregistry:5000/les/${project.artifactId}-4.0</name>
                            <alias>les-${project.artifactId}</alias>
                            <build>
                                <from>lesregistry:5000/les/jre:17</from>
                                <tags>
                                    <tag>latest</tag>
                                </tags>
                                <entryPoint>
                                    <exec>
                                        <arg>java</arg>
                                        <arg>-Xmx2g</arg>
                                        <arg>-Dfile.encoding=UTF-8</arg>
                                        <arg>-Duser.timezone=GMT+08</arg>
                                        <arg>-jar</arg>
                                        <arg>/home/<USER>/${project.build.finalName}.jar</arg>
                                    </exec>
                                </entryPoint>
                                <assembly>
                                    <name>/home/<USER>/name>
                                    <descriptorRef>artifact</descriptorRef>
                                </assembly>
                            </build>
                            <run>
                                <log>
                                    <driver>
                                        <opts>
                                            <max-size>10m</max-size>
                                        </opts>
                                    </driver>
                                </log>
                                <namingStrategy>alias</namingStrategy>
                                <restartPolicy>
                                    <name>always</name>
                                </restartPolicy>
                                <network>
                                    <mode>custom</mode>
                                    <name>lestpms</name>
                                </network>
                            </run>
                        </image>
                    </images>
                    <authConfig>
                        <username>lesregistry</username>
                        <password>LESits123</password>
                    </authConfig>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

# OpenLES - 城市道路交通信号控制系统-莱斯协议服务

## 项目简介
OpenLES 是一个基于 Spring Boot 的城市道路交通信号控制系统，实现了莱斯协议服务。该项目采用现代化的技术栈，提供了高效、可靠的交通信号控制解决方案。

## 技术栈
- Java 17
- Spring Boot 3.x
- Spring Data JPA
- MySQL
- Netty 4.1.119.Final
- Fastjson2 2.0.56
- Spring Boot Admin Client 3.3.6
- Groovy 4.0.26 (规则引擎)
- JUnit 5
- Mockito

## 主要特性
- 基于 Netty 的高性能网络通信
- 规则引擎支持
- RESTful API 接口
- 数据库持久化
- 消息队列集成
- 完整的测试覆盖
- Docker 容器化支持

## 系统要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Docker (可选)

## 快速开始

### 1. 克隆项目
```bash
git clone [repository-url]
cd openles
```

### 2. 配置数据库
- 创建 MySQL 数据库
- 配置数据库连接信息（application.yml）

### 3. 构建项目
```bash
mvn clean package
```

### 4. 运行项目
```bash
java -jar target/openles-0.0.1-SNAPSHOT.jar
```

### 5. Docker 部署
```bash
mvn clean package docker:build
docker run -d -p 8080:8080 lesregistry:5000/les/openles-4.0:latest
```

## 项目结构
```
src/
├── main/
│   ├── java/
│   │   └── com/les/its/openles/
│   │       ├── config/        # 配置类
│   │       ├── controller/    # 控制器
│   │       ├── service/       # 服务层
│   │       ├── repository/    # 数据访问层
│   │       ├── model/         # 数据模型
│   │       └── util/          # 工具类
│   └── resources/
│       ├── application.yml    # 应用配置
│       └── static/            # 静态资源
└── test/                      # 测试代码
```

## 开发指南

### 代码规范
- 遵循 Java 编码规范
- 使用 Lombok 简化代码
- 遵循 RESTful API 设计规范
- 使用 JPA 进行数据访问

### 测试
- 使用 JUnit 5 进行单元测试
- 使用 Mockito 进行模拟测试
- 使用 Spring Test 进行集成测试

## 配置说明
主要配置项位于 `application.yml`，包括：
- 数据库连接
- 服务器端口
- 日志配置
- 消息队列配置
- Netty 配置

## 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证
[许可证信息]

## 联系方式
[联系信息]
package com.les.its.open.bussiness.service;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.bussiness.bean.P1049Entity;
import com.les.its.open.bussiness.bean.P1049Infos;
import com.les.its.open.bussiness.bean.QP1049Entity;
import com.les.its.open.area.message.service.CrossingService;
import com.myweb.commons.dao.RepositoryDao;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
@Getter
public class LocalSignalCacheService {
    @Autowired
    private JPAQueryFactory queryFactory;
    @Autowired
    private RepositoryDao repositoryDao;
    @Autowired
    private CrossingService crossingService;

    /**
     * 1049信号机数据项
     */
    private ConcurrentHashMap<String, Map<String, P1049Entity>> signalInfoMap = new ConcurrentHashMap<>();


    /*
     * 从数据库中加载基础数据项
     */
    public void loadDataFromDb() {

        StopWatch stopWatch = new StopWatch("读取原始数据");

        stopWatch.start("数据库读取数据");
        //读取数据项
        List<P1049Entity> p1049Entities = queryFactory.selectFrom(QP1049Entity.p1049Entity).fetch();
        stopWatch.stop();

        log.error("从数据库读取{}条数据花费时间——{}", p1049Entities.size(), stopWatch.prettyPrint());

        stopWatch.start("装载到内存");
        AtomicInteger dataCount = new AtomicInteger(0);
        //保存到当前内存中
        p1049Entities.forEach(
                p1049Entity -> {
                    Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(p1049Entity.getSignalId());
                    //此信号机的第一条数据项
                    if (stringP1049EntityMap == null) {
                        stringP1049EntityMap = new ConcurrentHashMap<>();
                        signalInfoMap.put(p1049Entity.getSignalId(), stringP1049EntityMap);
                    }

                    stringP1049EntityMap.put(p1049Entity.getKey(), p1049Entity);

                    int count = dataCount.getAndIncrement();
                    if (count % 5000 == 0) {
                        log.error("加载内存数据-{}", count);
                    }
                }
        );
        log.error("加载内存数据-{}", dataCount.get());
        stopWatch.stop();
        log.error("加载到内存花费时间——{}", stopWatch.prettyPrint());

    }


    /**
     * 调看信号机数据项时候更新本地缓存
     *
     * @param p1049Infos
     */
    public void updateData(P1049Infos p1049Infos) {

        List<P1049Entity> controlerInfosOrg = p1049Infos.getControlerInfos();

        Map<String, List<P1049Entity>> stringListMap =
                controlerInfosOrg.stream().collect(Collectors.groupingBy(P1049Entity::getSignalId));


        stringListMap.keySet().forEach(
            signalId -> {
                //查询是否已经有数据项
                Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(signalId);
                if (stringP1049EntityMap == null) {
                    stringP1049EntityMap = new ConcurrentHashMap<>();
                    signalInfoMap.put(signalId, stringP1049EntityMap);
                }

                //更新数据项
                final Map<String, P1049Entity> stringP1049EntityMapData = stringP1049EntityMap;
                stringListMap.get(signalId).forEach(
                        p1049Entity -> {
                            stringP1049EntityMapData.put(p1049Entity.getKey(), p1049Entity);
                        }
                );
            }
        );
    }

    /**
     * 移除特定的数据项
     *
     * @param signalId
     * @param subJuncNo
     * @param type
     * @param no
     */
    public void deleteData(String signalId, int subJuncNo, String type, String no) {
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(signalId);
        if (null == stringP1049EntityMap) {
            return;
        }

        stringP1049EntityMap.remove(P1049Entity.get1049DataKey(signalId, subJuncNo, type, no));
    }

    /**
     * 移除特定的数据项
     *
     * @param signalId
     * @param subJuncNo
     * @param type
     */
    public void deleteData(String signalId, int subJuncNo, String type) {
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(signalId);
        if (null == stringP1049EntityMap) {
            return;
        }

        String dataKeyPrefix = P1049Entity.get1049DataKeyPrefix(signalId, subJuncNo, type);
        List<String> keys = stringP1049EntityMap.keySet().stream().filter(
                key -> key.startsWith(dataKeyPrefix)
        ).collect(Collectors.toList());

        keys.stream().forEach(
                key -> stringP1049EntityMap.remove(key)
        );
    }


    /**
     * 更新一条数据项
     *
     * @param p1049Entity
     */
    public void updateData(P1049Entity p1049Entity) {
        //查询是否已经有数据项
        Map<String, P1049Entity> stringP1049EntityMap = signalInfoMap.get(p1049Entity.getSignalId());
        if (stringP1049EntityMap == null) {
            stringP1049EntityMap = new ConcurrentHashMap<>();
            signalInfoMap.put(p1049Entity.getSignalId(), stringP1049EntityMap);
        }
        //log.info("updateData key-{}info-{}", p1049Entity.getKey(), p1049Entity);
        stringP1049EntityMap.put(p1049Entity.getKey(), p1049Entity);
    }

    /**
     * 从缓存中获取一种数据项，指定no
     *
     * @param signalId
     * @param no
     * @param <T>
     * @return
     */
    public <T> Optional<T> getData(String signalId, int subJuncNo, String no, Class clazz) {
        Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        String nameKey = P1049Entity.get1049DataKey(signalId, subJuncNo, clazz.getSimpleName(), no);
        if (signalInfo.containsKey(nameKey)) {
            T data = (T) JSONObject.parseObject(signalInfo.get(nameKey).getData(), clazz);
            return Optional.of(data);
        } else {
            return Optional.empty();
        }
    }

    /**
     * 从缓存中获取一类数据项
     *
     * @param signalId
     * @param <T>
     * @return
     */
    public <T> Optional<Map<String, T>> getData(String signalId, int subJuncNo, Class clazz) {
        Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        Map<String, T> datas = new HashMap<>();
        String nameKeyPrefix = P1049Entity.get1049DataKeyPrefix(signalId, subJuncNo, clazz.getSimpleName());

        signalInfo.keySet().stream().filter(
                key -> key.startsWith(nameKeyPrefix)
        ).forEach(
                key -> {
                    T eachData = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                    datas.put(key.replace(nameKeyPrefix, ""), (eachData));
                }
        );

        if (datas.isEmpty()) {
            return Optional.empty();
        } else {
            return Optional.of(datas);
        }
    }


    /**
     * 从缓存中获取一类所有的数据项
     *
     * @param <T>
     * @return
     */
    public <T> Optional<Map<String, Map<String, T>>> getData(int subJuncNo, Class clazz) {
        Map<String, Map<String, T>> allData = new ConcurrentHashMap<>();
        signalInfoMap.keySet().stream().forEach(
                signalId -> {
                    Map<String, P1049Entity> signalInfo = signalInfoMap.get(signalId);
                    if (signalInfo == null) {
                        return;
                    }

                    Map<String, T> datas = new HashMap<>();
                    String nameKeyPrefix = P1049Entity.get1049DataKeyPrefix(signalId, subJuncNo, clazz.getSimpleName());

                    signalInfo.keySet().stream().filter(
                            key -> key.startsWith(nameKeyPrefix)
                    ).forEach(
                            key -> {
                                T eachData = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                                datas.put(key.replace(nameKeyPrefix, ""), (eachData));
                            }
                    );

                    if (!datas.isEmpty()) {
                        allData.put(signalId, datas);
                    }
                }
        );

        if (allData.isEmpty()) {
            return Optional.empty();
        } else {
            return Optional.of(allData);
        }
    }








}

package com.les.its.open.bussiness.repository;

import com.les.its.open.bussiness.bean.LinkLogEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface LinkLogEntityRepository extends JpaRepository<LinkLogEntity, String> {

    @Modifying
    @Transactional
    @Query("DELETE FROM LinkLogEntity e WHERE e.timeStampLog < :cutoffTime")
    int deleteByCreatedTimeBefore(@Param("cutoffTime") long cutoffTime);

    @Query(value = "SELECT e FROM LinkLogEntity e WHERE e.controllerId = :controllerId AND e.timeStampLog between :startTime AND :endTime") //, nativeQuery = true)
    List<LinkLogEntity> findByControllerIdAndTimeStampLogBetween(String controllerId, long startTime, long endTime);

}

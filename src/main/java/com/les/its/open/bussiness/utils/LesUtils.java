package com.les.its.open.bussiness.utils;

import com.les.its.open.event.AckManager.NeedAck;
import com.les.its.open.protocol.InterProtocolType;
import com.les.its.open.protocol.common.InterProtocol;
import com.les.its.open.protocol.common.OuterProtocolType;

import static com.les.its.open.event.MessageOuterPublisher.DEFAULT_TIMEOUT_MESSAGE;


/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/8 16:22
 */
public class LesUtils {


    /**
     * 构造数据报文
     *
     * @param data
     * @return
     */
    public static InterProtocol buildMessage(String ip, Object data, boolean isSimSend, boolean ackDirect) {
        if (data instanceof NeedAck) {
            NeedAck needAck = (NeedAck) data;
            return buildMessage(ip, InterProtocolType.OPENLES_DATA, data, true, needAck.getAckKey(), DEFAULT_TIMEOUT_MESSAGE, isSimSend, ackDirect);
        } else {
            return buildMessage(ip, InterProtocolType.OPENLES_DATA, data, false, "", DEFAULT_TIMEOUT_MESSAGE, isSimSend, ackDirect);
        }
    }


    /**
     * 构造数据报文
     *
     * @param data
     * @return
     */
    public static InterProtocol buildMessage(Object data, boolean isSimSend, boolean ackDirect) {
        if (data instanceof NeedAck) {
            NeedAck needAck = (NeedAck) data;
            return buildMessage("", InterProtocolType.OPENLES_DATA, data, true, needAck.getAckKey(), DEFAULT_TIMEOUT_MESSAGE, isSimSend,  ackDirect);
        } else {
            return buildMessage("", InterProtocolType.OPENLES_DATA, data, false, "", DEFAULT_TIMEOUT_MESSAGE, isSimSend, ackDirect);
        }
    }

    /**
     * @param interProtocolType
     * @param data
     * @param needAck
     * @return
     */
    public static InterProtocol buildMessage(String ip, InterProtocolType interProtocolType,
                                             Object data, boolean needAck, String ackKey, int timeOutSecond, boolean isSimSend, boolean ackDirect) {
        InterProtocol interProtocol = new InterProtocol();
        interProtocol.setOuterIp(ip);
        interProtocol.setOuterPort(0);
        interProtocol.setInterProtocolType(interProtocolType);
        interProtocol.setOuterProtocolType(String.valueOf(OuterProtocolType.OPENNLES.value()));
        interProtocol.setNeedAck(needAck);
        interProtocol.setAckKey(ackKey);
        interProtocol.setTimeOutSeconds(timeOutSecond);
        interProtocol.setData(data);
        interProtocol.setSimSend(isSimSend);
        interProtocol.setAckDirect(ackDirect);
        return interProtocol;
    }
}

package com.les.its.open.bussiness.utils;


import java.util.Optional;

public class CentralUtils {
    /**
     * 获取标准信号机的品牌
     * @param controllerId
     * @return
     */
    public static Optional<String> getBrand(String controllerId) {
        //320100LS06045
        if (controllerId == null || controllerId.length() != 13) {
            return Optional.empty();
        }
        return Optional.of(controllerId.substring(6,8));
    }

}

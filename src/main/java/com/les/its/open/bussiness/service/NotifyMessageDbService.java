package com.les.its.open.bussiness.service;

import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.bussiness.bean.QP1049Entity;
import com.myweb.commons.dao.RepositoryDao;
import com.les.its.open.bussiness.bean.P1049Entity;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class NotifyMessageDbService {

    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private JPAQueryFactory queryFactory;



    @Transactional
    @Async(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    public void deleteAndSave(String signalId, String type, P1049Entity p1049Entity) {
        queryFactory.delete(QP1049Entity.p1049Entity).where(
                QP1049Entity.p1049Entity.signalId.eq(signalId)
                        .and(QP1049Entity.p1049Entity.type.eq(type))
        ).execute();

        repositoryDao.save(p1049Entity, P1049Entity.class);
    }


}

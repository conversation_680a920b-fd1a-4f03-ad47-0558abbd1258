package com.les.its.open.bussiness.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class P1049Infos {

    @Builder.Default
    private List<P1049Entity> controlerInfos = new ArrayList<>();
}

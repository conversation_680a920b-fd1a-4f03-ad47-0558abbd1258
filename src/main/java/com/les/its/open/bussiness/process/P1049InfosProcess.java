package com.les.its.open.bussiness.process;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.bussiness.bean.DataIndexAble;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.bussiness.bean.QP1049Entity;
import com.myweb.commons.dao.RepositoryDao;
import com.les.its.open.bussiness.bean.P1049Entity;
import com.les.its.open.bussiness.bean.P1049Infos;
import com.les.its.open.bussiness.service.LocalSignalCacheService;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class P1049InfosProcess {
    @Autowired
    private RepositoryDao repositoryDao;

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private LocalSignalCacheService localSignalCacheService;


    /**
     * 构建保存数据项
     * @param objects
     * @param controllerBaseInfo
     * @return
     */
    public static P1049Infos build(List<Object> objects, ControllerBaseInfo controllerBaseInfo){
        return build(objects, controllerBaseInfo.getSignalId());
    }

    /**
     * 构建数据保存项
     * @param objects
     * @param controllerId
     * @return
     */
    public static P1049Infos build(List<Object> objects, String controllerId){
        List<P1049Entity> controlerInfos = new ArrayList<>();
        P1049Infos p1049Infos = P1049Infos.builder().controlerInfos(controlerInfos).build();

        //构建数据保存数据项
        if(!objects.isEmpty()){
            objects.forEach(
                    object -> {
                        if(object instanceof DataIndexAble) {
                            DataIndexAble dataIndexAble = (DataIndexAble) object;
                            //获取基础数据项
                            controlerInfos.add(
                                    P1049Entity.builder().type(object.getClass().getSimpleName())
                                            .signalId(controllerId)
                                            .no(String.valueOf(dataIndexAble.getDataNo()))
                                            .subJuncNo(1)
                                            .data(JSONObject.toJSONString(object)).build());
                        }
                    }
            );
        }

        return p1049Infos;
    }

    /**
     * 删除特定的数据项指定no
     *
     * @param signalId
     * @param type
     * @param no
     */
    @Transactional
    public void deleteP1049Infos(String signalId, int subJuncNo, String type, String no) {

        //移除内存数据项
        localSignalCacheService.deleteData(signalId, subJuncNo, type, no);

        //移除数据库数据项
        queryFactory.delete(QP1049Entity.p1049Entity).where(
                QP1049Entity.p1049Entity.signalId.eq(signalId)
                        .and(QP1049Entity.p1049Entity.type.eq(type))
                        .and(QP1049Entity.p1049Entity.no.eq(no))).execute();
    }


    /**
     * 删除特定的数据项指定
     *
     * @param signalId
     * @param type
     */
    @Transactional
    public void deleteP1049Infos(String signalId, int subJuncNo, String type) {
        //移除内存数据项
        localSignalCacheService.deleteData(signalId, subJuncNo, type);

        //移除数据库数据项
        queryFactory.delete(QP1049Entity.p1049Entity).where(
                QP1049Entity.p1049Entity.signalId.eq(signalId)
                        .and(QP1049Entity.p1049Entity.type.eq(type))
        ).execute();
    }

    /**
     * 数据处理
     *
     * @param p1049Infos
     */

    @Transactional
    public void p1049InfoProcessSync(P1049Infos p1049Infos) {
        p1049InfoProcess(p1049Infos);
    }

    @EventListener
    @Async(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    @Transactional
    public void p1049InfoProcess(P1049Infos p1049Infos) {
        log.trace("准备保存数据项-{}", p1049Infos);

        List<P1049Entity> controlerInfoOrg = p1049Infos.getControlerInfos();

        Map<String, List<P1049Entity>> stringListMap =
                controlerInfoOrg.stream().collect(Collectors.groupingBy(P1049Entity::getSignalId));

        Set<String> signalIds = stringListMap.keySet();

        //更新本地内存数据项
        localSignalCacheService.updateData(p1049Infos);

        for (String signalId : signalIds) {

            List<P1049Entity> p1049Entities = stringListMap.get(signalId);

            p1049Entities.stream().forEach(
                    p1049Entity -> {
                        p1049Entity.setUpdateDate(new Date());
                    }
            );

            //删除原先数据项
            Map<String, List<String>> typeDataMap = new HashMap<>();
            for (int i = 0; i < p1049Entities.size(); i++) {
                List<String> nos = typeDataMap.get(p1049Entities.get(i).getType());
                if (nos == null) {
                    nos = new ArrayList<>();
                    typeDataMap.put(p1049Entities.get(i).getType(), nos);
                    nos.add(p1049Entities.get(i).getNo());
                } else {
                    if (!nos.contains(p1049Entities.get(i).getNo())) {
                        nos.add(p1049Entities.get(i).getNo());
                    }
                }
            }
            //优化删除数据对象
            for (String type : typeDataMap.keySet()) {
                queryFactory.delete(QP1049Entity.p1049Entity).where(
                        QP1049Entity.p1049Entity.signalId.eq(signalId)
                                .and(QP1049Entity.p1049Entity.type.eq(type))
                                .and(QP1049Entity.p1049Entity.no.in(typeDataMap.get(type)))).execute();
            }

        }

        Iterable<P1049Entity> p1049EntityIterable = () -> controlerInfoOrg.iterator();
        repositoryDao.save(p1049EntityIterable, P1049Entity.class);
    }
}

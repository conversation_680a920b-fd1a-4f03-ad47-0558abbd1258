package com.les.its.open.bussiness.bean;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "linklog",
  indexes = {
        @Index(name = "controllerId_timeStamp_index_linklog", columnList = "controllerId,timeStampLog")
  })
@Data
public class LinkLogEntity {

    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    @Column(length = 50, nullable = false)
    private String controllerId;

    @Column(length = 50, nullable = false)
    private String ip;

    /**
     * 信号机与系统RTT时间
     */
    private int link;

    /**
     * UTC时间戳
     */
    private long timeStampLog;

}




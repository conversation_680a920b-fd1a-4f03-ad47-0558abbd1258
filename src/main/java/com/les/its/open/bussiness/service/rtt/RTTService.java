package com.les.its.open.bussiness.service.rtt;

import com.les.its.open.area.message.param.status.TabRTT;
import com.les.its.open.bussiness.bean.RttLogEntity;
import com.les.its.open.bussiness.bean.rtt.ConcurrentSlidingWindow;
import com.les.its.open.bussiness.process.RttLogProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * RTT数据项
 */
@Service
@Slf4j
public class RTTService {

    /**
     * 本地缓存记录信号机RTT
     */
    private final Map<String, ConcurrentSlidingWindow<TabRTT>> rttMap = new ConcurrentHashMap<>();

    @Autowired
    private RttLogProcess rttLogProcess;

    /**
     * 记录信号机数据项
     * @param controllerId
     * @param tabRTT
     */
    public void saveRtt(String controllerId, TabRTT tabRTT){
        ConcurrentSlidingWindow<TabRTT> slidingWindow = rttMap.get(controllerId);
        if(slidingWindow == null){
            slidingWindow = new ConcurrentSlidingWindow<>(60000 * 30);
            rttMap.put(controllerId, slidingWindow);
        }

        slidingWindow.add(tabRTT);


        //本地缓存数据存储
        {
            RttLogEntity rttLogEntity = new RttLogEntity();
            rttLogEntity.setControllerId(controllerId);
            rttLogEntity.setRtt(tabRTT.getRtt());
            rttLogEntity.setDiff(tabRTT.getDiff());
            rttLogEntity.setTimeStampLog(tabRTT.getTimeStamp());
            rttLogProcess.saveRttLog(rttLogEntity);
        }

    }

    /**
     * 获取窗口数据项
     * @param controllerId
     * @return
     */
    public List<TabRTT> getRtt(String controllerId){
        ConcurrentSlidingWindow<TabRTT> slidingWindow = rttMap.get(controllerId);
        if(slidingWindow == null){
            return List.of();
        }
        return slidingWindow.getSnapshot();
    }

}

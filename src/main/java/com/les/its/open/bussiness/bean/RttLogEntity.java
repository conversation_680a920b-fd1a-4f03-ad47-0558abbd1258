package com.les.its.open.bussiness.bean;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Table(name = "rttlog",
  indexes = {
        @Index(name = "controllerId_timeStamp_index_rttlog", columnList = "controllerId,timeStampLog")
  })
@Data
public class RttLogEntity {

    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    @Column(length = 50, nullable = false)
    private String controllerId;

    /**
     * 信号机与系统RTT时间
     */
    private long rtt;

    /**
     * 信号机与系统时间差
     */
    private long diff;

    /**
     * UTC时间戳
     */
    private long timeStampLog;

}




package com.les.its.open.bussiness.bean.rtt;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * 线程安全的并发时间滑动窗口
 * 使用ConcurrentLinkedDeque实现高并发场景
 */
public class ConcurrentSlidingWindow<T> {
    private final long windowSizeMillis;
    private final ConcurrentLinkedDeque<TimestampedData<T>> deque;
    private volatile long lastCleanupTime;
    private final long cleanupIntervalMillis;

    public ConcurrentSlidingWindow(long windowSizeMillis) {
        this(windowSizeMillis, windowSizeMillis / 10); // 默认清理间隔为窗口大小的1/10
    }

    public ConcurrentSlidingWindow(long windowSizeMillis, long cleanupIntervalMillis) {
        this.windowSizeMillis = windowSizeMillis;
        this.cleanupIntervalMillis = cleanupIntervalMillis;
        this.deque = new ConcurrentLinkedDeque<>();
        this.lastCleanupTime = System.currentTimeMillis();
    }

    /**
     * 添加数据点
     */
    public void add(T data) {
        long currentTime = System.currentTimeMillis();
        deque.addLast(new TimestampedData<>(data, currentTime));

        // 定期清理过期数据
        if (currentTime - lastCleanupTime > cleanupIntervalMillis) {
            cleanup(currentTime);
            lastCleanupTime = currentTime;
        }
    }

    /**
     * 获取窗口内数据快照
     */
    public List<T> getSnapshot() {
        long currentTime = System.currentTimeMillis();
        long cutoffTime = currentTime - windowSizeMillis;

        List<T> result = new ArrayList<>();
        for (TimestampedData<T> item : deque) {
            if (item.timestamp >= cutoffTime) {
                result.add(item.data);
            }
        }
        return result;
    }

    /**
     * 获取窗口大小
     */
    public int size() {
        return (int) deque.stream()
                .filter(item -> item.timestamp >= System.currentTimeMillis() - windowSizeMillis)
                .count();
    }

    private void cleanup(long currentTime) {
        long cutoffTime = currentTime - windowSizeMillis;
        while (!deque.isEmpty() && deque.peekFirst().timestamp < cutoffTime) {
            deque.removeFirst();
        }
    }
}
package com.les.its.open.netty.link;



import com.les.its.open.protocol.common.constdata.ProtocolType;
import com.les.its.open.utils.ConstValue;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName: ClientType
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/16 13:24
 */
public interface ClientType {
    @Data
    @AllArgsConstructor
    class IpPortProtocol {
        private String ip;
        private int port;
        private String protocol;

        private int localPort;

        /**
         * @return
         */
        public Optional<String> getKey()
        {
            try {

                if(ip.matches(ConstValue.IP_PATTERN)) {
                    ProtocolType protocolType = ProtocolType.valueOf(protocol);
                    return Optional.of(protocolType.value() + "-" + ip + "-" + port);
                }else{
                    //串口类型数据项
                    ProtocolType protocolType = ProtocolType.valueOf(protocol);
                    return Optional.of(protocolType.value() + "-" + ip);
                }
            }catch (IllegalArgumentException e)
            {
                return Optional.empty();
            }
        }
    }

    /**
     * 获取客户端类型的参数
     * @return
     */
    List<IpPortProtocol> getClientList();
}

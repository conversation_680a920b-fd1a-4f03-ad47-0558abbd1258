package com.les.its.open.netty.stop;


import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.task.AsyncTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @ClassName: ReconnectEvent
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/7 15:13
 */
@Slf4j
@Component
public class StopEventProcess {

    @Autowired
    private AsyncTaskService asyncTaskService;

    /**
     * 接收重连数据事件
     * @param stopEvent
     */
    @EventListener
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void stopEventProcess(StopEvent stopEvent) {
        asyncTaskService.executeStopEvent(stopEvent.getStopAble());
    }
}

package com.les.its.open.netty.dbprocess;



import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.netty.link.AbstractLink;
import com.les.its.open.netty.link.LinkManager;
import com.les.its.open.task.AsyncTaskService;
import com.les.its.open.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @ClassName: ReconnectEvent
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/7 15:13
 */
@Slf4j
@Component
public class StartStopMessageEventProcess {

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private LinkManager linkManager;

    /**
     * 接收重连数据事件
     * @param startStopMessageEvent
     */
    @EventListener
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void startStopEventProcess(StartStopMessageEvent startStopMessageEvent) {
        if(startStopMessageEvent.isStart())
        {/*新建链路*/
            if(startStopMessageEvent.getIpPortProtocolOptional().isPresent())
            {
                try {
                    if(startStopMessageEvent.getIpPortProtocolOptional().get().getIp().matches(ConstValue.IP_PATTERN)){
                            asyncTaskService.executeCommtAsyncTask(startStopMessageEvent.getIpPortProtocolOptional().get().getIp(),
                                    startStopMessageEvent.getIpPortProtocolOptional().get().getProtocol());
                    }else{
                          asyncTaskService.executeClientAsyncTask(startStopMessageEvent.getIpPortProtocolOptional().get());
                    }
                } catch (Exception e) {
                   log.error("新建客户端启动异常", e);
                }
            }else if(startStopMessageEvent.getPortPotocolOptional().isPresent()){
                try {
                    asyncTaskService.executeServerAsyncTask(startStopMessageEvent.getPortPotocolOptional().get());
                } catch (Exception e) {
                    log.error("新建服务端启动异常", e);
                }
            }
        }else {
        /**移除*/
            Optional<AbstractLink> abstractLink = Optional.empty();
            if(startStopMessageEvent.getIpPortProtocolOptional().isPresent())
            {
                abstractLink = linkManager.getClient(startStopMessageEvent.getIpPortProtocolOptional().get());

            }else if(startStopMessageEvent.getPortPotocolOptional().isPresent()){
                abstractLink = linkManager.getServer(startStopMessageEvent.getPortPotocolOptional().get());
            }

            if(abstractLink.isEmpty())
            {
                log.error("没有找到-{}的连接器,无法停止", startStopMessageEvent.getIpPortProtocolOptional().get());
                return;
            }
            asyncTaskService.executeStopEvent(abstractLink.get());
            linkManager.rmLink(abstractLink.get());
        }
    }
}

package com.les.its.open.netty;

import com.les.its.open.event.MessagePublisher;
import com.les.its.open.netty.link.AbstractLink;
import com.les.its.open.protocol.common.Protocol;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
@Data
@EqualsAndHashCode(callSuper=false)
public class NettyUdp extends AbstractLink {
    /**
     * netty监听的端口
     */
    private int port;

    /**
     * 端口对应的解析协议
     */
    private Optional<Protocol> protocol;

    // 配置服务端的NIO线程组
    private EventLoopGroup bossGroup = new NioEventLoopGroup();

    public NettyUdp(int port, String protocol,
                    MessagePublisher messagePublisher) {
        this.port = port;
        this.protocol = Protocol.buildProtocol(protocol, messagePublisher, false);
    }

    /**
     * 绑定端口开始数据的监听
     * @throws Exception
     */
    public void bind() throws Exception {
        //检查协议是否正常
        if(!protocol.isPresent())
        {
            log.error("not able to bind port" + port + ",because protocol is null");
            return;
        }

        Bootstrap b =  new Bootstrap();
        b.group(bossGroup).channel(NioDatagramChannel.class)
                .handler(protocol.get().getProtocolChannelInitializer().getChannelInitializer());
        // 绑定端口，同步等待成功
        ChannelFuture f = b.bind(port).sync();
        log.info("端口绑定成功: " + port + ",协议=" + protocol.get().getProtocolType());

        // 等待服务端监听端口关闭
        f.channel().closeFuture().addListener((ChannelFutureListener) future -> {
            bossGroup.shutdownGracefully();
        });
    }

    @Override
    public void stop() {
        bossGroup.shutdownGracefully();
    }

    @Override
    public String getKey() {
        return this.getProtocol().get().getProtocolType().value() + "-" + port ;
    }
}

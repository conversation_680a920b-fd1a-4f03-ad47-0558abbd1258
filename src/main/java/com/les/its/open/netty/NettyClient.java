package com.les.its.open.netty;


import com.les.its.open.event.MessagePublisher;
import com.les.its.open.netty.link.AbstractLink;
import com.les.its.open.netty.reconnect.ReconnectAble;
import com.les.its.open.netty.reconnect.ReconnectEvent;
import com.les.its.open.protocol.common.Protocol;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;
import java.util.Optional;
import java.util.Random;

/**
 * @ClassName: NettyClient
 * @Description:
 * @Author: king
 * @CreateDate: 2018/12/27 20:16
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper=false)
public class NettyClient extends AbstractLink implements ReconnectAble {

    /**
     * 源端IP
     */
    private String ip;

    /**
     * 远端监听的端口
     */
    private int port;

    /**
     * 本地端口
     */
    private int localPort;

    /**
     * 端口对应的解析协议
     */
    private Optional<Protocol> protocol;

    /**
     * 客户端所在的EventLoopGroup，设置默认线程个数为1
     */
    private EventLoopGroup group = new NioEventLoopGroup(1);

    /**
     * 发起异步连接操作
     */
    private ChannelFuture future;

    private MessagePublisher messagePublisher;

    /**
     * 标记当前是否已经停止
     */
    private volatile boolean stopFlag = false;

    /**
     * 重连随机数
     */
    private static Random reconnectRandom = new Random(System.currentTimeMillis());


    public NettyClient(String ip, int port, int localPort, String protocol,
                       MessagePublisher messagePublisher) {
        this.ip = ip;
        this.port = port;
        this.localPort = localPort;
        this.protocol = Protocol.buildProtocol(protocol, messagePublisher, false);
        this.messagePublisher = messagePublisher;
    }


    public void connect() {

        if (null == this.ip || 0 == port) {
            log.error("error ip and port " + " " + this.ip + " " + this.port);
            return;
        }

        connect(this.port, this.ip, this.localPort);
    }

    /**
     * 防止出现大规模重连
     * 重连间隔在3 - 20 随机
     *
     * @return
     */
    private int getRandomReconnectTime() {
        return (3 + reconnectRandom.nextInt(17)) * 1000;
    }

    private void connect(int port, String host, int localPort) {
        if (stopFlag) {
            log.warn("客户端 {}:{} 已经停止连接", ip, port);
            return;
        }

        try {
            NettyClient nettyClient = this;
            Bootstrap b = new Bootstrap();

            // 配置客户端NIO线程组
            b.group(group).channel(NioSocketChannel.class)
                    .option(ChannelOption.TCP_NODELAY, true)
                    .option(ChannelOption.SO_REUSEADDR, true)
                    .handler(protocol.get().getProtocolChannelInitializer().getChannelInitializer());

            if(localPort != 0) {
                // 发起异步连接操作
                future = b.connect(
                        new InetSocketAddress(host, port), new InetSocketAddress(localPort)).sync();
            }else{
                // 发起异步连接操作
                future = b.connect(
                        new InetSocketAddress(host, port)).sync();
            }


            // 当对应的channel关闭的时候，就会返回对应的channel。
            // Returns the ChannelFuture which will be notified when this channel is closed. This method always returns the same future instance.
            future.channel().closeFuture().addListener(new ChannelFutureListener() {
                @Override
                public void operationComplete(ChannelFuture future) throws Exception {

                    if (stopFlag) {
                        log.warn("客户端 {}:{} 已经停止连接", ip, port);
                        return;
                    }

                    /**关闭之后进行重新连接*/
                    int delay = getRandomReconnectTime();
                    log.warn("客户端 {}:{} {}ms后尝试重新连接", ip, port, delay);
                    messagePublisher.publishDelayMessage(delay, ReconnectEvent.builder().reconnectAble(nettyClient).build());
                }
            });
        } catch (Exception e) {
            // 所有资源释放完成之后，清空资源，再次发起重连操作
            int delay = getRandomReconnectTime();
            log.warn("出现异常客户端 {}:{} {}ms尝试重新连接", ip, port, delay);
            messagePublisher.publishDelayMessage(delay, ReconnectEvent.builder().reconnectAble(this).build());
        }
    }

    @Override
    public void reconnect() {
        connect();
    }

    @Override
    public void stop() {
        stopFlag = true;
        group.shutdownGracefully();
    }

    @Override
    public String getKey() {
        return this.getProtocol().get().getProtocolType().value() + "-" + ip + "-" + port;
    }
}

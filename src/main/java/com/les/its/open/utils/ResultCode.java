package com.les.its.open.utils;

import lombok.Data;
import lombok.Getter;

/**
 * The enum Result code.
 *
 * @ClassName: ResultCode
 * @Description: 异常返回code定义
 * @Author: king
 * @CreateDate: 2018 /12/3 10:13
 */
@Getter
public enum ResultCode {

    /**
     * 成功
     */
    SUCCESS(0, "成功"),
    /**
     * 失败
     */
    FAILED(-1, "失败"),


    NOT_SUPPORT_LOOK_TYPE(-1000, "不支持的调看加载类型"),
    FAILED_LOOK(-1002, "调看失败"),

    FAILED_LOAD(-1502, "加载失败"),


    FAILED_CMD(-2000, "控制命令异常"),
    FAILED_SET_STAGE_PARAM(-2001, "锁定阶段参数异常"),

    /**
     * Channelholder no equipment connectted result code.
     * 数据发送错误code
     */
    CHANNELHOLDER_NO_EQUIPMENT_CONNECTTED(-2000, "没有对应的IP设备"),

    FAILED_NO_CONTROLLER(-3000, "没有对应的信号机设备")

    ;

    private Integer code;
    private String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * Code integer.
     *
     * @return the integer
     */
    public Integer code() {
        return this.code;
    }

    /**
     * Message string.
     *
     * @return the string
     */
    public String message() {
        return this.message;
    }
}

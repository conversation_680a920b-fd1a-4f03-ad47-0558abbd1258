package com.les.its.open.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
public class PingUtil {

    public static boolean pingResult(String hostname) {
        return pingResult(hostname, 1000);
    }

    /***
     *  ping操作
     * @param hostname
     * @param timeout in milliseconds
     * @return
     */
    public static boolean pingResult(String hostname, Integer timeout) {
        boolean result = false;
        try {
            InetAddress address = InetAddress.getByName(hostname);
            boolean flag = address.isReachable(timeout);
            if (flag) {
                log.info("ping结果:the address-{} is reachable.", hostname);
            } else {
                log.info("ping结果:the address-{} is not reachable.", hostname);
            }
        } catch (UnknownHostException e) {
            log.error("ping Exception e ", e);
        } catch (IOException e) {
            log.error("ping Exception e ", e);
        }
        return result;
    }
}

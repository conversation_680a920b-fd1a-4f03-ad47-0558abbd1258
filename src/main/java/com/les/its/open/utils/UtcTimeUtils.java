package com.les.its.open.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class UtcTimeUtils {

    public static LocalDateTime convert2LocalDateTime(long utcTimestampMillis) {
        // 创建Instant对象
        Instant instant = Instant.ofEpochMilli(utcTimestampMillis);

        // 转换为LocalDateTime，使用系统默认时区
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return localDateTime;
    }

    /**
     * 将LocalDateTime转换为UTC时间戳（毫秒）
     * @param localDateTime 本地日期时间
     * @return UTC时间戳（毫秒）
     */
    public static long convert2UtcTimestamp(LocalDateTime localDateTime) {
        // 转换为Instant，使用系统默认时区
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();

        // 返回UTC时间戳（毫秒）
        return instant.toEpochMilli();
    }

}

package com.les.its.open.utils;

/**
 * 电流值转换工具类
 * 实现电流值与字节表示之间的转换
 */
public class CurrentValueConverter {

    // 常量定义
    private static final int RANGE1_MAX = 100;  // 第一个范围的最大值
    private static final int RANGE2_MIN = 101;  // 第二个范围的最小值
    private static final int RANGE2_MAX = 200;  // 第二个范围的最大值
    private static final int RANGE3_MIN = 201;  // 第三个范围的最小值
    private static final int RANGE3_MAX = 250;  // 第三个范围的最大值
    private static final int RANGE4_MIN = 251;  // 第四个范围的最小值
    private static final int RANGE4_MAX = 255;  // 第四个范围的最大值
    
    private static final int RANGE1_BASE = 0;    // 第一个范围的基础值
    private static final int RANGE2_BASE = 100;  // 第二个范围的基础值
    private static final int RANGE3_BASE = 600;  // 第三个范围的基础值
    private static final int RANGE4_BASE = 2600; // 第四个范围的基础值
    
    private static final int RANGE2_PRECISION = 5;   // 第二个范围的精度
    private static final int RANGE3_PRECISION = 40;  // 第三个范围的精度
    private static final int RANGE4_PRECISION = 80;  // 第四个范围的精度

    /**
     * 将字节表示转换为实际电流值(mA)
     * 
     * @param byteValue 字节表示值(0-255)
     * @return 实际电流值(mA)
     * @throws IllegalArgumentException 如果输入值超出有效范围
     */
    public static int byteToCurrentValue(int byteValue) {
        if (byteValue < 0 || byteValue > 255) {
            throw new IllegalArgumentException("字节值必须在0-255范围内");
        }
        
        // 范围1: 1-100 直接表示 1mA-100mA
        if (byteValue <= RANGE1_MAX) {
            return byteValue;
        }
        // 范围2: 101-200 表示 105mA-600mA，精度为5
        else if (byteValue <= RANGE2_MAX) {
            return RANGE2_BASE + (byteValue - RANGE2_MIN + 1) * RANGE2_PRECISION;
        }
        // 范围3: 201-250 表示 640mA-2600mA，精度为40
        else if (byteValue <= RANGE3_MAX) {
            return RANGE3_BASE + (byteValue - RANGE3_MIN + 1) * RANGE3_PRECISION;
        }
        // 范围4: 251-255 表示 2680mA-3000mA，精度为80
        else {
            return RANGE4_BASE + (byteValue - RANGE4_MIN + 1) * RANGE4_PRECISION;
        }
    }
    
    /**
     * 将实际电流值(mA)转换为字节表示
     * 
     * @param currentValue 实际电流值(mA)
     * @return 字节表示值(0-255)
     * @throws IllegalArgumentException 如果输入值超出有效范围
     */
    public static int currentValueToByte(int currentValue) {
        if (currentValue < 0 || currentValue > 3000) {
            throw new IllegalArgumentException("电流值必须在0-3000mA范围内");
        }
        
        // 范围1: 0-100mA 直接表示为 0-100
        if (currentValue <= RANGE2_BASE) {
            return currentValue;
        }
        // 范围2: 105mA-600mA 表示为 101-200
        else if (currentValue <= RANGE3_BASE) {
            // 计算与基础值的差距
            int diff = currentValue - RANGE2_BASE;
            // 如果不是精度的整数倍，向下取整
            int steps = (diff + RANGE2_PRECISION - 1) / RANGE2_PRECISION; // 向上取整
            return RANGE2_MIN + steps - 1;
        }
        // 范围3: 640mA-2600mA 表示为 201-250
        else if (currentValue <= RANGE4_BASE) {
            int diff = currentValue - RANGE3_BASE;
            int steps = (diff + RANGE3_PRECISION - 1) / RANGE3_PRECISION; // 向上取整
            return RANGE3_MIN + steps - 1;
        }
        // 范围4: 2680mA-3000mA 表示为 251-255
        else {
            int diff = currentValue - RANGE4_BASE;
            int steps = (diff + RANGE4_PRECISION - 1) / RANGE4_PRECISION; // 向上取整
            return RANGE4_MIN + steps - 1;
        }
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试字节到电流值的转换
        System.out.println("字节值 -> 电流值(mA)");
        System.out.println("1 -> " + byteToCurrentValue(1));     // 1mA
        System.out.println("100 -> " + byteToCurrentValue(100)); // 100mA
        System.out.println("101 -> " + byteToCurrentValue(101)); // 105mA
        System.out.println("200 -> " + byteToCurrentValue(200)); // 600mA
        System.out.println("201 -> " + byteToCurrentValue(201)); // 640mA
        System.out.println("250 -> " + byteToCurrentValue(250)); // 2600mA
        System.out.println("251 -> " + byteToCurrentValue(251)); // 2680mA
        System.out.println("255 -> " + byteToCurrentValue(255)); // 3000mA
        
        System.out.println("\n电流值(mA) -> 字节值");
        System.out.println("1 -> " + currentValueToByte(1));     // 1
        System.out.println("100 -> " + currentValueToByte(100)); // 100
        System.out.println("105 -> " + currentValueToByte(105)); // 101
        System.out.println("600 -> " + currentValueToByte(600)); // 200
        System.out.println("640 -> " + currentValueToByte(640)); // 201
        System.out.println("2600 -> " + currentValueToByte(2600)); // 250
        System.out.println("2680 -> " + currentValueToByte(2680)); // 251
        System.out.println("3000 -> " + currentValueToByte(3000)); // 255
    }
}
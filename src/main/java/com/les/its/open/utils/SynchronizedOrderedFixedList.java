package com.les.its.open.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 基于读写锁的有序List实现
 * @param <T>
 */
public class SynchronizedOrderedFixedList<T extends Comparable<T>> {
    private final List<T> list = new ArrayList<>();
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private final int maxSize;

    public SynchronizedOrderedFixedList(int maxSize) {
        this.maxSize = maxSize;
    }

    public void add(T item) {
        lock.writeLock().lock();
        try {
            // 使用二分查找找到插入位置
            int index = Collections.binarySearch(list, item);
            if (index < 0) {
                index = -(index + 1);
            }
            list.add(index, item);

            // 保持最大的maxSize个元素
            while (list.size() > maxSize) {
                list.remove(0); // 移除最小的元素
            }
        } finally {
            lock.writeLock().unlock();
        }
    }

    public List<T> getAll() {
        lock.readLock().lock();
        try {
            return new ArrayList<>(list);
        } finally {
            lock.readLock().unlock();
        }
    }

    public int size() {
        lock.readLock().lock();
        try {
            return list.size();
        } finally {
            lock.readLock().unlock();
        }
    }

    public void clear() {
        lock.writeLock().lock();
        try {
            list.clear();
        } finally {
            lock.writeLock().unlock();
        }
    }
}


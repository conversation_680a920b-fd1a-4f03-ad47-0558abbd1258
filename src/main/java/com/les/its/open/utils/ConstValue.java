package com.les.its.open.utils;

/**
 * @ClassName: ConstValue
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/5 10:12
 */
public class ConstValue {

    public static final String DEFAULT_ID = "0";

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 由于报文存在接收和发送的报文id是一样的报文，此处作特殊处理，接收的报文按照原先id处理
     * 发送的报文，内部使用添加区分号
     */
    public static final short MESSAGE_IN_OUT_SPILTER_FLAG = 10000;

    /**
     * IP格式
     */
    public static final String IP_PATTERN = "(2(5[0-5]{1}|[0-4]\\d{1})|[0-1]?\\d{1,2})(\\.(2(5[0-5]{1}|[0-4]\\d{1})|[0-1]?\\d{1,2})){3}";


    /**
     * 异常的阶段编号
     */
    public static final String ErrorStageNo = "65535";

}

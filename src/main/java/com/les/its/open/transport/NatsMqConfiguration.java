package com.les.its.open.transport;


import com.les.its.open.config.GlobalConfigure;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 区域机数据配置
 */
@Configuration
public class NatsMqConfiguration {

    @Bean(name = "signalExchange2")
    public TopicExchange signalExchange2() {
        return new TopicExchange(GlobalConfigure.exchangeName2, false, false);
    }


    @Bean(name = "cmdQueue2")
    public Queue cmdQueue2() {
        return new Queue(GlobalConfigure.cmdQueue2, true, false, true);
    }


    /**
     * 绑定当前所在区域的控制命令数据项
     *
     * @param cmdQueue2
     * @param signalExchange2
     * @return
     */
    @Bean
    Binding bindingExchangeMessages(Queue cmdQueue2, TopicExchange signalExchange2) {
        return BindingBuilder.bind(cmdQueue2).to(signalExchange2)
                .with(GlobalConfigure.routingKeyPrefixListen2 + "*." + GlobalConfigure.areaNo + ".*");
    }

}

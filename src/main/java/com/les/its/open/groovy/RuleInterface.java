package com.les.its.open.groovy;

import java.util.Optional;

/**
 * @Author: WJ
 * @Description: 根据1049路口ID生成莱斯信号机ID，路口ID则后续+1
 * @Date: create in 2023/4/20 16:02
 */
public interface RuleInterface {

    /**
     * 获取标准的信号机编号比如 320100YL01214
     * 320100 -- 前六位表示城市编码
     * YL -- 中间表示品牌
     * 01 -- 区域号
     * 214 -- 路口号
     *
     * @param crossId1049
     * @return
     */
    Optional<String> change1049CrossToLesSignalId(String crossId1049);

    /**
     * 获取设备厂家名称
     *
     * @param crossId10419
     * @return
     */
    Optional<String> getManufacturer(String crossId10419);

    /**
     * 获取officeID数据项量
     *
     * @param crossId10419
     * @return
     */
    Optional<String> getOfficeId(String crossId10419);

    /**
     * 获取信号机类型数据
     * 比如: HT2000 ,HT001 等
     *
     * @param crossId10419
     * @return
     */
    Optional<String> getSignalType(String crossId10419);
}

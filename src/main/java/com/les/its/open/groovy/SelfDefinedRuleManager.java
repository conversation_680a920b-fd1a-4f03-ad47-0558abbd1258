package com.les.its.open.groovy;

import com.les.its.open.config.GlobalConfigure;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FilenameFilter;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: SelfDefinedMessageManager
 * @Description: 自定义的报文类文件管理
 * 1）读取指定位置的配置文件，加载
 * @Author: king
 * @CreateDate: 2019/1/9 14:50
 */
@Component
@Slf4j
@Data
public class SelfDefinedRuleManager {

    /**
     * 存储用户自定义的报文message解析Class类
     */
    private final ConcurrentHashMap<String, ParseInfo> userDefinedMessages = new ConcurrentHashMap<>();


    /**
     * 线程运行标记
     */
    private static volatile boolean bRunning = true;

    /**
     * 读取的配置文件地址数组
     */
    private String[] aDirectories;

    /**
     * 检测配置是否变化的时间间隔
     */
    private int pollingIntervalSeconds = 30;

    /**
     * 检测线程
     */
    protected Thread poller = null;

    /**
     * 存储自定义报文上次文件修改时间
     */
    private final ConcurrentHashMap<String, Long> filterClassLastModified = new ConcurrentHashMap<String, Long>();

    /**
     * 编译报文定义的编译器
     */
    private IDynamicCodeCompiler COMPILER = new GroovyCompiler();

    /**
     * 文件过滤器
     */
    private static FilenameFilter FILENAME_FILTER = new RuleFileFilter();


    /**
     * 初始化加载定义类
     */
    public void loadData() {
        aDirectories = new String[]{GlobalConfigure.USER_DEFINED_RULE_FOLDER};

        try {
            manageFiles();
        } catch (Exception e) {
            e.printStackTrace();
        }
        startPoller();

    }


    /**
     * 开始检测报文定义是否变化
     */
    protected void startPoller() {
        if (poller == null) {
            poller = new Thread("GroovyFilterFileManagerPoller") {
                @Override
                public void run() {
                    while (bRunning) {
                        try {
                            sleep(pollingIntervalSeconds * 1000);
                            manageFiles();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            };
            poller.start();
        }
    }


    /**
     * From a file this will read the User defined source code, compile it, and add it to the list of current userDefinedMessages
     * a true response means that it was successful.
     *
     * @param file
     * @return true if the filter in file successfully read, compiled, verified and added to Zuul
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public boolean putUserDefinedMessage(File file) throws Exception {
        String sName = file.getAbsolutePath() + file.getName();
        if (filterClassLastModified.get(sName) != null && (file.lastModified() != filterClassLastModified.get(sName))) {
            log.debug("重新加载报文消息定义文件 " + sName);
            userDefinedMessages.remove(sName);
        }
        ParseInfo parseInfo = userDefinedMessages.get(sName);
        if (parseInfo == null) {
            log.debug("file name == {}", file.getName());
            String fileName = file.getName().replace(RuleFileFilter.USER_DEFINED_RULE_TAIL, "");
            String[] strings = fileName.split("-");
            if (strings.length != 2) {
                log.error("规则文件名没有按照规则进行设置，无法进行解析，示例【2-RuleMessage.java】");
                return false;
            }

            String ruleID = strings[0];

            Class clazz = COMPILER.compile(file);
            if (!Modifier.isAbstract(clazz.getModifiers())) {

                parseInfo = ParseInfo.builder()
                        .ruleID(ruleID)
                        .clazz(clazz).build();

                userDefinedMessages.put(file.getAbsolutePath() + file.getName(), parseInfo);
                log.info("重新加载规则定义文件[{}] 从文件[{}] ，解析文件={}", file.getName(),
                        file.getAbsolutePath() + " " + file.getName(), parseInfo);
                filterClassLastModified.put(sName, file.lastModified());
                //移除原先的规则class,下次获取最新的规则引擎数据
                ruleInterfaceMap.remove(ruleID);
                return true;
            } else {
                log.error("你定义的规则是抽象类型代码！");
            }

        } else {
            log.debug("规则定义文件未变化，不需要重新加载文件[{}]", sName);
        }

        return false;
    }


    /**
     * puts files into the FilterLoader. The FilterLoader will only addd new or
     * changed userDefinedMessages
     *
     * @param aFiles a List<File>
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    private void processGroovyFiles(List<File> aFiles)
            throws Exception, InstantiationException, IllegalAccessException {
        for (File file : aFiles) {
            if (file.getName().endsWith(RuleFileFilter.USER_DEFINED_RULE_TAIL)) {
                putUserDefinedMessage(file);
            }
        }
    }

    protected void manageFiles() throws Exception, IllegalAccessException, InstantiationException {
        List<File> aFiles = getFiles();
        processGroovyFiles(aFiles);
    }

    /**
     * Returns the directory File for a path. A Runtime Exception is thrown if
     * the directory is in valid
     *
     * @param sPath
     * @return a File representing the directory path
     */
    public File getDirectory(String sPath) {
        File directory = new File(sPath);
        if (!directory.isDirectory()) {
            URL resource = SelfDefinedRuleManager.class.getClassLoader().getResource(sPath);
            try {
                directory = new File(resource.toURI());
            } catch (Exception e) {
                log.error("Error accessing directory in classloader. path=" + sPath, e);
            }
            if (!directory.isDirectory()) {
                throw new RuntimeException(directory.getAbsolutePath() + " is not a valid directory");
            }
        }
        return directory;
    }


    /**
     * 根据过滤条件获取所有的文件
     *
     * @return
     */
    public List<File> getFiles() {
        List<File> list = new ArrayList<File>();
        for (String sDirectory : aDirectories) {
            if (sDirectory != null) {
                File directory = getDirectory(sDirectory);
                File[] aFiles = directory.listFiles(FILENAME_FILTER);
                if (aFiles != null) {
                    list.addAll(Arrays.asList(aFiles));
                }
            }
        }
        return list;
    }


    /**
     * 根据自定义协议号以及报文ID查找报文定义
     *
     * @param ruleID
     * @return
     */
    public Optional<ParseInfo> getRule(String ruleID) {
        return userDefinedMessages.values().stream().filter(
                parseInfo ->
                {
                    return parseInfo.getRuleID().equalsIgnoreCase(ruleID);
                }
        ).findAny();
    }

    /**
     * 保存生成的规则引擎数据
     */
    private Map<String, RuleInterface> ruleInterfaceMap = new ConcurrentHashMap<>();

    /**
     * 根据自定义规则号以及报文ID查找规则定义
     *
     * @param ruleID
     * @return
     */
    public Optional<RuleInterface> getRuleClass(String ruleID) {

        RuleInterface ruleInterface = ruleInterfaceMap.get(ruleID);
        if (ruleInterface != null) {
            return Optional.of(ruleInterface);
        }

        Optional<ParseInfo> parseInfoOp = userDefinedMessages.values().stream().filter(
                parseInfo ->
                        parseInfo.getRuleID().equalsIgnoreCase(ruleID)
        ).findAny();
        if (parseInfoOp.isPresent()) {
            Object ruleObject = ReflectUtils.newInstance(parseInfoOp.get().getClazz());
            if (ruleObject instanceof RuleInterface) {
                RuleInterface ruleInterfaceNew = (RuleInterface) ruleObject;
                ruleInterfaceMap.put(ruleID, ruleInterfaceNew);
                return Optional.of(ruleInterfaceNew);
            }
        }

        return Optional.empty();
    }
}

package com.les.its.open.groovy;

import lombok.Builder;
import lombok.Data;

/**
 * @ClassName: ParseInfo
 * @Description:
 * @Author: king
 * @CreateDate: 2019/1/17 9:22
 */
@Data
@Builder
public class ParseInfo {

    private String ruleID;

    private String definedStr;

    private Class clazz;


    public static String getKey(ParseInfo parseInfo) {
        return parseInfo.getRuleID();
    }
}

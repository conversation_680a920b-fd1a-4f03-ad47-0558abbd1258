package com.les.its.open.event.AckManager;

import java.util.concurrent.Executor;

/**
 * @ClassName: InvokeCallback
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:06
 */
public interface InvokeCallback {

    /**
     * Response received.
     *
     * @param result
     */
    void onResponse(final Object result);

    /**
     * Exception caught.
     *
     * @param e
     */
    void onException(final Throwable e);

    /**
     * User defined executor.
     *
     * @return
     */
    Executor getExecutor();

}


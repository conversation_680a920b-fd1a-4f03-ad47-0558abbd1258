package com.les.its.open.event.AckManager.response;


import com.les.its.open.protocol.common.InterProtocol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: ResponseBase
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:22
 */
@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseMessage {
    /**
     * 请求的uuid
     */
    private String requestUuid;

    /**
     * 报文应答状态
     */
    private ResponseStatus responseStatus;

    /**
     * 报文应答的时间
     */
    private long responseTimeMillis;

    /**
     * 请求的数据项
     */
    private InterProtocol requestInterProtocol;

    /**
     * 应答的数据项数据
     */
    private Object responseObject;
}

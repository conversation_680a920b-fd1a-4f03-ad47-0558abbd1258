package com.les.its.open.event.AckManager.response;


import com.les.its.open.protocol.common.InterProtocol;
import lombok.ToString;


/**
 * @ClassName: CommandFactory
 * @Description:
 * @Author: king
 * @CreateDate: 2019/6/17 14:25
 */

@ToString
public class CommandFactory {

    /**
     * 生成错误类报文报文
     *
     * @param responseStatus
     * @param interProtocol
     * @return
     */
    public static ResponseMessage createMessageResponse(ResponseStatus responseStatus, InterProtocol interProtocol) {
        ResponseMessage responseCommand = new ResponseMessage();
        responseCommand.setRequestUuid(interProtocol.getOuterUuid());
        responseCommand.setResponseStatus(responseStatus);
        responseCommand.setResponseTimeMillis(System.currentTimeMillis());
        responseCommand.setRequestInterProtocol(interProtocol);
        return responseCommand;
    }


}




package com.les.its.open.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.stereotype.Component;

/**
 * @ClassName: MyApplicationEventPublisher
 * @Description: 使用ApplicationEventPublisher进行事件的发布
 * @Author: king
 * @CreateDate: 2018/11/26 13:35
 */
@Component
@Slf4j
public class MessagePublisher extends AbstractSummary {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 发送数据到总线
     *
     * @param object
     */
    public void publishMessage(Object object) {
        try {
            applicationEventPublisher.publishEvent(object);
            Long current = getAtomicSuccessLong().incrementAndGet();
            log.trace("publish message to event bus index {} - {}", current, object);
        } catch (TaskRejectedException task) {
            Long current = getAtomicErrorLong().incrementAndGet();
            log.error("reject Task, detail reject/total [{}/{}] -{}", current, current + getAtomicSuccessLong().get(), object);
        }
    }

    /**
     * 发送延迟数据
     *
     * @param delayMilSeconds
     * @param object
     */
    public void publishDelayMessage(int delayMilSeconds, Object object) {
        publishMessage(new DelayMessageProcess.DelayObject(object, delayMilSeconds));
    }

}

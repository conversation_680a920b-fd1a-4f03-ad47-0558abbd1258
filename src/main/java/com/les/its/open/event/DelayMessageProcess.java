package com.les.its.open.event;


import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.utils.TimerHolder;
import io.netty.util.HashedWheelTimer;
import io.netty.util.TimerTask;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: DelayMessagePublish
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/17 11:36
 */
@Component
@Slf4j
@Data
public class DelayMessageProcess {

    @Autowired
    private MessagePublisher messagePublisher;

    /**
     * 存储延迟队列数据项
     */
    private final int MAX_DELAY_SIZE = 1000;

    /**
     * 使用netty实现的时间轮延迟处理
     * DefaultThreadFactory 也是netty中实现的
     */
    private final HashedWheelTimer timer = TimerHolder.getTimer();


    /**
     * 新建延迟任务,延迟任务执行时间不可过长，否则影响延迟队列
     *
     * @param object
     * @param messagePublisher
     * @return
     */
    private TimerTask createDelayTimerTask(Object object, MessagePublisher messagePublisher) {
        return timeout -> {
            log.info("时间到了，执行操作-{} ", object);
            messagePublisher.publishMessage(object);
        };
    }

    /**
     * 接收延迟队列数据项
     *
     * @param delayObject
     */
    @EventListener
    @Async(GlobalConfigure.MESSAGE_ASYC_EXECUTOR)
    public void processMessage(DelayObject delayObject) {
        try {
            timer.newTimeout(createDelayTimerTask(delayObject.getObject(), messagePublisher),
                    delayObject.getDelayMilSeconds(), TimeUnit.MILLISECONDS);
            log.debug("添加到延迟队列数据项执行时间-[{}], 当前延迟队列数据个数-[{}]", delayObject, timer.pendingTimeouts());
            if (timer.pendingTimeouts() > MAX_DELAY_SIZE * 0.8) {
                log.warn("延迟队列数据已经达到80%={}", MAX_DELAY_SIZE * 0.8);
            }
        } catch (RejectedExecutionException e) {
            log.error("延迟队列数据项已经超过{}, 任务被拒绝接受-{}", MAX_DELAY_SIZE, e);
        }
    }

    @ToString
    public static class DelayObject {
        /**
         * 执行的实际时间
         */
        @Getter
        private int delayMilSeconds;

        /**
         * 实际数据项
         */
        @Getter
        private Object object;

        public DelayObject(Object object, int delayMilSeconds) {
            this.delayMilSeconds = delayMilSeconds;
            this.object = object;
        }
    }

}

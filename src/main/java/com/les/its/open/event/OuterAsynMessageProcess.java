package com.les.its.open.event;

import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.AckManager.response.CommandFactory;
import com.les.its.open.event.AckManager.response.ResponseMessage;
import com.les.its.open.event.AckManager.response.ResponseStatus;
import com.les.its.open.netty.ChannelHolder;
import com.les.its.open.protocol.common.InterProtocol;
import com.les.its.open.protocol.common.message.AbstractProtocolMessage;
import com.les.its.open.protocol.common.utils.MessageProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @ClassName: AsynMessageProcess
 * @Description: 总线数据处理，接收报文发送的数据
 * @Author: king
 * @CreateDate: 2018/12/3 19:25
 */
@Component
@Slf4j
public class OuterAsynMessageProcess extends AbstractSummary {


    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private MessageProcessUtils messageProcessUtils;

    /**
     * 发送应道到数据总线
     *
     * @param responseStatus
     * @param interProtocol
     */
    private void publishErrorResponse(ResponseStatus responseStatus, InterProtocol interProtocol) {
        ResponseMessage errorMessageResponse = CommandFactory.createMessageResponse(responseStatus, interProtocol);
        messagePublisher.publishMessage(errorMessageResponse);
    }

    /**
     * 接收发往外系统的报文，进行报文的转换
     *
     * @param interProtocol
     */
    @EventListener
    @Async(GlobalConfigure.OUTER_MESSAGE_ASYC_EXECUTOR)
    public void consumeMessage(InterProtocol interProtocol) {

        log.debug("准备向外部发送数据报文...{}", interProtocol);

        //模拟发送数据项
        if (interProtocol.isSimSend()) {
            //是否需要直接应答成功
            if(interProtocol.isAckDirect()) {
                ResponseMessage messageResponse = CommandFactory.createMessageResponse(ResponseStatus.SUCCESS, interProtocol);
                log.debug("模拟数据直接返回发送成功{}", messageResponse);
                messagePublisher.publishMessage(messageResponse);
            }
            return;
        }

        /*根据协议类型查找活跃连接，进行数据转换发送数据*/
        if (interProtocol.getOuterProtocolType() == null || interProtocol.getOuterProtocolType().isEmpty()) {
            log.error("没有指定外部协议类别，无法进行数据发送");
            publishErrorResponse(ResponseStatus.ERROR_PROTOCL_TYPE, interProtocol);
            return;
        }

        if (interProtocol.getOuterIp() != null && !interProtocol.getOuterIp().isEmpty()) {
            /*根据ip查找对应的协议类型，进行数据转换发送数据*/
            log.info("报文指定了发送ip-{}", interProtocol.getOuterIp());
            Optional<ChannelHolder.ChannelInfo> channelInfo = ChannelHolder.getChannelInfo(interProtocol.getOuterIp(),
                    interProtocol.getOuterPort(),
                    Integer.parseInt(interProtocol.getOuterProtocolType()));
            if (channelInfo.isEmpty()) {
                log.error("没有找到ip-{},协议-{}对应的socket通道，无法进行数据的发送", interProtocol.getOuterIp(), interProtocol.getOuterProtocolType());
                publishErrorResponse(ResponseStatus.ERROR_COMM, interProtocol);
                return;
            }

            Optional<AbstractProtocolMessage> optionalAbstractProtocolMessage = messageProcessUtils.processInternalMessage(interProtocol, channelInfo.get().getProtocolType());
            if (optionalAbstractProtocolMessage.isPresent()) {
                ChannelHolder.send(channelInfo.get(), optionalAbstractProtocolMessage.get(), interProtocol, messagePublisher);
                long dataLong = getAtomicSuccessLong().incrementAndGet();
                log.debug("success encode message [" + dataLong + "] InterProtocol in thread " + Thread.currentThread().getName());
            } else {
                long errorLong = getAtomicErrorLong().incrementAndGet();
                log.error("InterProtocol encode error, already error/total encode [{}/{}]", errorLong, errorLong + getAtomicSuccessLong().get());
                publishErrorResponse(ResponseStatus.ERROR_ENCODE_MESSAGE, interProtocol);
            }
        } else {
            log.error("没有找到合适的连接发送数据");
            publishErrorResponse(ResponseStatus.NOT_AVALIABLE_LINK, interProtocol);
        }
    }

}

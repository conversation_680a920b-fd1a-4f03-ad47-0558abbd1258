package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.HashSet;
import java.util.Set;

/**
 * 自定义验证器实现
 */
public class DigitsInSetValidator implements ConstraintValidator<DigitsInSet, Integer> {
    private Set<Integer> acceptedValues;


    @Override
    public void initialize(DigitsInSet constraintAnnotation) {
        acceptedValues = new HashSet<>();
        for (int value : constraintAnnotation.acceptedValues()) {
            acceptedValues.add(value);
        }
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // null值的验证交给@NotNull等注解
        }
        return acceptedValues.contains(value);
    }
}
package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.LogicInput;
import lombok.Data;

@Data
public class DetectorParam extends LogicInput implements SgpTransAble {
    public static final String MqObjectId = "10021";


    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return getLogicInputNo();
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}

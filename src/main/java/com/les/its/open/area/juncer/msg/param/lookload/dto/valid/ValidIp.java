package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;


import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = IpAddressValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidIp {
    String message() default "无效的IP地址";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    // 允许指定需要验证的IP版本
    IpVersion[] versions() default {IpVersion.IPv4, IpVersion.IPv6};

    // IP版本枚举
    enum IpVersion {
        IPv4,
        IPv6
    }
}
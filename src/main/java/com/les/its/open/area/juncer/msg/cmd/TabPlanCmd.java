package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 方案控制
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabPlanCmd extends TabInBase implements NeedAck {
    
    /**
     * 方案启动/查询
     * 1:启动，2：查询
     */
    @NotNull(message = "方案启动/查询不能为空")
    @DigitsInSet(acceptedValues = {1, 2}, message = "方案启动/查询必须是1或2")
    private Integer cmdNo;
    
    /**
     * 子路口号
     */
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号范围[1,8]")
    private Integer crossingSeqNo;
    
    /**
     * 特征值
     */
    @NotNull(message = "特征值不能为空")
    @DigitsInSet(acceptedValues = {1, 5, 6}, message = "特征值必须是1或5或6")
    private Integer character;
    
    /**
     * 时间/周期类型
     * 1:时间控制；2：周期控制
     */
    @NotNull(message = "时间/周期类型不能为空")
    @DigitsInSet(acceptedValues = {1, 2}, message = "时间/周期类型必须是1或2")
    private Integer cmdType;
    
    /**
     * 指定运行时间/周期
     */
    @NotNull(message = "指定运行时间/周期参数不能为空")
    private Integer duration;
    
    /**
     * 协调阶段号
     */
    @NotNull(message = "协调阶段号不能为空")
    private Integer stageSeq;
    
    /**
     * 相位差
     */
    @NotNull(message = "相位差不能为空")
    private Integer offset;
    
    /**
     * 阶段数
     */
    @NotNull(message = "阶段数不能为空")
    @Range(min = 2, max = 16, message = "阶段数范围[2,16]")
    private Integer stageNum;
    
    /**
     * 阶段链
     */
    @NotNull(message = "阶段链不能为空")
    @Size(min = 2, max = 16, message = "阶段链长度范围[2,16]")
    @DiscreteValuesList(min = 1, max=64, message = "阶段链值范围[1,64]")
    private List<Integer> stageNos;
    
    /**
     * 阶段时长链
     */
    @NotNull(message = "阶段时长链不能为空")
    @Size(min = 2, max = 16, message = "阶段时长链长度范围[2,16]")
    private List<Integer> stageTimes;
    
    /**
     * 相位表号
     */
    private Integer phaseTableNo;


    @AssertTrue(message = "阶段链、阶段时长和阶段个数不匹配")
    private boolean isStageNumValid() {
        if (stageNum == null || stageNos == null || stageTimes == null) {
            return true; // 让 @NotNull 处理空值验证
        }
        return ( stageNos.size() ==  stageTimes.size() ) &&  ( stageNos.size() == stageNum );
    }


    @Override
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_PLAN_CMD.getCode());
    }
}
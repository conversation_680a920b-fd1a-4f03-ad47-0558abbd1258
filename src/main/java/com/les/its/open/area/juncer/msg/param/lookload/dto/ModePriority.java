package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModePriority implements DataIndexAble {
    // 控制模式优先级
    @NotNull(message = "控制模式优先级不能为空")
    @Size(min = 255, max = 255, message = "控制模式优先级个数应该为255")
    private List<Integer> modePrioritys;

    @Override
    public int getDataNo() {
        return 1;
    }
}
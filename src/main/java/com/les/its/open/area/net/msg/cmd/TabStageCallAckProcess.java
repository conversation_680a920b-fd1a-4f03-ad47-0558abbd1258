package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabStageCall;
import com.les.its.open.area.juncer.msg.cmd.TabStageCallAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabStageCallAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_STAGE_CALL_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 8;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabStageCallAck stageCallAck = new TabStageCallAck();
            stageCallAck.setAck(buf.readUnsignedByte() & 0xff);
            stageCallAck.setReason(buf.readUnsignedByte() & 0xff);

            TabStageCall stageCall = new TabStageCall();
            stageCallAck.setTabStageCall(stageCall);
            List<Integer> calls = new ArrayList<>();
            stageCall.setSystemCalls(calls);

            //标记
            long longValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                calls.add((int) ((longValue >> i) & 0x01));
            }
            buf.release();

            return Optional.of(stageCallAck);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.ReportSetting;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ReportSettingProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_REPORT_SETTING;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            ReportSetting reportSetting = new ReportSetting();
            objects.add(reportSetting);

            // 实时相位/灯组状态
            reportSetting.setPhaseStatus(buf.readUnsignedByte() & 0xff);
            // 实时阶段状态
            reportSetting.setStageStatus(buf.readUnsignedByte() & 0xff);
            // 上传间隔
            reportSetting.setStageStatusInterval(buf.readUnsignedShortLE() & 0xffff);
            // 实时控制方式/方案/日计划/调度计划
            reportSetting.setPlanStatus(buf.readUnsignedByte() & 0xff);
            // 上传间隔
            reportSetting.setPlanStatusInterval(buf.readUnsignedShortLE() & 0xffff);
            // 设备状态(电流/电压/温度)
            reportSetting.setDeviceStatus(buf.readUnsignedByte() & 0xff);
            // 上传间隔
            reportSetting.setDeviceStatusInterval(buf.readUnsignedShortLE() & 0xffff);
            // 交通数据-存在数据
            reportSetting.setExistenceData(buf.readUnsignedByte() & 0xff);
            // 交通数据-统计数据
            reportSetting.setStatisticalData(buf.readUnsignedByte() & 0xff);
            // 上传间隔
            reportSetting.setStatisticalDataInterval(buf.readUnsignedShortLE() & 0xffff);
            // 灯电压电流状态
            reportSetting.setLampUI(buf.readUnsignedByte() & 0xff);
            // 上传间隔
            reportSetting.setLampUIInterval(buf.readUnsignedShortLE() & 0xffff);
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof ReportSetting)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            ReportSetting reportSetting
                    = (ReportSetting)objects.get(i);

           // 实时相位/灯组状态
            buf.writeByte(reportSetting.getPhaseStatus());
            // 实时阶段状态
            buf.writeByte(reportSetting.getStageStatus());
            // 上传间隔
            buf.writeShortLE(reportSetting.getStageStatusInterval());
            // 实时控制方式/方案/日计划/调度计划
            buf.writeByte(reportSetting.getPlanStatus());
            // 上传间隔
            buf.writeShortLE(reportSetting.getPlanStatusInterval());
            // 设备状态(电流/电压/温度)
            buf.writeByte(reportSetting.getDeviceStatus());
            // 上传间隔
            buf.writeShortLE(reportSetting.getDeviceStatusInterval());
            // 交通数据-存在数据
            buf.writeByte(reportSetting.getExistenceData());
            // 交通数据-统计数据
            buf.writeByte(reportSetting.getStatisticalData());
            // 上传间隔
            buf.writeShortLE(reportSetting.getStatisticalDataInterval());
            // 灯电压电流状态
            buf.writeByte(reportSetting.getLampUI());
            // 上传间隔
            buf.writeShortLE(reportSetting.getLampUIInterval());
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 17;
    }

    @Override
    public Class dataClazz() {
        return ReportSetting.class;
    }
}

package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabUIStatus;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabUIStatusProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_UI_STATUS;
    }

    @Override
    public int oneItemSize() {
        return ( 64 * ( 1 + 3 * 2));
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabUIStatus tabUIStatus = new TabUIStatus();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            List<TabUIStatus.UIStatus> uiStatusList = new ArrayList<>();
            tabUIStatus.setUiStatus(uiStatusList);
            for (int i = 0; i < 64; i++) {
                TabUIStatus.UIStatus uiStatus = new TabUIStatus.UIStatus();
                uiStatusList.add(uiStatus);
                uiStatus.setLightsGroupNo(buf.readUnsignedByte() & 0xff);

                List<TabUIStatus.LightVoltageCurrent> lightVoltageCurrents = new ArrayList<>();
                uiStatus.setLightVoltageCurrent(lightVoltageCurrents);
                for (int t = 0; t < 3; t++) {
                    TabUIStatus.LightVoltageCurrent lightVoltageCurrent = new TabUIStatus.LightVoltageCurrent();
                    lightVoltageCurrents.add(lightVoltageCurrent);
                    lightVoltageCurrent.setVoltage(buf.readUnsignedShort() & 0xffff);
                    lightVoltageCurrent.setCurrent(buf.readUnsignedShort() & 0xffff);
                }
            }

            buf.release();
            return Optional.of(tabUIStatus);
        }

        return Optional.empty();
    }
}

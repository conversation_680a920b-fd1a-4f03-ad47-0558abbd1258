package com.les.its.open.area.message.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.les.ads.ds.gis.arm.CrossingDTO;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.config.TestSignalConfigure;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/18 14:27
 */
@Service
@Slf4j
public class CrossingService {
    @Getter
    private Map<String, CrossingBaseInfo> crossingBaseInfoMap = new ConcurrentHashMap<>();

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private ControllerService controllerService;


    @Data
    public static class CrossingBaseInfo {
        private String crossingId;
        private String controllerId;
        private int noArea;
        private int noJunc;
        private int subJuncNo;

        private String name;

        /**
         * 状态检查时控，如果出现失败，则增加
         */
        private AtomicInteger statusCheckFailedCount = new AtomicInteger(0);
        private AtomicInteger timeCount = new AtomicInteger(0);

        /**
         * 判定路口状态是否正常
         *
         * @return
         */
        public boolean isOnline() {
            {
                return statusCheckFailedCount.get() <= 10;
            }
        }
    }


    @PostConstruct
    public void initData() {
            if (testSignalConfigure.isUseTest()) {
                testSignalConfigure.getSignalMap().values().forEach(controllerBaseInfoDto -> {

                if (controllerBaseInfoDto.getCrossingIds() != null
                        && controllerBaseInfoDto.getSubJuncNos() != null
                        && controllerBaseInfoDto.getCrossingIds().size() == controllerBaseInfoDto.getSubJuncNos().size()) {

                    for (int i = 0; i < controllerBaseInfoDto.getCrossingIds().size(); i++) {
                        CrossingBaseInfo crossingBaseInfo = new CrossingBaseInfo();
                        crossingBaseInfo.setCrossingId(controllerBaseInfoDto.getCrossingIds().get(i));
                        crossingBaseInfo.setControllerId(controllerBaseInfoDto.getSignalId());
                        crossingBaseInfo.setNoArea(controllerBaseInfoDto.getNoArea());
                        crossingBaseInfo.setNoJunc(controllerBaseInfoDto.getNoJunc());
                        crossingBaseInfo.setSubJuncNo(controllerBaseInfoDto.getSubJuncNos().get(i));
                        crossingBaseInfo.setName("调试测试1");
                        crossingBaseInfoMap.put(crossingBaseInfo.getCrossingId(), crossingBaseInfo);
                    }
                }
            });
        }
    }

    /**
     * 获取路口基本数据项
     *
     * @param signalId
     * @param subJuncNo
     * @return
     */
    public Optional<CrossingBaseInfo> getCrossingInfo(String signalId, int subJuncNo) {
        return crossingBaseInfoMap.values().stream().filter(
                crossingBaseInfo ->
                {
                    if (crossingBaseInfo.getControllerId() == null) {
                        return false;
                    }
                    return crossingBaseInfo.getControllerId().equals(signalId) && crossingBaseInfo.getSubJuncNo() == subJuncNo;
                }
        ).findFirst();
    }


    /**
     * 获取路口基本数据项
     *
     * @param signalId
     * @return
     */
    public Optional<List<CrossingBaseInfo>> getCrossingInfos(String signalId) {
        List<CrossingBaseInfo> crossingBaseInfos = crossingBaseInfoMap.values().stream().filter(
                crossingBaseInfo ->
                {
                    if (crossingBaseInfo.getControllerId() == null) {
                        return false;
                    }
                    return crossingBaseInfo.getControllerId().equals(signalId);
                }
        ).collect(Collectors.toList());
        return Optional.ofNullable(crossingBaseInfos);
    }

    /**
     * 获取路口基本数据项
     *
     * @param crossingId
     * @return
     */
    public Optional<CrossingBaseInfo> getCrossingInfo(String crossingId) {
        return Optional.ofNullable(crossingBaseInfoMap.get(crossingId));
    }



    /**
     * 从sgp读取路口基本参数
     */
    public boolean updateCrossingFromSgp() {
        log.info("准备检查路口数据项,向sgp-url-{}请求路口参数", GlobalConfigure.signalParamIp);
        AtomicBoolean dataChange = new AtomicBoolean(false);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/arm/crossing";
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口数据返回异常");
                return dataChange.get();
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回路口的数据项个数是-{}", jsonArray.size());

            Map<String, CrossingBaseInfo> currentCrossingBaseInfoMap = new ConcurrentHashMap<>();
            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        CrossingDTO crossing = jsonArray.getObject(index, CrossingDTO.class);

                        if (crossing.getSignalControllerId() == null || crossing.getId() == null) {
                            return;
                        }

                        //针对非telvent的信号机路口进行数据过滤
                        if (!controllerService.processMqMsg(crossing.getSignalControllerId())) {
                            return;
                        }

                        //构建数据项存入本地
                        CrossingBaseInfo crossingBaseInfo = new CrossingBaseInfo();
                        crossingBaseInfo.setCrossingId(crossing.getId());
                        crossingBaseInfo.setControllerId(crossing.getSignalControllerId());
                        crossingBaseInfo.setNoArea(crossing.getNoArea());
                        crossingBaseInfo.setNoJunc(crossing.getNoJunc());
                        crossingBaseInfo.setSubJuncNo(crossing.getNoSubJunc());
                        crossingBaseInfo.setName(crossing.getName());

                        currentCrossingBaseInfoMap.put(crossingBaseInfo.getCrossingId(), crossingBaseInfo);
                    }
            );

            //1、检查路口数据是否进行了删除
            crossingBaseInfoMap.keySet().stream().forEach(
                    crossingId -> {
                        if (!currentCrossingBaseInfoMap.containsKey(crossingId)) {
                            log.error("删除路口数据项-{}", crossingId);
                            dataChange.set(true);
                            crossingBaseInfoMap.remove(crossingId);
                        }
                    }
            );
            //2、检查数据更新及插入新数据
            currentCrossingBaseInfoMap.keySet().stream().forEach(
                    crossingId -> {
                        CrossingBaseInfo crossingBaseInfo = crossingBaseInfoMap.get(crossingId);
                        CrossingBaseInfo crossingBaseInfoNew = currentCrossingBaseInfoMap.get(crossingId);
                        if (crossingBaseInfo == null) {
                            dataChange.set(true);
                            log.error("添加路口数据项-{}", crossingBaseInfoNew);
                            crossingBaseInfoMap.put(crossingId, crossingBaseInfoNew);
                        }else{

                            //fix 路口关联的信号机编号发生变化20250704
                            if(!crossingBaseInfo.getControllerId().equals(crossingBaseInfoNew.getControllerId())){
                                dataChange.set(true);
                                log.error("路口{}关联的信号机编号发生变化,原始数据{},更新后数据{}", crossingId, crossingBaseInfo, crossingBaseInfoNew);
                                crossingBaseInfo.setControllerId(crossingBaseInfoNew.getControllerId());
                            }
                            //fix 区域号/子路口号发生变化20250704
                            if(crossingBaseInfo.getNoArea() != crossingBaseInfoNew.getNoArea()
                                    || crossingBaseInfo.getNoJunc() != crossingBaseInfoNew.getNoJunc()
                                    || crossingBaseInfo.getSubJuncNo() != crossingBaseInfoNew.getSubJuncNo()){
                                dataChange.set(true);
                                log.error("路口{}的区域号/子路口号发生变化,原始数据{},更新后数据{}", crossingId, crossingBaseInfo, crossingBaseInfoNew);
                                crossingBaseInfo.setNoArea(crossingBaseInfoNew.getNoArea());
                                crossingBaseInfo.setNoJunc(crossingBaseInfoNew.getNoJunc());
                                crossingBaseInfo.setSubJuncNo(crossingBaseInfoNew.getSubJuncNo());
                            }
                            //fix 路口名称发生变化
                            if(!crossingBaseInfo.getName().equals(crossingBaseInfoNew.getName())){
                                dataChange.set(true);
                                log.error("路口{}的名称发生变化,原始数据{},更新后数据{}", crossingId, crossingBaseInfo, crossingBaseInfoNew);
                                crossingBaseInfo.setName(crossingBaseInfoNew.getName());
                            }
                        }
                    }
            );
        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }

        return dataChange.get();
    }

    /**
     * 从sgp读取路口基本参数
     */
    public void getCrossingFromSgp() {
        log.info("向sgp-url-{}请求路口参数", GlobalConfigure.signalParamIp);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/arm/crossing";
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取路口数据返回异常");
                return;
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回路口的数据项个数是-{}", jsonArray.size());

            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        CrossingDTO crossing = jsonArray.getObject(index, CrossingDTO.class);

                        if (crossing.getSignalControllerId() == null || crossing.getId() == null) {
                            return;
                        }

                        //针对非telvent的信号机路口进行数据过滤
                        if (!controllerService.processMqMsg(crossing.getSignalControllerId())) {
                            return;
                        }

                        //构建数据项存入本地
                        CrossingBaseInfo crossingBaseInfo = new CrossingBaseInfo();
                        crossingBaseInfo.setCrossingId(crossing.getId());
                        crossingBaseInfo.setControllerId(crossing.getSignalControllerId());
                        crossingBaseInfo.setNoArea(crossing.getNoArea());
                        crossingBaseInfo.setNoJunc(crossing.getNoJunc());
                        crossingBaseInfo.setSubJuncNo(crossing.getNoSubJunc());
                        crossingBaseInfo.setName(crossing.getName());
                        log.error("添加路口数据项-{}", crossingBaseInfo);

                        crossingBaseInfoMap.put(crossingBaseInfo.getCrossingId(), crossingBaseInfo);
                    }
            );

        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }
    }

}

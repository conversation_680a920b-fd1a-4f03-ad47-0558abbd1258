package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LampFaultDetectFlag implements DataIndexAble {

    @NotNull(message = "灯故障检测标记不能为空")
    @DiscreteValuesList(acceptedValues = {0, 1}, 
    allowNull = true, 
    allowDuplicates = true, 
    message = "灯故障检测标记有效范围是[0,1]")
    @Size(min = 64, max = 64, message = "灯故障检测标记数量必须为64")
    // 灯故障检测标记
    private List<Integer> reportFlags;

    @Override
    public int getDataNo() {
        return 1;
    }
}
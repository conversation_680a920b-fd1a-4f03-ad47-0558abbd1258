package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.*;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class LampSequenceProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_LAMP_SEQUENCE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            LampSequence lampSequence = new LampSequence();
            objects.add(lampSequence);

            //相位灯序编号
            lampSequence.setLampSequenceNo(buf.readUnsignedByte() & 0xff);

            //灯序类型
            lampSequence.setSequenceType(buf.readUnsignedByte() & 0xff);

            //灯序名称
            {
                byte[] name = new byte[64];
                buf.readBytes(name);
                lampSequence.setSequenceName(new String(name, StandardCharsets.UTF_8).trim());
            }

            //常规灯色
            DefaultColor defaultColor = new DefaultColor();
            lampSequence.setDefaultColor(defaultColor);
            defaultColor.setRow(buf.readUnsignedByte() & 0xff);
            defaultColor.setNotAtRow(buf.readUnsignedByte() & 0xff);
            defaultColor.setPartTime(buf.readUnsignedByte() & 0xff);

            //过渡灯色
             {

                 //失去路权配置
                 {
                     List<LightTransition> loseLightTransitions =  new ArrayList<>();
                     lampSequence.setLoseLightTransitions(loseLightTransitions);
                     for (int j = 0; j < 4; j++) {
                         LightTransition loseLightTransition = new LightTransition();
                         loseLightTransitions.add(loseLightTransition);
                         loseLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                         loseLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                     }
                 }

                 //获得路权配置
                 {
                     List<LightTransition> obtainLightTransitions =  new ArrayList<>();
                     lampSequence.setObtainLightTransitions(obtainLightTransitions);
                     for (int j = 0; j < 4; j++) {
                         LightTransition obtainLightTransition = new LightTransition();
                         obtainLightTransitions.add(obtainLightTransition);
                         obtainLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                         obtainLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                     }
                 }


                //开机失去路权配置
                {
                    List<LightTransition> loseLightTransitionStartups =  new ArrayList<>();
                    lampSequence.setLoseLightTransitionStartups(loseLightTransitionStartups);
                    for (int j = 0; j < 4; j++) {
                        LightTransition loseLightTransitionStartup = new LightTransition();
                        loseLightTransitionStartups.add(loseLightTransitionStartup);
                        loseLightTransitionStartup.setColorType(buf.readUnsignedByte() & 0xff);
                        loseLightTransitionStartup.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }

                //开机获得路权配置
                {
                    List<LightTransition> obtainLightTransitionStartups =  new ArrayList<>();
                    lampSequence.setObtainLightTransitionStartups(obtainLightTransitionStartups);
                    for (int j = 0; j < 4; j++) {
                        LightTransition obtainLightTransition = new LightTransition();
                        obtainLightTransitionStartups.add(obtainLightTransition);
                        obtainLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        obtainLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }

                //正常到黄闪失去路权配置
                {
                    List<LightTransition> loseLightTransitionYellowFlashs =  new ArrayList<>();
                    lampSequence.setLoseLightTransitionYellowFlashs(loseLightTransitionYellowFlashs);
                    for (int j = 0; j < 4; j++) {
                        LightTransition loseLightTransition = new LightTransition();
                        loseLightTransitionYellowFlashs.add(loseLightTransition);
                        loseLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        loseLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }

                //正常到黄闪开机获得路权配置
                {
                    List<LightTransition> obtainLightTransitionYellowFlashs =  new ArrayList<>();
                    lampSequence.setObtainLightTransitionYellowFlashs(obtainLightTransitionYellowFlashs);
                    for (int j = 0; j < 4; j++) {
                        LightTransition obtainLightTransition = new LightTransition();
                        obtainLightTransitionYellowFlashs.add(obtainLightTransition);
                        obtainLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        obtainLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }

                //黄闪到正常失去路权配置
                {
                    List<LightTransition> loseLightTransitionYellowFlashToNormals =  new ArrayList<>();
                    lampSequence.setLoseLightTransitionYellowFlashToNormals(loseLightTransitionYellowFlashToNormals);
                    for (int j = 0; j < 4; j++) {
                        LightTransition loseLightTransition = new LightTransition();
                        loseLightTransitionYellowFlashToNormals.add(loseLightTransition);
                        loseLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        loseLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }

                //黄闪到正常开机获得路权配置
                {
                    List<LightTransition> obtainLightTransitionYellowFlashToNormals =  new ArrayList<>();
                    lampSequence.setObtainLightTransitionYellowFlashToNormals(obtainLightTransitionYellowFlashToNormals);
                    for (int j = 0; j < 4; j++) {
                        LightTransition obtainLightTransition = new LightTransition();
                        obtainLightTransitionYellowFlashToNormals.add(obtainLightTransition);
                        obtainLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        obtainLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof LampSequence)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            LampSequence lampSequence
                    = (LampSequence)objects.get(i);

            //相位灯序编号
            buf.writeByte(lampSequence.getLampSequenceNo());

            //灯序类型
            buf.writeByte(lampSequence.getSequenceType());

            //灯序名称
            {
                byte[] name = new byte[64];
                byte[] bytes = lampSequence.getSequenceName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }

            //常规灯色
            {
                DefaultColor defaultColor = lampSequence.getDefaultColor();
                buf.writeByte(defaultColor.getRow());
                buf.writeByte(defaultColor.getNotAtRow());
                buf.writeByte(defaultColor.getPartTime());
            }


            //过渡灯色
            {

                //失去路权配置
                {
                    List<LightTransition> loseLightTransitions = lampSequence.getLoseLightTransitions();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(loseLightTransitions.get(j).getColorType());
                        buf.writeByte(loseLightTransitions.get(j).getColorTime());
                    }
                }

                //获得路权配置
                {
                    List<LightTransition> obtainLightTransitions = lampSequence.getObtainLightTransitions();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(obtainLightTransitions.get(j).getColorType());
                        buf.writeByte(obtainLightTransitions.get(j).getColorTime());
                    }
                }


                //开机失去路权配置
                {
                    List<LightTransition> loseLightTransitionStartups = lampSequence.getLoseLightTransitionStartups();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(loseLightTransitionStartups.get(j).getColorType());
                        buf.writeByte(loseLightTransitionStartups.get(j).getColorTime());
                    }
                }

                //开机获得路权配置
                {
                    List<LightTransition> obtainLightTransitionStartups = lampSequence.getObtainLightTransitionStartups();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(obtainLightTransitionStartups.get(j).getColorType());
                        buf.writeByte(obtainLightTransitionStartups.get(j).getColorTime());
                    }
                }

                //正常到黄闪失去路权配置
                {
                    List<LightTransition> loseLightTransitionYellowFlashs = lampSequence.getLoseLightTransitionYellowFlashs();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(loseLightTransitionYellowFlashs.get(j).getColorType());
                        buf.writeByte(loseLightTransitionYellowFlashs.get(j).getColorTime());
                    }
                }

                //正常到黄闪开机获得路权配置
                {
                    List<LightTransition> obtainLightTransitionYellowFlashs = lampSequence.getObtainLightTransitionYellowFlashs();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(obtainLightTransitionYellowFlashs.get(j).getColorType());
                        buf.writeByte(obtainLightTransitionYellowFlashs.get(j).getColorTime());
                    }
                }

                //黄闪到正常失去路权配置
                {
                    List<LightTransition> loseLightTransitionYellowFlashToNormals = lampSequence.getLoseLightTransitionYellowFlashToNormals();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(loseLightTransitionYellowFlashToNormals.get(j).getColorType());
                        buf.writeByte(loseLightTransitionYellowFlashToNormals.get(j).getColorTime());
                    }
                }

                //黄闪到正常开机获得路权配置
                {
                    List<LightTransition> obtainLightTransitionYellowFlashToNormals = lampSequence.getObtainLightTransitionYellowFlashToNormals();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(obtainLightTransitionYellowFlashToNormals.get(j).getColorType());
                        buf.writeByte(obtainLightTransitionYellowFlashToNormals.get(j).getColorTime());
                    }
                }
            }

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 2 + 64 + 3  +  ( 4*2*8 );
    }

    @Override
    public Class dataClazz() {
        return LampSequence.class;
    }
}

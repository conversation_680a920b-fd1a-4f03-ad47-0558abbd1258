package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Ipv4Info {
    // IPv4地址
    @NotNull(message = "IPv4地址不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "必须是有效的IPv4地址"
    )
    private String ip;

    // 子网掩码
    @NotNull(message = "子网掩码不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "必须是有效的子网掩码"
    )
    private String mask;

    // 网关
    @NotNull(message = "网关不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "必须是有效的网关"
    )
    private String gateway;


    /**
     * 生成默认参数数据,用于数据补全
     * @return
     */
    public static Ipv4Info genDefault(){
        Ipv4Info ipv4 = new Ipv4Info();
        ipv4.setIp("************00");
        ipv4.setMask("*************");
        ipv4.setGateway("************");
        return ipv4;
    }
}

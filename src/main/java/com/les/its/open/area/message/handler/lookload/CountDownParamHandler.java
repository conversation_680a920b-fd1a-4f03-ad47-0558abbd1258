package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.CountDownParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class CountDownParamHandler implements MqMsgBaseHandler {


    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public CountDownParamHandler(ControllerService controllerService, OpenLesSender openLesSender,
                                 DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_CountDownParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return CountDownParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看倒计时参数
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_COUNT_DOWN_INFO, 1, 1,
                    signalInfoOp.get());
            areaMessages.add(msg);
        }
        //倒计时屏参数
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_COUNT_DOWN_DISPLAY_PARAM, 1,
                    ParamMsgType.PARAM_COUNT_DOWN_DISPLAY_PARAM.getMax(),
                    signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            CountDownParam countDownParam = new CountDownParam();
            //设置信号机编号
            countDownParam.setSignalControllerID(requestMessage.getSignalControllerID());

            //倒计时基础配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownInfo> countdownInfosOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_COUNT_DOWN_INFO);
            if(countdownInfosOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_COUNT_DOWN_INFO);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), countdownInfosOp.get());

            //倒计时屏参数
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownDisplayParam>> countdownDisplayParamsOp
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_COUNT_DOWN_DISPLAY_PARAM);
            if(countdownDisplayParamsOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_COUNT_DOWN_DISPLAY_PARAM);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), countdownDisplayParamsOp.get());

            //倒计时基础配置
            countDownParam.setCountDown(countdownInfosOp.get());
            //倒计时屏参数
            countDownParam.setCountdownDisplayParams(countdownDisplayParamsOp.get());

            //应答参数
            datas.add(countDownParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        CountDownParam countDownParam = jsonObject.toJavaObject(CountDownParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(countDownParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, countDownParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 倒计时基础配置
        {

            List<com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownInfo> countdownInfos
                    = new ArrayList<>();
            countdownInfos.add(countDownParam.getCountDown());

            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_COUNT_DOWN_INFO,
                    countdownInfos, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //倒计时屏参数
        {
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownDisplayParam> countdownDisplayParams
                    = new ArrayList<>();
            countdownDisplayParams.addAll(countDownParam.getCountdownDisplayParams());
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_COUNT_DOWN_DISPLAY_PARAM,
                    countdownDisplayParams, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }


        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage,
                                                    List<InvokeFuture> invokeFutures,
                                                    StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultDetectFlag;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class LampFaultDetectFlagProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_LAMP_FAULT_DETECT_FLAG;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            LampFaultDetectFlag lampFaultDetect = new LampFaultDetectFlag();
            objects.add(lampFaultDetect);

            List<Integer> reportFlag = new ArrayList<>();
            lampFaultDetect.setReportFlags(reportFlag);
            for (int m = 0; m < 8; m++) {
               int data =  buf.readUnsignedByte() & 0xff ;
                for (int t = 0; t < 8; t++) {
                    if(((data >> t) & 0x01) == 0x01){
                        reportFlag.add(0x01);
                    }else{
                        reportFlag.add(0x00);
                    }
                }
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.size() != 1){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof LampFaultDetectFlag)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            LampFaultDetectFlag lampFaultDetect
                    = (LampFaultDetectFlag)objects.get(i);

            List<Integer> reportFlags = lampFaultDetect.getReportFlags();
            if(reportFlags.size() == 64) {
                for (int m = 0; m < 8; m++) {
                    int data = 0x00;
                    for (int t = 0; t < 8; t++) {
                        if ((reportFlags.get(m*8 + t)) == 0x01) {
                            data = (data | ( 0x01 << t));
                        } else {
                            data = (data | ( 0x00 << t));
                        }
                    }
                    buf.writeByte(data & 0xff);
                }
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 8;
    }

    @Override
    public Class dataClazz() {
        return LampFaultDetectFlag.class;
    }
}

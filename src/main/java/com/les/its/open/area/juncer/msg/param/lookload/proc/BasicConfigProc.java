package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.BasicConfig;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class BasicConfigProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_BASIC_CONFIG;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        BasicConfig basicConfig= new BasicConfig();

        basicConfig.setMaxSystemControlTime(buf.readUnsignedShortLE());
        basicConfig.setSystemCordRef(buf.readUnsignedShortLE() );
        basicConfig.setMaxGreenWatchDog(buf.readUnsignedShortLE());
        basicConfig.setStartInterGreen(buf.readUnsignedByte() & 0xff);
        basicConfig.setSwitchedSigns(buf.readUnsignedByte() & 0xff);
        basicConfig.setStageSkipProhibited(buf.readUnsignedByte() & 0xff);
        basicConfig.setDegradeAction(buf.readUnsignedByte() & 0xff);
        basicConfig.setHurryCallExecuteWatchDog(buf.readUnsignedShortLE() & 0xff);
        basicConfig.setYellowAsWaitIndicator(buf.readUnsignedByte() & 0xff);

        basicConfig.setMaxManualControlTime(buf.readUnsignedByte() & 0xff);
        basicConfig.setFlashOnTime((buf.readUnsignedByte() & 0xff) * 100 );
        basicConfig.setFlashOffTime((buf.readUnsignedByte() & 0xff) * 100 );

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(basicConfig);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof BasicConfig)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        BasicConfig basicConfig = (BasicConfig)objects.get(0);
        buf.writeShortLE(basicConfig.getMaxSystemControlTime());
        buf.writeShortLE(basicConfig.getSystemCordRef());
        buf.writeShortLE(basicConfig.getMaxGreenWatchDog());
        buf.writeByte(basicConfig.getStartInterGreen());
        buf.writeByte(basicConfig.getSwitchedSigns());
        buf.writeByte(basicConfig.getStageSkipProhibited());
        buf.writeByte(basicConfig.getDegradeAction());
        buf.writeShortLE(basicConfig.getHurryCallExecuteWatchDog());
        buf.writeByte(basicConfig.getYellowAsWaitIndicator());

        buf.writeByte(basicConfig.getMaxManualControlTime());
        buf.writeByte(basicConfig.getFlashOnTime()/100);
        buf.writeByte(basicConfig.getFlashOffTime()/100);


        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 16;
    }

    @Override
    public Class dataClazz() {
        return BasicConfig.class;
    }
}

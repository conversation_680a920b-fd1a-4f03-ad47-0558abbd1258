package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DayPlan implements DataIndexAble {
    // 日计划编号   
    @NotNull(message = "日计划编号不能为空")
    @Range(min = 1, max = 128, message = "日计划编号有效范围为[1,128]")
    private Integer dayPlanNo;
    
    // 子路口号
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号有效范围为[1,8]")
    private Integer crossingSeqNo;
    
    // 时段个数 
    @NotNull(message = "时段个数不能为空")
    @Range(min = 1, max = 48, message = "时段个数有效范围为[1,48]")
    private Integer segmentNum;
    
    // 时段参数列表
    @NotNull(message = "时段参数列表不能为空")
    @Size(min = 1, max = 48, message = "时段参数列表数量有效范围为[1,48]")
    @Valid
    private List<SegmentParam> segmentParams;
    
    // 名称
    @NotNull(message = "名称不能为空")
    private String name;

    @Override
    public int getDataNo() {
        return dayPlanNo;
    }
}
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhaseTableTransInfo {

    // 失去路权配置
    @NotNull(message = "失去路权配置不能为空")
    @Size(min = 4, max = 4, message = "失去路权配置数量必须为4")
    @Valid
    private List<LightTransition> loseLightTransitions;
    
    // 获得路权配置
    @NotNull(message = "获得路权配置不能为空")
    @Size(min = 4, max = 4, message = "获得路权配置数量必须为4")
    @Valid
    private List<LightTransition> obtainLightTransitions;

} 
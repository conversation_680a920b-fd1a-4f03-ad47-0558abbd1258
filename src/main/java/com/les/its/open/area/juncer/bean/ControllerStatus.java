package com.les.its.open.area.juncer.bean;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ControllerStatus {

    public ControllerStatus(){
        this.controlModeNo = 255;
        this.character = 255;
        this.planNo = 255;
        this.dayPlanNo = 255;
        this.segmentNo = 255;
        this.scheduleNo = 255;
        this.stageNoPre = 255;
        this.stageNo =255;
    }

    // 控制方式编号
    private int controlModeNo;
    // 特征值
    private int character;
    // 方案编号
    private int planNo;
    // 日计划编号
    private int dayPlanNo;
    // 时段编号
    private int segmentNo;
    // 调度表编号
    private int scheduleNo;
    // 上一阶段编号
    private int stageNoPre;
    // 当前阶段编号
    private int stageNo;
}

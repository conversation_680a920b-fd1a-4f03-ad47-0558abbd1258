package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabShieldCmd;
import com.les.its.open.area.juncer.msg.cmd.TabShieldCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabShieldCmdAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_SHIELD_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 9;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabShieldCmdAck tabShieldCmdAck = new TabShieldCmdAck();
            tabShieldCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabShieldCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabShieldCmd shieldCmd = new TabShieldCmd();
            tabShieldCmdAck.setTabShieldCmd(shieldCmd);
            List<Integer> shields = new ArrayList<>();
            shieldCmd.setShields(shields);

            //命令类型
            shieldCmd.setCmdNo(buf.readUnsignedByte() & 0xff);
            //相位标记
            long longValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                shields.add((int) ((longValue >> i) & 0x01));
            }
            buf.release();

            return Optional.of(tabShieldCmdAck);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabDwell;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabDwellProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_DWELL;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabDwell.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabDwell tabDwell) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer(1 + 2  + 8);

            buf.writeByte(tabDwell.getCrossingSeqNo());
            buf.writeShortLE(tabDwell.getDuration());
            buf.writeLongLE(tabDwell.getPhases());

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}

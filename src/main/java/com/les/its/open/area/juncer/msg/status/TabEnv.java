package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 环境状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabEnv extends TabOutBase {

    /**
     * 电压
     */
    private int voltage;
    
    /**
     * 电流
     */
    private int current;
    
    /**
     * 温度
     */
    private int temperature;
    
    /**
     * 湿度
     */
    private int humidity;
    
    /**
     * 水浸
     */
    private int waterLeak;
    
    /**
     * 烟雾
     */
    private int smoke;
} 
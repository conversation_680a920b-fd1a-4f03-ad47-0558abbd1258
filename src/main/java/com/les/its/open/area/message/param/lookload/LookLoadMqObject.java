package com.les.its.open.area.message.param.lookload;

import java.util.Arrays;
import java.util.Optional;

public enum LookLoadMqObject {

    LookLoad_BasicParam("10001", "基础信息"),
    LookLoad_ControllerParam("10002", "信号机参数"),
    LookLoad_LampGroupParam("10003", "灯组"),
    LookLoad_LampSequenceParam("10004", "相位灯序"),
    LookLoad_PhaseParam("10005", "相位参数"),
    LookLoad_StageParam("10007", "阶段相位"),
    LookLoad_StageTransitionParam("10008", "阶段转移表"),
    LookLoad_IncompatiblePhaseParam("10009", "相位冲突表"),
    LookLoad_InterGreenTimeParam("10010", "绿间隔"),
    LookLoad_PlanParam("10011", "配时方案"),
    LookLoad_DayPlanParam("10012", "日计划"),
    LookLoad_ScheduleParam("10013", "调度计划"),
    LookLoad_CountDownParam("10014", "倒计时"),
    LookLoad_LogicParam("10015", "逻辑参数"),
    LookLoad_PhaseDemandExtendParam("10016", "相位需求"),
    LookLoad_ActuatedPhaseParam("10017", "感应参数"),
    LookLoad_ConditioningParam("10018", "逻辑运算"),
    LookLoad_EmergencyParam("10019", "紧急调用"),
    LookLoad_LampGroupDetectParam("10020", "灯检测"),
    LookLoad_DetectorParam("10021", "检测器参数"),
    ;

    private String objectId;
    private String des;

    LookLoadMqObject(String objectId, String des){
        this.objectId = objectId;
        this.des = des;
    }

    public String objectId() {
        return this.objectId;
    }

    public String des(){
        return this.des;
    }

    public static Optional<LookLoadMqObject> getType(String objectId) {
        return Arrays.stream(LookLoadMqObject.values())
                .filter(messageType -> messageType.objectId().equalsIgnoreCase(objectId))
                .findAny();
    }

}

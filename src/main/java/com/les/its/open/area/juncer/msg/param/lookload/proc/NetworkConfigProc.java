package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Ipv4Info;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Ipv6Info;
import com.les.its.open.area.juncer.msg.param.lookload.dto.NetworkConfig;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class NetworkConfigProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_NETWORK_CONFIGURATION;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        NetworkConfig networkConfig = new NetworkConfig();

        //ipv4/v6启用状态
        {
            int ipEnabled = buf.readUnsignedByte();
            networkConfig.setIpEnabled(ipEnabled);
        }

        //ipv4配置
        {
            Ipv4Info ipv4Info = new Ipv4Info();
            networkConfig.setIpv4(ipv4Info);

            //4位ip地址
            {
                List<Short> datas = new ArrayList<>();
                for (int i = 0; i < 4; i++) {
                    datas.add(buf.readUnsignedByte());
                }
                String ip = datas.stream().map(
                        data -> Integer.toString(data & 0xFF)
                ).collect(Collectors.joining("."));
                ipv4Info.setIp(ip);
            }

            // 子网掩码
            {
                List<Short> datas = new ArrayList<>();
                for (int i = 0; i < 4; i++) {
                    datas.add(buf.readUnsignedByte());
                }
                String mask = datas.stream().map(
                        data -> Integer.toString(data & 0xFF)
                ).collect(Collectors.joining("."));
                ipv4Info.setMask(mask);
            }

            // 网关
            {
                List<Short> datas = new ArrayList<>();
                for (int i = 0; i < 4; i++) {
                    datas.add(buf.readUnsignedByte());
                }
                String gateway = datas.stream().map(
                        data -> Integer.toString(data & 0xFF)
                ).collect(Collectors.joining("."));
                ipv4Info.setGateway(gateway);
            }

        }

        //ipv6配置
        {
            Ipv6Info ipv6Info = new Ipv6Info();
            networkConfig.setIpv6(ipv6Info);

            //ip地址
            {
                List<Integer> datas = new ArrayList<>();
                for (int i = 0; i < 8; i++) {
                    datas.add(buf.readUnsignedShort());
                }
                String ip = datas.stream().map( data -> String.format("%04x", data))
                        .collect(Collectors.joining(":"));
                ipv6Info.setIp(ip);
            }

            // 子网掩码
            {
                List<Integer> datas = new ArrayList<>();
                for (int i = 0; i < 8; i++) {
                    datas.add(buf.readUnsignedShort());
                }
                String mask = datas.stream().map( data -> String.format("%04x", data))
                        .collect(Collectors.joining(":"));
                ipv6Info.setMask(mask);
            }

            // 网关
            {
                List<Integer> datas = new ArrayList<>();
                for (int i = 0; i < 8; i++) {
                    datas.add(buf.readUnsignedShort());
                }
                String gateway = datas.stream().map( data -> String.format("%04x", data))
                        .collect(Collectors.joining(":"));
                ipv6Info.setGateway(gateway);
            }

        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(networkConfig);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof NetworkConfig)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        NetworkConfig networkConfig = (NetworkConfig)objects.get(0);

        //ipv4/v6启用
        {
            buf.writeByte(networkConfig.getIpEnabled());
        }



        //ipv4配置
        if(networkConfig.getIpv4() == null){
            networkConfig.setIpv4(Ipv4Info.genDefault());
        }

        {

            //4位ip地址
            {
                String[] dataStrs = networkConfig.getIpv4().getIp().split("\\.");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 10)
                ).toList();
                for (int i = 0; i < 4 && i < datas.size(); i++) {
                    buf.writeByte(datas.get(i));
                }
            }

            // 子网掩码
            {
                String[] dataStrs = networkConfig.getIpv4().getMask().split("\\.");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 10)
                ).toList();
                for (int i = 0; i < 4 && i < datas.size(); i++) {
                    buf.writeByte(datas.get(i));
                }
            }

            // 网关
            {
                String[] dataStrs = networkConfig.getIpv4().getGateway().split("\\.");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 10)
                ).toList();
                for (int i = 0; i < 4 && i < datas.size(); i++) {
                    buf.writeByte(datas.get(i));
                }
            }

        }

        //ipv6配置
        if(networkConfig.getIpv6() == null){
            networkConfig.setIpv6(Ipv6Info.genDefault());
        }
        {

            //ip地址
            {
                String[] dataStrs = networkConfig.getIpv6().getIp().split(":");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 16)
                ).toList();
                for (int i = 0; i < 8 && i < datas.size(); i++) {
                    buf.writeShort(datas.get(i));
                }
            }

            // 子网掩码
            {
                String[] dataStrs = networkConfig.getIpv6().getIp().split(":");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 16)
                ).toList();
                for (int i = 0; i < 8 && i < datas.size(); i++) {
                    buf.writeShort(datas.get(i));
                }
            }

            // 网关
            {
                String[] dataStrs = networkConfig.getIpv6().getIp().split(":");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 16)
                ).toList();
                for (int i = 0; i < 8 && i < datas.size(); i++) {
                    buf.writeShort(datas.get(i));
                }
            }
        }


        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 12 +  16 * 3;
    }

    @Override
    public Class dataClazz() {
        return NetworkConfig.class;
    }
}

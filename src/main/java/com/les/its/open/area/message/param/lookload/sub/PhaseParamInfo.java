package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhaseParamInfo {
    // 相位编号
    @NotNull(message = "{basic.param.phaseInfo.phaseNo.notNull}")
    @Range(min = 1, max = 64, message = "{basic.param.phaseInfo.phaseNo.range}")
    private Integer phaseNo;

    // 相位灯序编号
    @NotNull(message = "{basic.param.phaseInfo.lampSequenceNo.notNull}")
    @Range(min = 0, max = 64, message = "{basic.param.phaseInfo.lampSequenceNo.range}")
    private Integer lampSequenceNo;
    
    // 相位灯组 [约束原则: 灯组控制源唯一，一个灯相只能属于一个相位]
    @NotNull(message = "{basic.param.phaseInfo.phaseLightsGroups.notNull}")
    @Size(min = 64, max = 64, message = "{basic.param.phaseInfo.phaseLightsGroups.size}")
    @DiscreteValuesList(min = 0, max = 1, message = "{basic.param.phaseInfo.phaseLightsGroups.range}")
    private List<Integer> phaseLightsGroups;

    /**
     * 绿灯开始方式
     * 0: 一直出现;
     * 1: 绿间隔前有需求;
     * 2: 黄口时间结束前有需求
     */
    @NotNull(message = "{basic.param.phaseInfo.appearance.notNull}")
    @Range(min = 0, max = 2, message = "{basic.param.phaseInfo.appearance.range}")
    private Integer appearance;

    /**
     * 绿灯结束方式
     * 0:阶段结束末
     */
    @NotNull(message = "{basic.param.phaseInfo.termination.notNull}")
    private Integer termination;

    // 关联相位
    @NotNull(message = "{basic.param.phaseInfo.assocPhase.notNull}")
    @Range(min = 0, max = 64, message = "{basic.param.phaseInfo.assocPhase.range}")
    private Integer assocPhase;

    // 相位名称
    private String phaseName;

    // 相位最小绿时间1
    @NotNull(message = "{basic.param.phaseInfo.minGreenTime.notNull}")
    @Range(min = 0, max = 30, message = "{basic.param.phaseInfo.minGreenTime.range}")
    private Integer minGreenTime;

    // 相位最大绿时间1
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime1.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime1.range}")
    private Integer maxGreenTime1;

    // 相位最大绿时间2
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime2.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime2.range}")
    private Integer maxGreenTime2;

    // 相位最大绿时间3
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime3.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime3.range}")
    private Integer maxGreenTime3;

    // 相位最大绿时间4
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime4.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime4.range}")
    private Integer maxGreenTime4;

    // 相位最大绿时间5
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime5.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime5.range}")
    private Integer maxGreenTime5;

    // 相位最大绿时间6
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime6.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime6.range}")
    private Integer maxGreenTime6;

    // 相位最大绿时间7
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime7.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime7.range}")
    private Integer maxGreenTime7;

    // 相位最大绿时间8
    @NotNull(message = "{basic.param.phaseInfo.maxGreenTime8.notNull}")
    @Range(min = 0, max = 255, message = "{basic.param.phaseInfo.maxGreenTime8.range}")
    private Integer maxGreenTime8;

    @AssertTrue(message = "{basic.param.phaseInfo.maxGreenTime1.greaterThanMinGreen}")
    private boolean isMaxGreenTimeGreaterThanMinGreen() {
        if (maxGreenTime1 == null || minGreenTime == null
        || maxGreenTime2 == null || maxGreenTime3 == null ||
           maxGreenTime4 == null || maxGreenTime5 == null || maxGreenTime6 == null ||
            maxGreenTime7 == null || maxGreenTime8 == null) {
            return true; // Let @NotNull handle null validation
        }

        if(phaseLightsGroups == null){
            return true;
        }

        //没有配置灯组的相位,不判定范围
        if(!phaseLightsGroups.contains(1)){
            return true;
        }

        return (maxGreenTime1 >= minGreenTime) &&
                (maxGreenTime2 >= minGreenTime) &&
                (maxGreenTime3 >= minGreenTime) &&
                (maxGreenTime4 >= minGreenTime) &&
                (maxGreenTime5 >= minGreenTime) &&
                (maxGreenTime6 >= minGreenTime) &&
                (maxGreenTime7 >= minGreenTime) &&
                (maxGreenTime8 >= minGreenTime);
    }

    @AssertTrue(message = "{basic.param.phaseInfo.lampSequenceNo.range2}")
    private boolean isLampSequenceNoValid() {
        if (lampSequenceNo == null) {
            return true; // Let @NotNull handle null validation
        }

        //没有配置灯组的相位,不判定范围
        if(!phaseLightsGroups.contains(1)){
            return true;
        }

        return (lampSequenceNo >=1) &&
                (lampSequenceNo   <= 64) ;
    }

    //@AssertTrue(message = "{basic.param.phaseInfo.maxGreenTime.sequential}")
    private boolean isMaxGreenTimeSequential() {
        if (maxGreenTime1 == null || maxGreenTime2 == null || maxGreenTime3 == null || 
            maxGreenTime4 == null || maxGreenTime5 == null || maxGreenTime6 == null || 
            maxGreenTime7 == null || maxGreenTime8 == null) {
            return true; // Let @NotNull handle null validation
        }
        
        return maxGreenTime1 <= maxGreenTime2 &&
               maxGreenTime2 <= maxGreenTime3 &&
               maxGreenTime3 <= maxGreenTime4 &&
               maxGreenTime4 <= maxGreenTime5 &&
               maxGreenTime5 <= maxGreenTime6 &&
               maxGreenTime6 <= maxGreenTime7 &&
               maxGreenTime7 <= maxGreenTime8;
    }
} 
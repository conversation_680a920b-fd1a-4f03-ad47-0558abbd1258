package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

import org.hibernate.validator.constraints.Range;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncompatiblePhase {
    // 相位编号
    @NotNull(message = "{juncer.param.incompatiblePhase.phaseNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.incompatiblePhase.phaseNo.range}")
    private Integer phaseNo;
    
    // 冲突对立相位序列 (每个相位用2bit表示:0:非对立冲突, 1:冲突, 2:对立, 3:冲突对立)
    @NotNull(message = "{juncer.param.incompatiblePhase.incompatiblePhaseSeq.notNull}")
    @Size(min = 64, max = 64, message = "{juncer.param.incompatiblePhase.incompatiblePhaseSeq.size}")
    @DiscreteValuesList(min = 0, max = 3, message = "{juncer.param.incompatiblePhase.incompatiblePhaseSeq.range}")
    private List<Integer> incompatiblePhaseSeq;
} 
package com.les.its.open.area.message.param.cmdack;

import java.util.Arrays;
import java.util.Optional;

public enum CmdAckMqObject {

    TAB_CANCEL_CMD_ACK(0x04010100,  "15002", "恢复本地控制应答"),
    TAB_CMD_ACK(0x04020100, "15003","一般控制命令应答"),
    TAB_DWELL_ACK(0x04040100, "15005","相位/灯组驻留应答"),
    TAB_SET_STAGE_ACK(0x04050100, "15006","指定相位阶段控制应答"),
    TAB_SPECIAL_MODE_ACK(0x04060100, "15007","特殊控制方式应答"),
    TAB_REAL_TIME_OPTIMIZE_ACK(0x04070100, "15008","实时优化控制应答"),
    TAB_PLAN_CMD_ACK(0x04080100, "15009","方案控制应答"),
    TAB_DETECTOR_CONTROL_GROUP_ACTUATION_ACK(0x04090100, "15010","检测器控制组应答"),
    TAB_DETECTOR_RESET_ACK(0x040A0100, "15011","检测器重置控制组应答"),
    TAB_ACTIVATED_PHASE_CONTROL_ACK(0x040B0100, "15012","感应需求相位控制应答"),
    TAB_ACTIVATED_PHASE_CONTROL2_ACK(0x040C0100, "15013", "感应需求相位控制应答"),
    TAB_STAGE_CALL_ACK(0x040D0100, "15014","阶段软件需求应答"),
    TAB_STAGE_SHIELD_ACK(0x040E0100, "15015","屏蔽控制应答"),
    TAB_PROHIBIT_CMD_ACK(0x040F0100, "15016","禁止控制应答"),
    TAB_EMERGENCY_PRIORITY_CMD_ACK(0x04100100, "15017","紧急优先控制应答"),
    TAB_EMERGENCY_PRIORITY_DISABLE_ACK(0x04110100, "15018","紧急优先屏蔽控制应答"),
    TAB_TRANSACTION_CMD_ACK(0x04120100, "15001","事务交易控制应答");


    private int value;
    private String objectId;
    private String des;

    CmdAckMqObject(int value, String objectId, String des){
        this.value = value;
        this.objectId = objectId;
        this.des = des;
    }

    public int value(){
        return this.value;
    }

    public String objectId() {
        return this.objectId;
    }

    public String des(){
        return this.des;
    }

    public static Optional<CmdAckMqObject> getType(int orgValue) {

        return Arrays.stream(CmdAckMqObject.values())
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();
    }

}

package com.les.its.open.area.juncer.bean;


import com.google.common.collect.EvictingQueue;
import com.google.common.collect.Queues;
import com.les.its.open.area.juncer.msg.status.*;
import com.les.its.open.area.net.msg.ParamMsgType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 信号机所有状态、配置、控制逻辑
 */
@Data
public class ControllerAgent {

    private String controllerId;

    /**
     * 物理链路状态
     */
    private boolean link;

    /**
     * 物理链路状态变化时间
     */
    private LocalDateTime linkChgTime;

    /**
     * 逻辑链路状态
     */
    private boolean logicLink;

    /**
     * 逻辑链路状态变化时间
     */
    private LocalDateTime logicLinkChgTime;

    /**
     * 最近发送心跳本地时间
     */
    private LocalDateTime lstHbSndTime;

    /**
     * 心跳时间计数，每隔5s发送信号机心跳
     */
    private AtomicInteger heartBeatCountSnd;

    /**
     * 未收到信号机心跳计数
     */
    private AtomicInteger heartBeatCountRev;


    //////////////////实时状态数据项--begin
    /**
     * 下标表示子路口
     */
    private List<ControllerStatus> controllerStatusList;

    /**
     * 下标表示紧急编号
     */
    private List<TabEmergency> tabEmergencyList;

    /**
     * 下标表示优先编号
     */
    private List<TabPriority> tabPriorityList;

    /**
     * 分别表示灯组、相位、阶段 屏蔽状态
     */
    private List<TabShield> tabShieldList;

    /**
     * 分别表示灯组、相位、阶段 禁止-关灯状态
     */
    private List<TabProhibit> tabProhibit0List;

    /**
     * 分别表示灯组、相位、阶段 禁止-待机状态
     */
    private List<TabProhibit> tabProhibit1List;

    /**
     * 分别表示紧急、优先屏蔽状态
     */
    private List<TabEmergencyPriorityShield> tabEmergencyPriorityShieldList;

    /**
     * 相位状态
     */
    private TabPhaseStatus tabPhaseStatus;

    /**
     * 阶段状态
     */
    private TabStageStatus tabStageStatus;


    /**
     *  信号机最近的故障
     */
    private Queue<TabFault> tabFaults;



    //////////////////实时状态数据项--end

    /**
     * 默认构造函数
     * @param controllerId
     */
    public ControllerAgent(String controllerId){
        this.controllerId = controllerId;
        this.link = false;
        this.linkChgTime = LocalDateTime.now();
        this.logicLink = false;
        this.logicLinkChgTime = LocalDateTime.now();
        this.heartBeatCountSnd = new AtomicInteger(0);
        this.heartBeatCountRev = new AtomicInteger(0);
        initStatus();
    }


    public void initStatus(){
        //总路口运行状态
        this.controllerStatusList = new CopyOnWriteArrayList<>();
        for (int i = 0; i < OpenLesConst.MAX_SUB_CROSS_NUM; i++) {
            this.controllerStatusList.add(new ControllerStatus());
        }
        //紧急数据
        this.tabEmergencyList = new CopyOnWriteArrayList<>();
        for (int i = 1; i <= ParamMsgType.PARAM_EMERGENCY.getMax(); i++) {
            TabEmergency tabEmergency = new TabEmergency();
            tabEmergency.setEmergencyNo(i);
            tabEmergencyList.add(tabEmergency);
        }
        //优先数据
        this.tabPriorityList = new CopyOnWriteArrayList<>();
        for (int i = 1; i <= ParamMsgType.PARAM_PRIORITY.getMax(); i++) {
            TabPriority tabPriority = new TabPriority();
            tabPriority.setPriorityNo(i);
            tabPriorityList.add(tabPriority);
        }
        //屏蔽数据
        this.tabShieldList = new CopyOnWriteArrayList<>();
        for (int i = 1; i <= 3; i++) {
            TabShield tabShield = new TabShield();
            tabShield.setType(i);
            tabShieldList.add(tabShield);
        }
        //禁止-关灯数据
        this.tabProhibit0List = new CopyOnWriteArrayList<>();
        for (int i = 1; i <= 3; i++) {
            TabProhibit tabProhibit = new TabProhibit();
            tabProhibit.setType(i);
            tabProhibit0List.add(tabProhibit);
        }
        //禁止-待机数据
        this.tabProhibit1List = new CopyOnWriteArrayList<>();
        for (int i = 1; i <= 3; i++) {
            TabProhibit tabProhibit = new TabProhibit();
            tabProhibit.setType(i);
            tabProhibit1List.add(tabProhibit);
        }
        //紧急优先屏蔽数据
        this.tabEmergencyPriorityShieldList = new CopyOnWriteArrayList<>();
        for (int i = 1; i <= 2; i++) {
            TabEmergencyPriorityShield tabEmergencyPriorityShield = new TabEmergencyPriorityShield();
            tabEmergencyPriorityShield.setType(i);
            tabEmergencyPriorityShieldList.add(tabEmergencyPriorityShield);
        }
        //故障数据
        this.tabFaults = Queues.synchronizedQueue(EvictingQueue.create(100));
    }


    /**
     * 物理链路发生变化
     * @param
     */
    public void setLink(boolean link){
        this.link = link;
        this.linkChgTime = LocalDateTime.now();

        //物理链路断开导致逻辑链路断开
        if(!link){
            //逻辑链路断开由心跳判定
            //setLogicLink(false);
        }
    }

    /**
     * 逻辑链路发生变化
     * @param logicLink
     */
    public void setLogicLink(boolean logicLink){
        this.logicLink = logicLink;
        this.logicLinkChgTime = LocalDateTime.now();
        this.heartBeatCountRev.set(0);
        this.heartBeatCountSnd.set(0);
    }


    /**
     * 更新心跳发送时间
     * @return
     */
    public boolean  updateHbSndTime(){
        //当上一次心跳已经正常应答后，可以重新记录当前心跳时间
        //用于解决 心跳间隔过大，超过5000ms 发送间隔时异常的心跳
        if(this.heartBeatCountRev.get() == 1) {
            this.lstHbSndTime = LocalDateTime.now();
            return true;
        }
        return false;
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidMac;
import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
public class PreConfig implements DataIndexAble {

    // MAC地址
    @ValidMac(message = "无效的MAC地址")
    private String macAddress;

    @Override
    public int getDataNo() {
        return 1;
    }

}

package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 感应需求相位控制
 */
@Data
public class TabActuatedPhaseControl2 extends TabInBase implements NeedAck {
    
    /**
     * 相位编号
     */
    @NotNull(message = "相位编号不能为空")
    @Range(min = 1, max = 64, message = "相位编号范围[1,64]")
    private Integer phaseNo;
    
    /**
     * 实时相位动作参数
     */
    @NotNull(message = "实时相位动作参数不能为空")
    private Integer phaseStartup;
    
    /**
     * 实时相位操作参数
     */
    @NotNull(message = "实时相位操作参数不能为空")
    private Integer phaseOptions;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###"
                + String.format("0x%08X", MsgType.TAB_ACTIVATED_PHASE_CONTROL2.getCode());
    }
}
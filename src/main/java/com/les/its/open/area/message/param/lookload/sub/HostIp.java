package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * 上位机IP地址信息
 */
@Data
public class HostIp {
    /**
     * 是否启用
     * 0:否，1:是
     */
    @NotNull(message = "{basic.param.hostIp.enabled.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.hostIp.enabled.range}")
    private Integer enabled;

    /**
     * 地址
     * 格式 **************
     */
    @NotNull(message = "{basic.param.hostIp.ip.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4, ValidIp.IpVersion.IPv6},
            message = "{basic.param.hostIp.ip.invalid}"
    )
    private String ip;

    /**
     * 通信端口
     */
    @NotNull(message = "{basic.param.hostIp.port.notNull}")
    @Range(min = 0, max = 65535, message = "{basic.param.hostIp.port.range}")
    private Integer port;

    /**
     * 通信类型
     * 1.tcp 2.udp 3.rs232
     */
    @NotNull(message = "{basic.param.hostIp.commType.notNull}")
    @Range(min = 1, max = 3, message = "{basic.param.hostIp.commType.range}")
    private Integer commType;

    /**
     * 协议类型
     * 1.亲新私有, 2.gbt20999, 3.920, 4.1743, 5.43229
     */
    @NotNull(message = "{basic.param.hostIp.protoType.notNull}")
    @Range(min = 1, max = 5, message = "{basic.param.hostIp.protoType.range}")
    private Integer protoType;



} 
package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * 特殊控制
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabSpecialMode extends TabInBase implements NeedAck {

    /**
     * 子路口号
     */
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号范围[1,8]")
    private Integer crossingSeqNo;

    /**
     * 0x07: 黄灯 0x08: 全红 0x09: 关灯
     */
    @NotNull(message = "控制模式不能为空")
    @DigitsInSet(acceptedValues = {7, 8, 9}, message = "控制模式必须是7、8或9")
    private Integer controlMode;
    


    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_SPECIAL_MODE.getCode());
    }
}
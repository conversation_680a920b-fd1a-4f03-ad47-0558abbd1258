package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 禁止状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabProhibit extends TabOutBase {
    /**
     * 类型
     */
    private int type;

    /**
     * 命令类型
     */
    private int cmdTag;

    /**
     * 禁止状态
     */
    private List<Integer> prohibits;

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        //修改内存数据
        if(0 == cmdTag) {
            if (type <= controllerAgent.getTabProhibit0List().size() && type > 0) {
                controllerAgent.getTabProhibit0List().get(type - 1).setProhibits(prohibits);
            }
        }else if (1 == cmdTag){
            if (type <= controllerAgent.getTabProhibit1List().size() && type > 0) {
                controllerAgent.getTabProhibit1List().get(type - 1).setProhibits(prohibits);
            }
        }

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }

} 
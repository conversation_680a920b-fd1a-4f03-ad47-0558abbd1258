package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 自定义注解，验证整数对象是否在指定的列表中
 */
@Documented
@Constraint(validatedBy = DigitsInSetValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DigitsInSet {
    String message() default "值必须在指定列表中";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    int[] acceptedValues() default {};
}

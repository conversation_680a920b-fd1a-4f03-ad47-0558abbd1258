package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;

/**
 * 检测器重置控制组应答
 */
@Data
public class TabDetectorResetAck extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;
    
    /**
     * 车辆检测器重置
     */
    private TabDetectorReset tabDetectorReset;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_DETECTOR_RESET.getCode());
    }
}
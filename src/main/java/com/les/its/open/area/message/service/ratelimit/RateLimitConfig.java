package com.les.its.open.area.message.service.ratelimit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;

/**
 * 限流配置类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RateLimitConfig {
    private long capacity;      // 桶容量
    private long tokens;        // 令牌补充数量
    private Duration period;    // 补充周期

    // 预定义配置
    public static final RateLimitConfig PER_SECOND_10 = new RateLimitConfig(10, 10, Duration.ofSeconds(1));
    public static final RateLimitConfig PER_MINUTE_100 = new RateLimitConfig(100, 100, Duration.ofMinutes(1));
    public static final RateLimitConfig PER_HOUR_1000 = new RateLimitConfig(1000, 1000, Duration.ofHours(1));
}

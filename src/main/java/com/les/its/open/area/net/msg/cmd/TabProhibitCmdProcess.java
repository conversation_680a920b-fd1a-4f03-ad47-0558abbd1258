package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabProhibitCmd;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabProhibitCmdProcess extends TabInnerBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_PROHIBIT_CMD;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabProhibitCmd.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {
        if(tabInBase instanceof TabProhibitCmd tabProhibitCmd) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer( 10);

            //属性
            buf.writeByte(tabProhibitCmd.getCmdNo());
            //命令标记
            buf.writeByte(tabProhibitCmd.getCmdTag());

            long data = 0;
            List<Integer> bits = tabProhibitCmd.getProhibits();
            for (int i = 0; i < bits.size(); i++) {
                Integer bit = bits.get(i);
                if(bit == 1){
                    data |= ( 0x01L << i);
                }
            }
            buf.writeLongLE(data);

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }
}

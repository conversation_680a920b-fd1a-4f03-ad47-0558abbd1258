package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabDetectorStatistic;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabDetectorStatisticProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_DETECTOR_STATISTICS;
    }

    @Override
    public int oneItemSize() {
        return (2 * 64 + 64 + 2 * 64);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabDetectorStatistic tabDetectorStatistic = new TabDetectorStatistic();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            // 检测器流量
            {
                List<Integer> detectorVolume = new ArrayList<>();
                tabDetectorStatistic.setDetectorVolume(detectorVolume);
                for (int i = 0; i < 64; i++) {
                    tabDetectorStatistic.getDetectorVolume().add(buf.readUnsignedShortLE());
                }
            }

            // 检测器占有率
            {
                List<Integer> detectorOccupancy = new ArrayList<>();
                tabDetectorStatistic.setDetectorOccupancy(detectorOccupancy);
                for (int i = 0; i < 64; i++) {
                    tabDetectorStatistic.getDetectorOccupancy().add(buf.readUnsignedByte() & 0xff);
                }
            }

            // 平均车速
            {
                List<Integer> averageSpeed = new ArrayList<>();
                tabDetectorStatistic.setAverageSpeed(averageSpeed);
                for (int i = 0; i < 64; i++) {
                    tabDetectorStatistic.getAverageSpeed().add(buf.readUnsignedShortLE());
                }
            }

            return Optional.of(tabDetectorStatistic);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabEmergencyPriorityCmd;
import com.les.its.open.area.juncer.msg.cmd.TabEmergencyPriorityCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabEmergencyPriorityCmdAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_EMERGENCY_PRIORITY_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 2;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabEmergencyPriorityCmdAck tabEmergencyPriorityCmdAck = new TabEmergencyPriorityCmdAck();
            tabEmergencyPriorityCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabEmergencyPriorityCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabEmergencyPriorityCmd tabEmergencyPriorityCmd = new TabEmergencyPriorityCmd();
            tabEmergencyPriorityCmdAck.setTabEmergencyPriorityCmd(tabEmergencyPriorityCmd);

            // 类型
            tabEmergencyPriorityCmd.setCmdNo(buf.readUnsignedByte() & 0xff);
            // 编号
            tabEmergencyPriorityCmd.setEmergencyPriorityNo(buf.readUnsignedByte() & 0xff);
            buf.release();

            return Optional.of(tabEmergencyPriorityCmdAck);
        }

        return Optional.empty();
    }
}

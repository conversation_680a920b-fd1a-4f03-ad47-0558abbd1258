package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 交通数据-统计数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabDetectorStatistic extends TabOutBase {
    /**
     * 检测器流量
     */
    private List<Integer> detectorVolume;
    
    /**
     * 检测器占有率
     */
    private List<Integer> detectorOccupancy;
    
    /**
     * 平均车速
     */
    private List<Integer> averageSpeed;
} 
package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.*;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class BoardInfoProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_BOARD_INFO;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        BoardInfo boardInfo = new BoardInfo();
        //生成数据项
        //主控板序列号
        {
            byte[] controlBoardSerialNumber = new byte[32];
            buf.readBytes(controlBoardSerialNumber);
            boardInfo.setControlBoardSerialNumber(new String(controlBoardSerialNumber, StandardCharsets.UTF_8).trim());
        }
        // 检测板最大数
        {
            boardInfo.setDetectionBoardMaxNumber(buf.readUnsignedByte());
        }
        // 信息板最大数
        {
            boardInfo.setInformationBoardMaxNumber(buf.readUnsignedByte());
        }
        // 灯驱板最大数
        {
            boardInfo.setLampDriverBoardMaxNumber(buf.readUnsignedByte());
        }

        // 检测板信息
        {
            List<DetectionBoard> detectionBoards = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                DetectionBoard detectionBoard = new DetectionBoard();
                detectionBoard.setEnabled(buf.readUnsignedByte());
                byte[] serialNumber = new byte[32];
                buf.readBytes(serialNumber);
                detectionBoard.setSerialNumber(new String(serialNumber, StandardCharsets.UTF_8).trim());
                detectionBoards.add(detectionBoard);
            }
            boardInfo.setDetectionBoards(detectionBoards);
        }

        // 信息板信息
        {
            InformationBoard informationBoard = new InformationBoard();
            informationBoard.setEnabled(buf.readUnsignedByte());
            byte[] serialNumber = new byte[32];
            buf.readBytes(serialNumber);
            informationBoard.setSerialNumber(new String(serialNumber, StandardCharsets.UTF_8).trim());
            boardInfo.setInformationBoard(informationBoard);
        }

        // 灯控板信息
        {
            List<LampDriverBoard> lampDriverBoards = new ArrayList<>();
            for (int i = 0; i < 16; i++) {
                LampDriverBoard lampDriverBoard = new LampDriverBoard();
                lampDriverBoard.setEnabled(buf.readUnsignedByte());
                byte[] serialNumber = new byte[32];
                buf.readBytes(serialNumber);
                lampDriverBoard.setSerialNumber(new String(serialNumber, StandardCharsets.UTF_8).trim());
                lampDriverBoards.add(lampDriverBoard);
            }
            boardInfo.setLampDriverBoards(lampDriverBoards);
        }

        boardInfo.setLampGroupNumber(buf.readUnsignedByte() );

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(boardInfo);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {

        //只能有一份数据，且数据类型为 BoardInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof BoardInfo)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        BoardInfo boardInfo = (BoardInfo)objects.get(0);
        // 主控板序列号
        {
            byte[] controlBoardSerialNumber = new byte[32];
            byte[] bytes = boardInfo.getControlBoardSerialNumber().getBytes(StandardCharsets.UTF_8);
            System.arraycopy(bytes, 0, controlBoardSerialNumber, 0, Math.min(bytes.length, 32));
            buf.writeBytes(controlBoardSerialNumber);
        }
        // 最大检测板数
        buf.writeByte(boardInfo.getDetectionBoardMaxNumber());
        // 最大信息板数
        buf.writeByte(boardInfo.getInformationBoardMaxNumber());
        // 最大灯驱板数
        buf.writeByte(boardInfo.getLampDriverBoardMaxNumber());
        // 检测板信息
        for (int i = 0; i < 4 && i < boardInfo.getDetectionBoards().size(); i++) {
            DetectionBoard detectionBoard = boardInfo.getDetectionBoards().get(i);
            buf.writeByte(detectionBoard.getEnabled());
            {
                byte[] serialNumber = new byte[32];
                byte[] bytes = detectionBoard.getSerialNumber().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, serialNumber, 0, Math.min(bytes.length, 32));
                buf.writeBytes(serialNumber);
            }
        }
        // 信息板信息
        {
            InformationBoard informationBoard = boardInfo.getInformationBoard();
            buf.writeByte(informationBoard.getEnabled());
            {
                byte[] serialNumber = new byte[32];
                byte[] bytes = informationBoard.getSerialNumber().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, serialNumber, 0, Math.min(bytes.length, 32));
                buf.writeBytes(serialNumber);
            }
        }
        // 灯驱板信息
        for (int i = 0; i < 16 && i < boardInfo.getLampDriverBoards().size(); i++) {
            LampDriverBoard lampDriverBoard = boardInfo.getLampDriverBoards().get(i);
            buf.writeByte(lampDriverBoard.getEnabled());
            {
                byte[] serialNumber = new byte[32];
                byte[] bytes = lampDriverBoard.getSerialNumber().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, serialNumber, 0, Math.min(bytes.length, 32));
                buf.writeBytes(serialNumber);
            }
        }
        //灯驱板灯组数
        buf.writeByte(boardInfo.getLampGroupNumber());

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return   32 + 3 + 4*(33) + 33 + 16 * 33 + 1;
    }

    @Override
    public Class dataClazz() {
        return BoardInfo.class;
    }

}

package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.Action;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Conditioning;

import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 条件参数
 */
@Data
public class ConditioningParam implements SgpTransAble {
    public static final String MqObjectId = "10018";

    /**
     * 条件运算列表
     */
    @NotNull(message = "{basic.param.conditioning.conditionings.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.conditioning.conditionings.size}")
    @Valid
    private List<Conditioning> conditionings;

    /**
     * 动作列表
     */
    @NotNull(message = "{basic.param.conditioning.actions.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.conditioning.actions.size}")
    @Valid  
    private List<Action> actions;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
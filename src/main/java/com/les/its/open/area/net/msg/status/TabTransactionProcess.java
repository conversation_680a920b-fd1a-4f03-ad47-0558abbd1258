package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabTransaction;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Component
public class TabTransactionProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_TRANSACTION;
    }

    @Override
    public int oneItemSize() {
        return 4 +  64 + 8;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabTransaction tabTransaction = new TabTransaction();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);
            // 交易事务创建
            tabTransaction.setTransactionCreate(buf.readUnsignedByte() & 0xff);
            // 交易超时时间
            tabTransaction.setTransactionTimeout(buf.readUnsignedByte() & 0xffff);
            // 交易验证状态
            tabTransaction.setTransactionStatus(buf.readUnsignedByte() & 0xff);
            // 交易验证错误码
            tabTransaction.setTransactionErrorCode(buf.readUnsignedByte() & 0xffff);
            //交易事务说明
            {
                byte[] transactionNote = new byte[64];
                buf.readBytes(transactionNote);
                tabTransaction.setTransactionNote(new String(transactionNote, StandardCharsets.UTF_8).trim());
            }
            // 版本时间
            tabTransaction.setVersionTag(buf.readLongLE());

            buf.release();
            return Optional.of(tabTransaction);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabNotify;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabNotifyProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_NOTIFY;
    }

    @Override
    public int oneItemSize() {
        return (1 + 2);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabNotify tabNotify = new TabNotify();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);
            // 配置来源
            tabNotify.setConfigurationSource(buf.readUnsignedByte() & 0xff);
            // 修改数据项
            tabNotify.setModifiedItem(buf.readUnsignedShort() & 0xffff);
            buf.release();
            return Optional.of(tabNotify);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;

/**
 * 检测器控制组应答
 */
@Data
public class TabDetectorControlGroupActuationAck extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;
    
    /**
     * 车辆/行人检测器控制组触发
     */
    private TabDetectorControlGroupActuation tabDetectorControlGroupActuation;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_DETECTOR_CONTROL_GROUP_ACTUATION.getCode());
    }
}
package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabDetectorControlGroupActuation;
import com.les.its.open.area.juncer.msg.cmd.TabDetectorControlGroupActuationAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabDetectorControlGroupActuationAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_DETECTOR_CONTROL_GROUP_ACTUATION_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 8 + 8;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabDetectorControlGroupActuationAck actuationAck = new TabDetectorControlGroupActuationAck();
            actuationAck.setAck(buf.readUnsignedByte() & 0xff);
            actuationAck.setReason(buf.readUnsignedByte() & 0xff);

            TabDetectorControlGroupActuation actuation = new TabDetectorControlGroupActuation();
            actuationAck.setTabDetectorControlGroupActuation(actuation);
            List<Integer> detectorControlGroupActuations = new ArrayList<>();
            actuation.setDetectorControlGroupActuations(detectorControlGroupActuations);

            List<Integer> flags = new ArrayList<>();
            actuation.setFlags(flags);

            long longValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                detectorControlGroupActuations.add((int) ((longValue >> i) & 0x01));
            }

            long flagValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                flags.add((int) ((flagValue >> i) & 0x01));
            }

            buf.release();

            return Optional.of(actuationAck);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabActuatedPhaseControl;
import com.les.its.open.area.juncer.msg.cmd.TabActuatedPhaseControlAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabActuatedPhaseControlAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_DETECTOR_RESET_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 9;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabActuatedPhaseControlAck actuatedPhaseControlAck = new TabActuatedPhaseControlAck();
            actuatedPhaseControlAck.setAck(buf.readUnsignedByte() & 0xff);
            actuatedPhaseControlAck.setReason(buf.readUnsignedByte() & 0xff);

            TabActuatedPhaseControl actuatedPhaseControl = new TabActuatedPhaseControl();
            actuatedPhaseControlAck.setTabActuatedPhaseControl(actuatedPhaseControl);
            List<Integer> phases = new ArrayList<>();
            actuatedPhaseControl.setPhases(phases);

            //命令类型
            actuatedPhaseControl.setCmdNo(buf.readUnsignedByte() & 0xff);
            //相位标记
            long longValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                phases.add((int) ((longValue >> i) & 0x01));
            }
            buf.release();

            return Optional.of(actuatedPhaseControlAck);
        }

        return Optional.empty();
    }
}

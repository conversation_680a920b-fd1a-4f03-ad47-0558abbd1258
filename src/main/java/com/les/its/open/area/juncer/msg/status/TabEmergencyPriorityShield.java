package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 紧急优先屏蔽状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabEmergencyPriorityShield extends TabOutBase {
    /**
     * 类型
     */
    private int type;
    
    /**
     * 屏蔽状态
     */
    private List<Integer> shields;


    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        //修改内存数据
        if(type <= controllerAgent.getTabEmergencyPriorityShieldList().size() && type > 0 ){
            controllerAgent.getTabEmergencyPriorityShieldList().get(type - 1).setShields(shields);
        }

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }
} 
package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabTransactionCmd;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabTransactionCmdProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_TRANSACTION_CMD;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabTransactionCmd.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabTransactionCmd tabTransactionCmd) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer(2);

            buf.writeByte(tabTransactionCmd.getTransactionCreate());
            buf.writeByte(tabTransactionCmd.getTransactionTimeout());

            //交易事务说明
            {
                byte[] transactionNote = new byte[64];
                byte[] bytes = tabTransactionCmd.getTransactionNote().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, transactionNote, 0, Math.min(bytes.length, 64));
                buf.writeBytes(transactionNote);
            }

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}

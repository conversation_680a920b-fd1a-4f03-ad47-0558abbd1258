package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
    public class ModeInterGreenTime implements DataIndexAble {
        
    // 运行模式绿间隔表集合 (最多支持255个)
    @NotNull(message = "运行模式绿间隔表集合不能为空")
    @Size(min = 255, max = 255, message = "运行模式绿间隔表集合长度有效长度是255")
    @DiscreteValuesList(min = 0, max = 255, message = "运行模式绿间隔表集合有效范围是[0,255]")
    private List<Integer> modeInterGreenTimes;

    @Override
    public int getDataNo() {
        return 1;
    }
}
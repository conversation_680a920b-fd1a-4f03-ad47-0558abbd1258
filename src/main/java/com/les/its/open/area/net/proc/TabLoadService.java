package com.les.its.open.area.net.proc;

import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.TabLoadParam;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.msg.param.TabLoadParamProcess;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.area.net.utils.JuncerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class TabLoadService {

    @Autowired
    private InMsgService inMsgService;

    @Autowired
    private JuncerUtils juncerUtils;

    @Autowired
    private TabLoadParamProcess tabLoadParamProcess;

    /**
     * 同步等待数据项应答，会阻塞所在线程，请特别注意
     * @param paramMsgType
     * @param body
     * @return
     */
    public Optional<Object> load(String controllerId, ParamMsgType paramMsgType, String body) {
        TabLoadParam tabLoadParam = new TabLoadParam();
        tabLoadParam.setControllerId(controllerId);
        tabLoadParam.setMsgType(MsgType.TAB_LOAD_PARAM);
        List<Object> objects = new ArrayList<>();

        {
            Optional<List<Object>> changes = tabLoadParamProcess.change(paramMsgType, body);
            changes.ifPresent(objects::addAll);
        }

        tabLoadParam.setObjects(objects);
        tabLoadParam.setParamMsgType(paramMsgType);
        boolean chg2Outer = inMsgService.chg2Outer(tabLoadParam);
        if(chg2Outer){
            Optional<Object> op = juncerUtils.send2ControllerAndWaitForAck(tabLoadParam.getIp(), tabLoadParam, false, false);
            if(op.isPresent()){
                if( op.get() instanceof TabLoad tabLoad){
                    log.error("加载结果-{}", tabLoad);
                    return Optional.of(tabLoad);
                }
            }
        }
        return Optional.empty();
    }


}

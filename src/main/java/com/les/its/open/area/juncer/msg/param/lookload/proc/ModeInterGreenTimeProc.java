package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.ModeInterGreenTime;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ModeInterGreenTimeProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_MODE_INTER_GREEN;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        ModeInterGreenTime modeInterGreenTime= new ModeInterGreenTime();
        List<Integer> modeInterGreens = new ArrayList<>();
        modeInterGreenTime.setModeInterGreenTimes(modeInterGreens);
        for (int i = 0; i < 255; i++) {
            modeInterGreens.add(buf.readUnsignedByte() & 0xff);
        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(modeInterGreenTime);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof ModeInterGreenTime)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        ModeInterGreenTime modeInterGreenTime = (ModeInterGreenTime)objects.get(0);
        for (int i = 0; (i < 255) && (i < modeInterGreenTime.getModeInterGreenTimes().size()); i++) {
            buf.writeByte(modeInterGreenTime.getModeInterGreenTimes().get(i) & 0xff);
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 255;
    }

    @Override
    public Class dataClazz() {
        return ModeInterGreenTime.class;
    }
}

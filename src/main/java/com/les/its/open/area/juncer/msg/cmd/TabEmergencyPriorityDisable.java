package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * 紧急优先屏蔽控制
 */
@Data
public class TabEmergencyPriorityDisable extends TabInBase implements NeedAck {
    
    /**
     * 类型
     */
    @NotNull(message = "类型不能为空")
    @DigitsInSet(acceptedValues = {1, 2}, message = "类型必须是1或2")
    private Integer cmdNo;
    
    /**
     * 屏蔽控制
     */
    @NotNull(message = "屏蔽控制不能为空")
    @Size(min = 64, max = 64, message = "屏蔽控制固定长度64")
    @DiscreteValuesList(acceptedValues = {0, 1}, message = "屏蔽控制值必须是0或1")
    private List<Integer> emergencyPriorities;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_DETECTOR_RESET.getCode());
    }
} 
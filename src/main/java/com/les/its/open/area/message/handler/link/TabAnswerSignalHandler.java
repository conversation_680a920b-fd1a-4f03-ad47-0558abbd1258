package com.les.its.open.area.message.handler.link;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.link.TabAnswerSignal;
import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.status.StatusMqObject;
import com.les.its.open.area.message.param.status.TabRTT;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.bussiness.service.rtt.RTTService;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.les.its.open.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class TabAnswerSignalHandler {

    private final ControllerService controllerService;

    private final ControllerAgentService controllerAgentService;

    private final MessageSender messageSender;

    private final WsMessageService wsMessageService;

    private final RTTService rttService;

    public TabAnswerSignalHandler(ControllerService controllerService,
                                  ControllerAgentService controllerAgentService,
                                  MessageSender messageSender,
                                  WsMessageService wsMessageService,
                                  RTTService rttService) {
        this.controllerService = controllerService;
        this.controllerAgentService = controllerAgentService;
        this.messageSender = messageSender;
        this.wsMessageService = wsMessageService;
        this.rttService = rttService;
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processTabAnswerSignal (TabAnswerSignal tabAnswerSignal) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabAnswerSignal.getControllerId());
        if (signalInfoOp.isEmpty()) {
            return;
        }

        Optional<ControllerAgent> controllerAgentOp = controllerAgentService.getControllerAgent(tabAnswerSignal.getControllerId());
        if(controllerAgentOp.isEmpty()){
            return;
        }

        ControllerAgent controllerAgent = controllerAgentOp.get();
        LocalDateTime lstHbSndTime = controllerAgent.getLstHbSndTime();
        if(lstHbSndTime != null){
            //计算本地时间和收到信号机心跳时间差
            long millis = Duration.between(lstHbSndTime, LocalDateTime.now()).toMillis();
            long gapTime = Duration.between(tabAnswerSignal.getLocalDateTime(), LocalDateTime.now()).toMillis() - millis/2;
            String logStr = String.format("信号机与系统[%s]RTT相差[%d]ms,信号机与系统偏差[%d]ms,系统时间[%s],信号机时间[%s]",
                    controllerAgent.getControllerId(), millis, gapTime, LocalDateTime.now().toString(), tabAnswerSignal.getLocalDateTime().toString());
            //本地日志记录
            //IPBasedLogger.logMessage(signalInfoOp.get().getIp(), signalInfoOp.get().getNoArea(),
            //        signalInfoOp.get().getNoJunc(), logStr);

            //构建中心系统数据
            TabRTT tabRTT = TabRTT.builder()
                    .rtt( millis)
                    .diff( gapTime)
                    .timeStamp(System.currentTimeMillis())
                    .build();

            //发送中心系统数据项
            MqMessage mqMessage =
                    OpenLesMqUtils.buildPushMqMsg(signalInfoOp.get().getSignalId(), StatusMqObject.TAB_RTT.objectId(),
                            tabRTT);
            messageSender.sendNats(signalInfoOp.get().getSignalId(),
                    tabRTT.getClass().getSimpleName().toLowerCase(),
                    signalInfoOp.get().getNoArea(),
                    signalInfoOp.get().getNoJunc(), mqMessage);


            //推送前端监控页面
            wsMessageService.revRTT(tabAnswerSignal.getControllerId(), tabRTT);

            //本地缓存记录
            rttService.saveRtt(tabAnswerSignal.getControllerId(), tabRTT);

        }

    }


}

package com.les.its.open.area.message.mq;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 10:06
 */
public enum MqMsgOperator {
    MqMsgOperator_Set(0x01, "设置"),
    MqMsgOperator_Query(0x02, "查询"),
    MqMsgOperator_Broadcast(0x03, "广播");

    private int value;
    private String des;

    MqMsgOperator(int value, String des){
        this.value = value;
        this.des = des;
    }

    public int value(){
        return this.value;
    }

    public String des(){
        return this.des;
    }

    public static Optional<MqMsgOperator> getType(int orgValue) {
        Optional<MqMsgOperator> messageTypeOptional = Arrays.asList(MqMsgOperator.values()).stream()
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();

        return messageTypeOptional;
    }
}

package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;

/**
 * 禁止控制应答
 */
@Data
public class TabProhibitCmdAck extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;

    
    /**
     * 禁止状态
     */
    private TabProhibitCmd tabProhibitCmd;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_PROHIBIT_CMD.getCode());
    }
}
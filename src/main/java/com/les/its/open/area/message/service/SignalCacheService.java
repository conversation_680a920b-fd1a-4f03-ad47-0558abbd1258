package com.les.its.open.area.message.service;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.message.dbsave.DataEntity;
import com.les.its.open.area.message.dbsave.QDataEntity;
import com.myweb.commons.dao.RepositoryDao;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description: 本地缓存数据项
 * @Date: create in 2021/5/31 16:26
 */
@Service
@Slf4j
@Getter
public class SignalCacheService {

    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private RepositoryDao repositoryDao;

    /**
     * 信号机数据项
     */
    private ConcurrentHashMap<String, Map<String, DataEntity>> signalInfoMap = new ConcurrentHashMap<>();


    /**
     * 从数据库中加载基础数据项
     */
    public void loadDataFromDb() {

        StopWatch stopWatch = new StopWatch("读取原始NATS数据");

        stopWatch.start("数据库NATS读取数据");

        try {
            //读取数据项
            List<DataEntity> dataEntities = queryFactory.selectFrom(QDataEntity.dataEntity).fetch();

            stopWatch.stop();
            log.error("从数据库NATS读取{}条数据花费时间——{}", dataEntities.size(), stopWatch.prettyPrint());

            stopWatch.start("装载NATS数据到内存");
            AtomicInteger dataCount = new AtomicInteger(0);
            //保存到当前内存中
            dataEntities.forEach(
                    dataEntity -> {
                        Map<String, DataEntity> stringP1049EntityMap = signalInfoMap.get(dataEntity.getSignalId());
                        //此信号机的第一条数据项
                        if (stringP1049EntityMap == null) {
                            stringP1049EntityMap = new ConcurrentHashMap<>();
                            signalInfoMap.put(dataEntity.getSignalId(), stringP1049EntityMap);
                        }
                        stringP1049EntityMap.put(dataEntity.getKey(), dataEntity);

                        int count = dataCount.getAndIncrement();
                        if (count % 5000 == 0) {
                            log.error("加载NATS内存数据-{}", count);
                        }
                    }
            );
            stopWatch.stop();
            log.error("加载NATS内存数据-{}", dataCount.get());
            log.error("加载NATS到内存花费时间——{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("初始化NATS加载中心机数据项异常", e);
        }

    }


    /**
     * 删除特定的数据项指定no
     *
     * @param signalId
     * @param type
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataEntity(String signalId, String type) {

        Map<String, DataEntity> stringDataEntityMap = signalInfoMap.get(signalId);
        if (stringDataEntityMap != null) {
            String dataKeyPrefix = DataEntity.getDataKeyPrefix(signalId, type);
            List<String> keys = stringDataEntityMap.keySet().stream().filter(
                    key -> key.startsWith(dataKeyPrefix)
            ).collect(Collectors.toList());

            keys.stream().forEach(
                    key -> stringDataEntityMap.remove(key)
            );
        }

        //移除数据库数据项
        queryFactory.delete(QDataEntity.dataEntity).where(
                QDataEntity.dataEntity.signalId.eq(signalId).and(
                        QDataEntity.dataEntity.type.eq(type)
                )).execute();
    }


    /**
     * 删除特定的数据项指定
     *
     * @param signalId
     * @param type
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataEntity(String signalId, String type, int no) {
        //移除内存数据项
        //查询是否已经有数据项
        Map<String, DataEntity> stringDataEntityMap = signalInfoMap.get(signalId);
        if (stringDataEntityMap != null) {
            stringDataEntityMap.remove(DataEntity.getDataKey(signalId, type, no));
        }

        //移除数据库数据项
        queryFactory.delete(QDataEntity.dataEntity).where(
                QDataEntity.dataEntity.signalId.eq(signalId).and(
                        QDataEntity.dataEntity.type.eq(type)
                                .and(QDataEntity.dataEntity.no.eq(no))
                )).execute();
    }


    /**
     * 调看信号机数据项时候更新本地缓存
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateData(DataEntity dataEntity) {
        String signalId = (dataEntity.getSignalId());

        //查询是否已经有数据项
        Map<String, DataEntity> stringDataEntityMap = signalInfoMap.get(signalId);
        if (stringDataEntityMap == null) {
            stringDataEntityMap = new ConcurrentHashMap<>();
            signalInfoMap.put(signalId, stringDataEntityMap);
        }

        stringDataEntityMap.put(dataEntity.getKey(), dataEntity);

        //本地数据存储
        queryFactory.delete(QDataEntity.dataEntity).where(
                QDataEntity.dataEntity.signalId.eq(dataEntity.getSignalId()).and(
                        QDataEntity.dataEntity.typeId.eq(dataEntity.getTypeId())
                                .and(QDataEntity.dataEntity.no.eq(dataEntity.getNo()))
                )).execute();
        repositoryDao.save(dataEntity, DataEntity.class);
    }

    /**
     * 从缓存中获取一种数据项，指定no
     *
     * @param signalId
     * @param no
     * @param <T>
     * @return
     */
    public <T> Optional<T> getData(String signalId, int no, Class clazz) {
        Map<String, DataEntity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }

        String nameKey = DataEntity.getDataKey(signalId, clazz.getSimpleName(), no);
        if (signalInfo.containsKey(nameKey)) {
            T data = (T) JSONObject.parseObject(signalInfo.get(nameKey).getData(), clazz);
            return Optional.of(data);
        } else {
            return Optional.empty();
        }
    }


    /**
     * 从缓存中获取一种数据项列表
     *
     * @param signalId
     * @param <T>
     * @return
     */
    public <T> Optional<List<T>> getDatas(String signalId, Class clazz) {
        Map<String, DataEntity> signalInfo = signalInfoMap.get(signalId);
        if (signalInfo == null) {
            return Optional.empty();
        }
        //获取数据项前缀
        String nameKey = DataEntity.getDataKeyPrefix(signalId, clazz.getSimpleName());
        List<String> keyList = signalInfo.keySet().stream().filter(
                key -> key.startsWith(nameKey)
        ).collect(Collectors.toList());

        List<T> datas = new ArrayList<>();
        keyList.stream().forEach(
                key -> {
                    T data = (T) JSONObject.parseObject(signalInfo.get(key).getData(), clazz);
                    datas.add(data);
                }
        );

        if (!datas.isEmpty()) {
            return Optional.of(datas);
        } else {
            return Optional.empty();
        }
    }
}

package com.les.its.open.area.message.param.lookload.sub;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 灯组信息
 */
@Data
public class LampGroup {
    /**
     * 灯组编号
     * [1-64]
     */
    @NotNull(message = "{basic.param.lampGroup.lampGroupNo.notNull}")
    @Range(min = 1, max = 64, message = "{basic.param.lampGroup.lampGroupNo.range}")
    private Integer lampGroupNo;

    /**
     * 子路口号
     * [0-8]
     */
    @NotNull(message = "{basic.param.lampGroup.crossingSeqNo.notNull}")
    @Range(min = 0, max = 8, message = "{basic.param.lampGroup.crossingSeqNo.range}")
    private Integer crossingSeqNo;

    /**
     * 灯组类型
     */
    @NotNull(message = "{basic.param.lampGroup.type.notNull}")
    private Integer type;

    /**
     * 方向
     */
    @NotNull(message = "{basic.param.lampGroup.direction.notNull}")
    private Integer direction;

    /**
     * 灯组名称
     */
    @NotNull(message = "{basic.param.lampGroup.name.notNull}")
    private String name;
} 
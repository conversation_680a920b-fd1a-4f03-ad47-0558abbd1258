package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabActuatedPhaseControl2;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabActuatedPhaseControl2Process extends TabInnerBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_ACTIVATED_PHASE_CONTROL2;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabActuatedPhaseControl2.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {
        if(tabInBase instanceof TabActuatedPhaseControl2 tabActuatedPhaseControl2) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer( 4);

            //相位编号
            buf.writeByte(tabActuatedPhaseControl2.getPhaseNo());
            //启动参数
            buf.writeByte(tabActuatedPhaseControl2.getPhaseStartup());
            //操作参数
            buf.writeShortLE(tabActuatedPhaseControl2.getPhaseOptions());

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }
}

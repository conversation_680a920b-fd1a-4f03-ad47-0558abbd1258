package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabPlanCmd;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabPlanCmdProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_PLAN_CMD;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabPlanCmd.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabPlanCmd tabPlanCmd) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer(10 + 16 + 16 + 1);

            buf.writeByte(tabPlanCmd.getCmdNo());
            buf.writeByte(tabPlanCmd.getCrossingSeqNo());
            buf.writeByte(tabPlanCmd.getCharacter());
            buf.writeByte(tabPlanCmd.getCmdType());
            buf.writeShortLE(tabPlanCmd.getDuration());
            buf.writeByte(tabPlanCmd.getStageSeq());
            buf.writeShortLE(tabPlanCmd.getOffset());
            buf.writeByte(tabPlanCmd.getStageNum());
            for (int i = 0; i < 16; i++) {
                if(i < tabPlanCmd.getStageNos().size()) {
                    buf.writeByte(tabPlanCmd.getStageNos().get(i));
                }else{
                    buf.writeByte(0x00);
                }
            }
            for (int i = 0; i < 16; i++) {
                if (i < tabPlanCmd.getStageTimes().size()) {
                    buf.writeShortLE(tabPlanCmd.getStageTimes().get(i));
                }else{
                    buf.writeShortLE(0x00);
                }
            }
            buf.writeByte(tabPlanCmd.getPhaseTableNo());

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicParamUsed implements DataIndexAble {

    // 实际相位数
    @NotNull(message = "实际相位数不能为空")
    @Range(min = 1, max = 64, message = "实际相位数有效范围[1,64]")
    private Integer usedPhaseNum;
    

    // 实际阶段数
    @NotNull(message = "实际阶段数不能为空")
    @Range(min = 2, max = 64, message = "实际阶段数有效范围[2,64]")
    private Integer usedStageNum;


    @Override
    public int getDataNo() {
        return 1;
    }
}
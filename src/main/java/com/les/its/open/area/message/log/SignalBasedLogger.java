package com.les.its.open.area.message.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

public class SignalBasedLogger {
    private static final Logger logger = LoggerFactory.getLogger(SignalBasedLogger.class);

    public static void logMessage(String signalId, String format, Object... argumentsString) {
        try {
            MDC.put("signalId", signalId);
            logger.error(format, argumentsString);
        } finally {
            MDC.remove("signalId");
        }
    }

}
package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;

/**
 * 紧急优先控制应答
 */
@Data
public class TabEmergencyPriorityCmdAck extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;

    
    /**
     * 紧急优先编号
     */
    private TabEmergencyPriorityCmd tabEmergencyPriorityCmd;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_EMERGENCY_PRIORITY_CMD.getCode());
    }
}
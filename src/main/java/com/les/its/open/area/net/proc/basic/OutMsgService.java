package com.les.its.open.area.net.proc.basic;

import com.alibaba.fastjson2.JSON;
import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabInBases;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.log.IPBasedLogger;
import com.les.its.open.area.net.log.LogPropertyFilter;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import com.les.its.open.area.net.utils.JuncerUtils;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.config.JuncerConfigure;
import com.les.its.open.event.MessagePublisher;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.les.its.open.utils.UtcTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 处理从信号机获得的报文，未转化为具体报文参数
 */
@Slf4j
@Service
public class OutMsgService implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private final Map<Integer, TabOuterBaseMsgProcess> tabBaseMsgMap = new ConcurrentHashMap<>();


    @Autowired
    private ControllerService controllerService;

    @Autowired
    private ControllerAgentService controllerAgentService;

    @Autowired
    private WsMessageService wsMessageService;

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private JuncerConfigure juncerConfigure;

    @Autowired
    private LogPropertyFilter logPropertyFilter;

    public void init(){
        Map<String, TabOuterBaseMsgProcess> beansOfType = applicationContext.getBeansOfType(TabOuterBaseMsgProcess.class);
        beansOfType.keySet().stream().forEach(key ->
        {
            tabBaseMsgMap.put(beansOfType.get(key).msgType().getCode(), beansOfType.get(key));
        });

        log.error("==================支持报文数据项==================begin");
        tabBaseMsgMap.values().stream().sorted(Comparator.comparingInt(a -> a.msgType().getCode())).forEach(
                tabOuterBaseMsgProcess -> {
                log.error("支持数据项-id-{},des-{}", String.format("0x%08X", tabOuterBaseMsgProcess.msgType().getCode()), tabOuterBaseMsgProcess.msgType().getDescription());
            }
        );
        log.error("==================支持报文数据项==================end");
    }

    @EventListener
    @Async(GlobalConfigure.JUNCER_ASYC_EXECUTOR)
    public void processJuncerMsg(JuncerMsg juncerMsg) {

        if(juncerMsg.getIp() == null
                || juncerMsg.getOpenLesMessage() == null){
            return;
        }

        int msgId = (juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_1() << 24)
                + (juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_2() << 16)
                + (juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_3() << 8)
                + juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_4();

        Optional<ControllerBaseInfo> signalInfoByIpOp = controllerService.getSignalInfoByIp(juncerMsg.getIp());
        if (signalInfoByIpOp.isEmpty()) {
            log.debug("未知设备的数据{},请检查信号机配置", juncerMsg.getIp());
            return;
        }


        //加载调看应答特殊处理
        if((MsgType.TAB_LOOK_PARAM.getCode() & 0xffff0000) == ( msgId & 0xffff0000)){
            msgId = MsgType.TAB_LOOK_PARAM.getCode();
        }
        //调看异常应答处理
        else if ((MsgType.TAB_LOOK_PARAM_ERR.getCode() & 0xffff0000) == ( msgId & 0xffff0000)){
            msgId = MsgType.TAB_LOOK_PARAM_ERR.getCode();
        }
        //加载应答处理
        else if ((MsgType.TAB_LOAD.getCode() & 0xffff0000) == ( msgId & 0xffff0000)){
            msgId = MsgType.TAB_LOAD.getCode();
        }


        TabOuterBaseMsgProcess tabOuterBaseMsgProcess = tabBaseMsgMap.get(msgId);
        if (tabOuterBaseMsgProcess == null) {
            log.debug("设备{}数据项不支持0x{}", juncerMsg.getIp(), Integer.toHexString(msgId));
        } else {

            //非可变长度数据时，对数据进行正文长度校验
            if(tabOuterBaseMsgProcess.oneItemSize() != JuncerUtils.SIZE_CHANGE_ABLE){
                if(tabOuterBaseMsgProcess.oneItemSize() != juncerMsg.getOpenLesMessage().getLesBody().getBody().length){
                    IPBasedLogger.logMessage(juncerMsg.getIp(), signalInfoByIpOp.get().getNoArea(),
                            signalInfoByIpOp.get().getNoJunc(),
                            "收到[{}]-[{}]-[{}]数据长度异常,应该是-{},实际是-{}",
                            signalInfoByIpOp.get().getSignalId(), juncerMsg.getIp(), tabOuterBaseMsgProcess.msgType().getDescription(),
                            tabOuterBaseMsgProcess.oneItemSize(), juncerMsg.getOpenLesMessage().getLesBody().getBody().length);
                    return;
                }
            }

            Optional<TabOutBase> msgInner = tabOuterBaseMsgProcess.toInner(juncerMsg, signalInfoByIpOp.get());
            if (msgInner.isEmpty()) {
                log.error("报文转换失败{}", juncerMsg);
            } else {
                //设置基础参数
                msgInner.get().setControllerId(signalInfoByIpOp.get().getSignalId());
                msgInner.get().setLocalDateTime(UtcTimeUtils.convert2LocalDateTime(juncerMsg.getOpenLesMessage().getLesHeader().getTimeStamp()));
                msgInner.get().setMsgType(tabOuterBaseMsgProcess.msgType());

                //控制报文打印
                if(juncerConfigure.getDisableLogMsgs() ==  null
                        || !juncerConfigure.getDisableLogMsgs().contains(msgId)){
                    IPBasedLogger.logMessage(juncerMsg.getIp(),
                            signalInfoByIpOp.get().getNoArea(),
                            signalInfoByIpOp.get().getNoJunc(),
                            "收到[{}]-[{}]-[{}]-{}", signalInfoByIpOp.get().getSignalId(), juncerMsg.getIp(),
                            tabOuterBaseMsgProcess.msgType().getDescription(), JSON.toJSONString(msgInner.get(), logPropertyFilter.getPropertyFilter()));
                }

                //发布通知
                messagePublisher.publishMessage(msgInner.get());

                //接收数据项
                wsMessageService.recvMsg(signalInfoByIpOp.get().getSignalId(), msgInner.get());

                //消息处理
                Optional<ControllerAgent> controllerAgentOp = controllerAgentService.getControllerAgent(signalInfoByIpOp.get().getSignalId());
                controllerAgentOp.ifPresent(controllerAgent ->
                {
                    List<TabInBase> tabInBases = msgInner.get().proc(controllerAgent);
                    if (!tabInBases.isEmpty()) {
                        TabInBases tabInbases = new TabInBases(tabInBases);
                        messagePublisher.publishMessage(tabInbases);
                    }
                });
            }
        }

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

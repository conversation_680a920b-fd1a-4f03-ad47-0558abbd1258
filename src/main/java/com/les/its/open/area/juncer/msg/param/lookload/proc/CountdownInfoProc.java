package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class CountdownInfoProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_COUNT_DOWN_INFO;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            CountdownInfo countdownInfo = new CountdownInfo();
            objects.add(countdownInfo);

            int crossingSeqNo = (buf.readUnsignedByte() & 0xff);
            countdownInfo.setEnableType(buf.readUnsignedByte() & 0xff);
            countdownInfo.setBaudRate(buf.readUnsignedByte() & 0xff);
            countdownInfo.setParity(buf.readUnsignedByte() & 0xff);
            countdownInfo.setPulseBrightWidth(buf.readUnsignedByte() & 0xff);
            countdownInfo.setPulseBrightColor(buf.readUnsignedByte() & 0xff);
            countdownInfo.setPulseDarkWidth(buf.readUnsignedByte() & 0xff);
            countdownInfo.setPulseDarkColor(buf.readUnsignedByte() & 0xff);
            countdownInfo.setRedVehicleFlag(buf.readUnsignedByte() & 0xff);
            countdownInfo.setRedVehicleTime(buf.readUnsignedByte() & 0xff);
            countdownInfo.setGreenVehicleFlag(buf.readUnsignedByte() & 0xff);
            countdownInfo.setGreenVehicleTime(buf.readUnsignedByte() & 0xff);
            countdownInfo.setRedPedestrianFlag(buf.readUnsignedByte() & 0xff);
            countdownInfo.setRedPedestrianTime(buf.readUnsignedByte() & 0xff);
            countdownInfo.setGreenPedestrianFlag(buf.readUnsignedByte() & 0xff);
            countdownInfo.setGreenPedestrianTime(buf.readUnsignedByte() & 0xff);
            countdownInfo.setInductionFlag(buf.readUnsignedByte() & 0xff);
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof CountdownInfo)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            CountdownInfo countdownInfo
                    = (CountdownInfo)objects.get(i);
            buf.writeByte(i + 1);
            buf.writeByte(countdownInfo.getEnableType());
            buf.writeByte(countdownInfo.getBaudRate());
            buf.writeByte(countdownInfo.getParity());
            buf.writeByte(countdownInfo.getPulseBrightWidth());
            buf.writeByte(countdownInfo.getPulseBrightColor());
            buf.writeByte(countdownInfo.getPulseDarkWidth());
            buf.writeByte(countdownInfo.getPulseDarkColor());
            buf.writeByte(countdownInfo.getRedVehicleFlag());
            buf.writeByte(countdownInfo.getRedVehicleTime());
            buf.writeByte(countdownInfo.getGreenVehicleFlag());
            buf.writeByte(countdownInfo.getGreenVehicleTime());
            buf.writeByte(countdownInfo.getRedPedestrianFlag());
            buf.writeByte(countdownInfo.getRedPedestrianTime());
            buf.writeByte(countdownInfo.getGreenPedestrianFlag());
            buf.writeByte(countdownInfo.getGreenPedestrianTime());
            buf.writeByte(countdownInfo.getInductionFlag());

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 17;
    }

    @Override
    public Class dataClazz() {
        return CountdownInfo.class;
    }
}

package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 相位参数
 */
@Data
public class ActuatedPhaseParam implements SgpTransAble {
    public static final String MqObjectId = "10017";

    /**
     * 相位编号
     * 范围: [1,64]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseNo.notNull}")
    @Min(value = 1, message = "{basic.param.actuatedPhase.phaseNo.min}")
    @Max(value = 64, message = "{basic.param.actuatedPhase.phaseNo.max}")
    private Integer phaseNo;

    /**
     * 相位通过时间参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phasePassage.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phasePassage.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phasePassage.max}")
    private Integer phasePassage;

    /**
     * 相位可变初始绿参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseAddedInitial.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseAddedInitial.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseAddedInitial.max}")
    private Integer phaseAddedInitial;

    /**
     * 相位最大初始绿参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseMaximumInitial.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseMaximumInitial.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseMaximumInitial.max}")
    private Integer phaseMaximumInitial;

    /**
     * 相位通过时间调整前时间参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseTimeBeforeReduction.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseTimeBeforeReduction.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseTimeBeforeReduction.max}")
    private Integer phaseTimeBeforeReduction;

    /**
     * 相位通过时间调整时相位通过时间参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseTimeToReduce.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseTimeToReduce.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseTimeToReduce.max}")
    private Integer phaseTimeToReduce;

    /**
     * 相位车辆间隔参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseReduceBy.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseReduceBy.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseReduceBy.max}")
    private Integer phaseReduceBy;

    /**
     * 相位车辆最小间隔参数
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseMinimumGap.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseMinimumGap.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseMinimumGap.max}")
    private Integer phaseMinimumGap;

    /**
     * 相位动态最大值
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseDynamicMaxLimit.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseDynamicMaxLimit.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseDynamicMaxLimit.max}")
    private Integer phaseDynamicMaxLimit;

    /**
     * 相位动态最大值步长
     * 范围: [0,255]
     */
    @NotNull(message = "{basic.param.actuatedPhase.phaseDynamicMaxStep.notNull}")
    @Min(value = 0, message = "{basic.param.actuatedPhase.phaseDynamicMaxStep.min}")
    @Max(value = 255, message = "{basic.param.actuatedPhase.phaseDynamicMaxStep.max}")
    private Integer phaseDynamicMaxStep;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return phaseNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
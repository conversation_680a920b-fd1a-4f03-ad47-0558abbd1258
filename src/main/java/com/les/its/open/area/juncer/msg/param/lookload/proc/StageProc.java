package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Stage;
import com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class StageProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_STAGE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Stage stage = new Stage();
            objects.add(stage);
            //阶段号
            stage.setStageNo(buf.readUnsignedByte() & 0xff);
            //相位参数
            List<StagePhase> stagePhases= new ArrayList<>();
            stage.setStagePhases(stagePhases);
            for (int m = 0; m < 64; m++) {
                StagePhase stagePhase = new StagePhase();
                stagePhases.add(stagePhase);
                stagePhase.setDemand(buf.readUnsignedByte() & 0xff);
                stagePhase.setLaggingTime(buf.readUnsignedByte() & 0xff);
                stagePhase.setDelayCutOffTime(buf.readUnsignedByte() & 0xff);
            }
            //相位名称
            {
                byte[] name = new byte[64];
                buf.readBytes(name);
                stage.setStageName(new String(name, StandardCharsets.UTF_8).trim());
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Stage)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Stage stage
                    = (Stage)objects.get(i);

            //阶段编号
            buf.writeByte(stage.getStageNo());
            //阶段相位参数
            List<StagePhase> stagePhases = stage.getStagePhases();
            if(stagePhases.size() == 64) {
                for (int m = 0; m < 64; m++) {
                    StagePhase stagePhase = stagePhases.get(m);
                    buf.writeByte(stagePhase.getDemand());
                    buf.writeByte(stagePhase.getLaggingTime());
                    buf.writeByte(stagePhase.getDelayCutOffTime());
                }
            }
            //相位名称
            {
                byte[] name = new byte[64];
                byte[] bytes = stage.getStageName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 64 * (3) + 64;
    }

    @Override
    public Class dataClazz() {
        return Stage.class;
    }
}

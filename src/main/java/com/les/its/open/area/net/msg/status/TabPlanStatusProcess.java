package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabPlanStatus;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabPlanStatusProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_PLAN_STATUS;
    }

    @Override
    public int oneItemSize() {
        return ( 64 + 8);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabPlanStatus tabPlanStatus = new TabPlanStatus();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            // 子路口号
            tabPlanStatus.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            // 控制方式编号
            tabPlanStatus.setControlModeNo(buf.readUnsignedByte() & 0xff);
            // 特征值
            tabPlanStatus.setCharacter(buf.readUnsignedByte() & 0xff);
            // 方案编号
            tabPlanStatus.setPlanNo(buf.readUnsignedByte() & 0xff);
            // 日计划编号
            tabPlanStatus.setDayPlanNo(buf.readUnsignedByte() & 0xff);
            // 调度表编号
            tabPlanStatus.setScheduleNo(buf.readUnsignedByte() & 0xff);
            // 阶段编号
            tabPlanStatus.setStageNo(buf.readUnsignedByte() & 0xff);
            // 阶段中相应灯组状态
            List<Integer> lightsGroupStatus = new ArrayList<>();
            tabPlanStatus.setLightsGroupStatus(lightsGroupStatus);
            for (int i = 0; i < 64; i++) {
                tabPlanStatus.getLightsGroupStatus().add(buf.readUnsignedByte() & 0xff);
            }
            // 系统状态
            tabPlanStatus.setSystemStatus(buf.readUnsignedByte() & 0xff);

            buf.release();
            return Optional.of(tabPlanStatus);
        }

        return Optional.empty();
    }
}

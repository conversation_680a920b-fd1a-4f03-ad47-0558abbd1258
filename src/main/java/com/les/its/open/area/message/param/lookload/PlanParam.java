package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.PlanStageParam;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * 方案参数
 */
@Data
public class PlanParam implements SgpTransAble {
    public static final String MqObjectId = "10011";

    /**
     * 方案编号
     */
    @NotNull(message = "{basic.param.plan.planNo.notNull}")
    @Min(value = 1, message = "{basic.param.plan.planNo.min}")
    @Max(value = 128, message = "{basic.param.plan.planNo.max}")
    private Integer planNo;

    /**
     * 子路口号
     */
    @NotNull(message = "{basic.param.plan.crossingSeqNo.notNull}")
    @Min(value = 1, message = "{basic.param.plan.crossingSeqNo.min}")
    @Max(value = 8 , message = "{basic.param.plan.crossingSeqNo.max}")
    private Integer crossingSeqNo;


    /**
     * 周期
     */
    @NotNull(message = "{basic.param.plan.cycle.notNull}")
    @Min(value = 0, message = "{basic.param.plan.cycle.min}")
    @Max(value = 65535, message = "{basic.param.plan.cycle.max}")
    private Integer cycle;

    /**
     * 协调序号
     */
    @NotNull(message = "{basic.param.plan.coordinatedStageSeq.notNull}")
    @Min(value = 1, message = "{basic.param.plan.coordinatedStageSeq.min}")
    @Max(value = 16, message = "{basic.param.plan.coordinatedStageSeq.max}")
    private Integer coordinatedStageSeq;

    /**
     * 协调参考点
     * 0绿灯切, 1黄灯切, 2红灯切
     */
    @NotNull(message = "{basic.param.plan.coordinatedRef.notNull}")
    @Min(value = 0, message = "{basic.param.plan.coordinatedRef.min}")
    @Max(value = 2, message = "{basic.param.plan.coordinatedRef.max}")
    private Integer coordinatedRef;

    /**
     * 相位差
     */
    @NotNull(message = "{basic.param.plan.offset.notNull}")
    @Min(value = 0, message = "{basic.param.plan.offset.min}")
    @Max(value = 65535, message = "{basic.param.plan.offset.max}")
    private Integer offset;

    /**
     * 方案阶段参数
     */
    @NotNull(message = "{basic.param.plan.planStageParams.notNull}")
    @Size(min = 2, max = 16, message = "{basic.param.plan.planStageParams.size}")
    @Valid
    private List<PlanStageParam> planStageParams;

    /**
     * 方案名称
     */
    @NotNull(message = "{basic.param.plan.planName.notNull}")
    private String planName;


    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return planNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
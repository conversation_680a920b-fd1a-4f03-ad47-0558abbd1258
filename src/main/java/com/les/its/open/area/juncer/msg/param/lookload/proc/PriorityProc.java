package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Priority;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PriorityProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PRIORITY;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Priority priority = new Priority();
            objects.add(priority);
            // 编号
            priority.setPriorityNo(buf.readUnsignedByte() & 0xff);
            // 申请相位阶段
            priority.setStageNo(buf.readUnsignedByte() & 0xff);
            // 申请优先级
            priority.setPriority(buf.readUnsignedByte() & 0xff);
            // 延迟时间
            priority.setDelayTime(buf.readUnsignedByte() & 0xff);
            // 持续时间
            priority.setDurationTime(buf.readUnsignedByte() & 0xff);
            // 呼叫间隔时间
            priority.setCallIntervalTime(buf.readUnsignedByte() & 0xff);
            //请求参数
            priority.setParamTypeCall(buf.readUnsignedByte() & 0xff);
            priority.setParamNoCall(buf.readUnsignedByte() & 0xff);
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Priority)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Priority priority
                    = (Priority)objects.get(i);

            // 编号
            buf.writeByte(priority.getPriorityNo() & 0xff);
            // 申请相位阶段
            buf.writeByte(priority.getStageNo() & 0xff);
            // 申请优先级
            buf.writeByte(priority.getPriority() & 0xff);
            // 延迟时间
            buf.writeByte(priority.getDelayTime() & 0xff);
            // 持续时间
            buf.writeByte(priority.getDurationTime() & 0xff);
            // 呼叫间隔时间
            buf.writeByte(priority.getCallIntervalTime() & 0xff);
            // 需求
            buf.writeByte(priority.getParamTypeCall() & 0xff);
            // 需求编号
            buf.writeByte(priority.getParamNoCall() & 0xff);
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 8;
    }

    @Override
    public Class dataClazz() {
        return Priority.class;
    }
}

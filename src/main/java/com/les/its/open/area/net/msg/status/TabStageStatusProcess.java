package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabStageStatus;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabStageStatusProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_STAGE_STATUS;
    }

    @Override
    public int oneItemSize() {
        return ( 64 + 64 * 2 + 64 * 2);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabStageStatus tabStageStatus = new TabStageStatus();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            // 阶段状态
            List<Integer> stageStatus = new ArrayList<>();
            tabStageStatus.setStageStatus(stageStatus);
            for (int i = 0; i < 64; i++) {
                stageStatus.add(buf.readUnsignedByte() & 0xff);
            }

            // 阶段需运行时间
            List<Integer> needRunningTime = new ArrayList<>();
            tabStageStatus.setNeedRunningTimes(needRunningTime);
            for (int i = 0; i < 64; i++) {
                needRunningTime.add(buf.readUnsignedShortLE() & 0xffff);
            }

            // 阶段已运行时间
            List<Integer> runningTime = new ArrayList<>();
            tabStageStatus.setRunningTimes(runningTime);
            for (int i = 0; i < 64; i++) {
                runningTime.add(buf.readUnsignedShortLE() & 0xffff);
            }

            buf.release();
            return Optional.of(tabStageStatus);
        }

        return Optional.empty();
    }
}

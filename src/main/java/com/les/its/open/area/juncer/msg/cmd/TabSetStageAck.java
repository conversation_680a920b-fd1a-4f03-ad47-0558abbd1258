package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabSetStageAck extends TabOutBase implements AckAble {

    private int ack;
    private int reason;

    private TabSetStage tabSetStage;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_SET_STAGE.getCode());
    }

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription(), this);
        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }
}

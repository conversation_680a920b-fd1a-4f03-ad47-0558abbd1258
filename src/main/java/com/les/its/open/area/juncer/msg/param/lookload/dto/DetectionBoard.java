package com.les.its.open.area.juncer.msg.param.lookload.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class DetectionBoard {
    // 是否启用
    @Range(min = 0, max = 1, message = "{juncer.param.detectionBoard.enabled.range}")
    private int enabled;

    // 检测板序列号
    @NotNull(message = "{juncer.param.detectionBoard.serialNumber.notNull}")
    private String serialNumber;
}

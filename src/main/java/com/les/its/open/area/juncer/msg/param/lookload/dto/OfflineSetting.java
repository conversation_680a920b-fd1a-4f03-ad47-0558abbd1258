package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfflineSetting implements DataIndexAble {
    // 控制命令类型
    @NotNull(message = "控制命令类型不能为空")
    @Range(min = 1, max = 128, message = "控制命令类型有效范围为[1,128]")
    private Integer cmdNo;
    
    // 恢复类型 (0:立即恢复/1:按命令时间恢复/2:按持续时间恢复)
    @NotNull(message = "恢复类型不能为空")
    @Range(min = 0, max = 2, message = "恢复类型有效范围为[0,2]")
    private Integer recoveryType;
    
    // 最大持续时间
    @NotNull(message = "最大持续时间不能为空")
    @Range(min = 0, max = 3600, message = "最大持续时间有效范围为[0,3600]")
    private Integer maxDuration;

    @Override
    public int getDataNo() {
        return cmdNo;
    }
}
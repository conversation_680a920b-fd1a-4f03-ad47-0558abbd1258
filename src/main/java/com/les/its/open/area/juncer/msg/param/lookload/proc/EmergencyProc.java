package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Emergency;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class EmergencyProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_EMERGENCY;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Emergency emergency = new Emergency();
            objects.add(emergency);
            // 紧急编号
            emergency.setEmergencyNo(buf.readUnsignedByte() & 0xff);
            // 紧急申请相位阶段
            emergency.setStageNo(buf.readUnsignedByte() & 0xff);
            // 紧急申请优先级
            emergency.setPriority(buf.readUnsignedByte() & 0xff);
            // 延迟时间
            emergency.setDelayTime(buf.readUnsignedByte() & 0xff);
            // 持续时间
            emergency.setDurationTime(buf.readUnsignedByte() & 0xff);
            // 呼叫间隔时间
            emergency.setCallIntervalTime(buf.readUnsignedByte() & 0xff);
            // 需求变量类型
            emergency.setParamTypeCall(buf.readUnsignedByte() & 0xff);
            // 需求变量编号
            emergency.setParamNoCall(buf.readUnsignedByte() & 0xff);
            // 删除变量类型
            emergency.setParamTypeCancel(buf.readUnsignedByte() & 0xff);
            // 删除变量编号
            emergency.setParamNoCancel(buf.readUnsignedByte() & 0xff);
            // 确认变量类型
            emergency.setParamTypeConfirm(buf.readUnsignedByte() & 0xff);
            // 确认变量编号
            emergency.setParamNoConfirm(buf.readUnsignedByte() & 0xff);
            // 紧急优先呼叫看门狗
            emergency.setCallWatchDog(buf.readUnsignedShortLE());
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Emergency)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Emergency emergency
                    = (Emergency)objects.get(i);

            // 紧急编号
            buf.writeByte(emergency.getEmergencyNo() & 0xff);
            // 紧急申请相位阶段
            buf.writeByte(emergency.getStageNo() & 0xff);
            // 紧急申请优先级
            buf.writeByte(emergency.getPriority() & 0xff);
            // 延迟时间
            buf.writeByte(emergency.getDelayTime() & 0xff);
            // 持续时间
            buf.writeByte(emergency.getDurationTime() & 0xff);
            // 呼叫间隔时间
            buf.writeByte(emergency.getCallIntervalTime() & 0xff);
            // 需求变量类型
            buf.writeByte(emergency.getParamTypeCall() & 0xff);
            // 需求变量编号
            buf.writeByte(emergency.getParamNoCall() & 0xff);
            // 删除变量类型
            buf.writeByte(emergency.getParamTypeCancel() & 0xff);
            // 删除变量编号
            buf.writeByte(emergency.getParamNoCancel() & 0xff);
            // 确认变量类型
            buf.writeByte(emergency.getParamTypeConfirm() & 0xff);
            // 确认变量编号
            buf.writeByte(emergency.getParamNoConfirm() & 0xff);
            // 紧急优先呼叫看门狗
            buf.writeShortLE(emergency.getCallWatchDog());

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 14;
    }

    @Override
    public Class dataClazz() {
        return Emergency.class;
    }
}

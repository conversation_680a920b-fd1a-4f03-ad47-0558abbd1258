package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabCmd;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabCmdProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_CMD;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabCmd.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabCmd tabCmd) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer(2 + 8 + 8);

            buf.writeShortLE(tabCmd.getCmd());
            buf.writeLongLE(tabCmd.getParam1());
            buf.writeShortLE(tabCmd.getParam2());
            buf.writeShortLE(tabCmd.getParam3());
            buf.writeShortLE(tabCmd.getParam4());
            buf.writeShortLE(tabCmd.getParam5());

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}

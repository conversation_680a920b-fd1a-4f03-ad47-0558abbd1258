package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.*;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PhaseTableTransProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PHASE_TABLE_TRANS;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            PhaseTableTrans phaseTableTrans= new PhaseTableTrans();
            objects.add(phaseTableTrans);

            //相位表编号
            phaseTableTrans.setPhaseTableNo(buf.readUnsignedByte() & 0xff);
            List<PhaseTableTransInfo> phaseTableTransInfos =  new ArrayList<>();
            phaseTableTrans.setPhaseTableTransInfos(phaseTableTransInfos);
            //相位表信息
            for (int i = 0; i < 64; i++) {
                PhaseTableTransInfo phaseTableTransInfo = new PhaseTableTransInfo();
                phaseTableTransInfos.add(phaseTableTransInfo);


                //失去路权配置
                {
                    List<LightTransition> loseLightTransitions =  new ArrayList<>();
                    phaseTableTransInfo.setLoseLightTransitions(loseLightTransitions);
                    for (int j = 0; j < 4; j++) {
                        LightTransition loseLightTransition = new LightTransition();
                        loseLightTransitions.add(loseLightTransition);
                        loseLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        loseLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }

                //获得路权配置
                {
                    List<LightTransition> obtainLightTransitions =  new ArrayList<>();
                    phaseTableTransInfo.setObtainLightTransitions(obtainLightTransitions);
                    for (int j = 0; j < 4; j++) {
                        LightTransition obtainLightTransition = new LightTransition();
                        obtainLightTransitions.add(obtainLightTransition);
                        obtainLightTransition.setColorType(buf.readUnsignedByte() & 0xff);
                        obtainLightTransition.setColorTime(buf.readUnsignedByte() & 0xff);
                    }
                }
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof PhaseTableTrans)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            PhaseTableTrans phaseTableTrans
                    = (PhaseTableTrans)objects.get(i);
            //相位表编号
            buf.writeByte(phaseTableTrans.getPhaseTableNo());
            //相位表信息
            List<PhaseTableTransInfo> phaseTableTransInfos = phaseTableTrans.getPhaseTableTransInfos();
            for (int k = 0; k < 64; k++) {
                PhaseTableTransInfo phaseTableTransInfo = phaseTableTransInfos.get(k);

                //失去路权配置
                {
                    List<LightTransition> loseLightTransitions = phaseTableTransInfo.getLoseLightTransitions();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(loseLightTransitions.get(j).getColorType());
                        buf.writeByte(loseLightTransitions.get(j).getColorTime());
                    }
                }

                //获得路权配置
                {
                    List<LightTransition> obtainLightTransitions = phaseTableTransInfo.getObtainLightTransitions();
                    for (int j = 0; j < 4; j++) {
                        buf.writeByte(obtainLightTransitions.get(j).getColorType());
                        buf.writeByte(obtainLightTransitions.get(j).getColorTime());
                    }
                }

            }

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 64 * ( 4*2*2 );
    }

    @Override
    public Class dataClazz() {
        return PhaseTableTrans.class;
    }
}

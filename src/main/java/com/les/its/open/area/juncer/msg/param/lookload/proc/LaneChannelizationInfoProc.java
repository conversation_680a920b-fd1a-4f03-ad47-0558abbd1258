package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Lane;
import com.les.its.open.area.juncer.msg.param.lookload.dto.LaneChannelizationInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class LaneChannelizationInfoProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_LANE_CHANNELIZATION_INFO;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            LaneChannelizationInfo laneChannelizationInfo = new LaneChannelizationInfo();
            objects.add(laneChannelizationInfo);
            //子路口号
            laneChannelizationInfo.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            //渠化信息
            List<Lane> lanes = new ArrayList<>();
            laneChannelizationInfo.setLanes(lanes);

            for (int i = 0; i < 128; i++) {
                Lane lane = new Lane();
                lanes.add(lane);
                lane.setLaneNo(buf.readUnsignedByte() & 0xff);
                lane.setDirection(buf.readUnsignedByte() & 0xff);
                lane.setFeature(buf.readUnsignedByte() & 0xff);
                lane.setAttribute(buf.readUnsignedByte() & 0xff);
                lane.setMovement(buf.readUnsignedByte() & 0xff);
                lane.setAzimuth(buf.readUnsignedByte() & 0xff);
            }

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
             if( !(object instanceof LaneChannelizationInfo)){
                 return Optional.empty();
             }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            LaneChannelizationInfo laneChannelizationInfo
                    = (LaneChannelizationInfo)objects.get(i);
            //子路口号
            buf.writeByte(laneChannelizationInfo.getCrossingSeqNo());
            for (int k = 0; k < 128; k++) {
                buf.writeByte(laneChannelizationInfo.getLanes().get(k).getLaneNo());
                buf.writeByte(laneChannelizationInfo.getLanes().get(k).getDirection());
                buf.writeByte(laneChannelizationInfo.getLanes().get(k).getFeature());
                buf.writeByte(laneChannelizationInfo.getLanes().get(k).getAttribute());
                buf.writeByte(laneChannelizationInfo.getLanes().get(k).getMovement());
                buf.writeByte(laneChannelizationInfo.getLanes().get(k).getAzimuth());
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 128 *(6);
    }

    @Override
    public Class dataClazz() {
        return LaneChannelizationInfo.class;
    }
}

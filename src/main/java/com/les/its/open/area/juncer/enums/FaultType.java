package com.les.its.open.area.juncer.enums;

/**
 * 故障类型枚举
 */
public enum FaultType {
    
    /**
     * 绿冲突故障
     */
    GREEN_CONFLICT(0x10, "绿冲突故障"),
    
    /**
     * 红绿冲突故障
     */
    RED_GREEN_CONFLICT(0x11, "红绿冲突故障"),
    
    /**
     * 红黄冲突故障
     */
    RED_YELLOW_CONFLICT(0x12, "红黄冲突故障"),
    
    /**
     * 黄绿冲突故障
     */
    YELLOW_GREEN_CONFLICT(0x13, "黄绿冲突故障"),
    
    /**
     * 红灯故障
     */
    RED_LIGHT_FAULT(0x20, "红灯故障"),
    
    /**
     * 黄灯故障
     */
    YELLOW_LIGHT_FAULT(0x21, "黄灯故障"),
    
    /**
     * 绿灯故障
     */
    GREEN_LIGHT_FAULT(0x22, "绿灯故障"),
    
    /**
     * 通讯故障
     */
    COMMUNICATION_FAULT(0x30, "通讯故障"),
    
    /**
     * 自检故障
     */
    SELF_CHECK_FAULT(0x40, "自检故障"),
    
    /**
     * 检测器故障
     */
    DETECTOR_FAULT(0x41, "检测器故障"),
    
    /**
     * 继电器故障
     */
    RELAY_FAULT(0x42, "继电器故障"),
    
    /**
     * 存储器故障
     */
    MEMORY_FAULT(0x43, "存储器故障"),
    
    /**
     * 计时故障
     */
    TIMER_FAULT(0x44, "计时故障"),
    
    /**
     * 主板故障
     */
    MAINBOARD_FAULT(0x45, "主板故障"),
    
    /**
     * 相位板(灯驱板)故障
     */
    PHASE_BOARD_FAULT(0x46, "相位板(灯驱板)故障"),
    
    /**
     * 检测板故障
     */
    DETECTION_BOARD_FAULT(0x47, "检测板故障"),
    
    /**
     * 配置故障
     */
    CONFIG_FAULT(0x50, "配置故障"),
    
    /**
     * 控制器时钟故障
     */
    CONTROLLER_CLOCK_FAULT(0x70, "控制器时钟故障"),
    
    /**
     * 通信门时钟故障
     */
    COMMUNICATION_GATE_CLOCK_FAULT(0x71, "通信门时钟故障"),
    
    /**
     * 紧急通信门时钟故障
     */
    EMERGENCY_COMMUNICATION_GATE_CLOCK_FAULT(0x72, "紧急通信门时钟故障");
    
    private final int code;
    private final String description;
    
    FaultType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据故障代码获取故障类型
     * @param code 故障代码
     * @return 故障类型枚举，如果未找到返回null
     */
    public static FaultType getByCode(int code) {
        for (FaultType faultType : values()) {
            if (faultType.getCode() == code) {
                return faultType;
            }
        }
        return null;
    }
    
    /**
     * 根据故障代码获取故障描述
     * @param code 故障代码
     * @return 故障描述，如果未找到返回"未知故障"
     */
    public static String getDescriptionByCode(int code) {
        FaultType faultType = getByCode(code);
        return faultType != null ? faultType.getDescription() : "未知故障";
    }
    
    @Override
    public String toString() {
        return String.format("FaultType{code=0x%02X, description='%s'}", code, description);
    }
}

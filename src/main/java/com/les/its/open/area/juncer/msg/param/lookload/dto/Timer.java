package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Timer implements DataIndexAble {
    // 定时器编号
    @NotNull(message = "{juncer.param.timer.timerNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.timer.timerNo.range}")
    private Integer timerNo;
    
    // 定时器名称
    @NotNull(message = "{juncer.param.timer.timerName.notNull}")
    private String timerName;
    
    // 时间间隔 (单位:100ms)
    @NotNull(message = "{juncer.param.timer.interval.notNull}")
    @Range(min = 0, max = 65535, message = "{juncer.param.timer.interval.range}")
    private Integer interval;

    @Override
    public int getDataNo() {
        return timerNo;
    }
}
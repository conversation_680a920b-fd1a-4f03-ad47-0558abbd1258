package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.LightTransition;
import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LampSequenceParam implements SgpTransAble {

    public static final String MqObjectId = "10004";


    // 相位灯序编号
    @NotNull(message = "{basic.param.lampSequence.lampSequenceNo.notNull}")
    @Range(min = 1, max = 64, message = "{basic.param.lampSequence.lampSequenceNo.range}")
    private Integer lampSequenceNo;

    /**
     *  相位灯序类型
     *  0:机动车
     *  1:行人
     *  2:非机动车
     *  3:绿箭头
     */
    @NotNull(message = "{basic.param.lampSequence.sequenceType.notNull}")
    @Range(min = 0, max = 3, message = "{basic.param.lampSequence.sequenceType.range}")
    private Integer sequenceType;

    /**
     * 相位灯序名称
     */
    @NotNull(message = "{basic.param.lampSequence.sequenceName.notNull}")
    private String sequenceName;

    /**
     * 待机灯色
     * 0:黄闪
     * 1:灭灯
     */
    @NotNull(message = "{basic.param.lampSequence.partTimeColor.notNull}")
    private Integer partTimeColor;


    // 失去路权配置
    @NotNull(message = "失去路权配置不能为空")
    @Size(min = 4, max = 4, message = "失去路权配置数量必须为4")
    @Valid
    private List<LightTransition> loseLightTransitions;

    // 获得路权配置
    @NotNull(message = "获得路权配置不能为空")
    @Size(min = 4, max = 4, message = "获得路权配置数量必须为4")
    @Valid
    private List<LightTransition> obtainLightTransitions;

    // 开机失去路权配置
    @NotNull(message = "{basic.param.lampSequence.loseLightTransitionStartups.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.lampSequence.loseLightTransitionStartups.size}")
    @Valid
    private List<LightTransition> loseLightTransitionStartups;
    
    // 开机获得路权配置 
    @NotNull(message = "{basic.param.lampSequence.obtainLightTransitionStartups.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.lampSequence.obtainLightTransitionStartups.size}")
    @Valid
    private List<LightTransition> obtainLightTransitionStartups;
    
    // 正常到待机失去路权配置
    @NotNull(message = "{basic.param.lampSequence.loseLightTransitionYellowFlashs.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.lampSequence.loseLightTransitionYellowFlashs.size}")
    @Valid
    private List<LightTransition> loseLightTransitionYellowFlashs;
    
    // 正常到待机获得路权配置
    @NotNull(message = "{basic.param.lampSequence.obtainLightTransitionYellowFlashs.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.lampSequence.obtainLightTransitionYellowFlashs.size}")
    @Valid  
    private List<LightTransition> obtainLightTransitionYellowFlashs;
    
    // 待机到正常失去路权配置
    @NotNull(message = "{basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.lampSequence.loseLightTransitionYellowFlashToNormals.size}")
    @Valid  
    private List<LightTransition> loseLightTransitionYellowFlashToNormals;
    
    // 待机到正常获得路权配置
    @NotNull(message = "{basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.lampSequence.obtainLightTransitionYellowFlashToNormals.size}")
    @Valid    
    private List<LightTransition> obtainLightTransitionYellowFlashToNormals;


    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return lampSequenceNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}
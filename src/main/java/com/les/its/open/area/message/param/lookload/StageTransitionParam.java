package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.StageTransTable;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * 阶段转换参数
 */
@Data
public class StageTransitionParam implements SgpTransAble {
    public static final String MqObjectId = "10008";

    /**
     * 阶段转移表
     */
    @NotNull(message = "{basic.param.stageTransition.stageTransTables.notNull}")
    @Size(min = 4, max = 4, message = "{basic.param.stageTransition.stageTransTables.size}")
    @Valid
    private List<StageTransTable> stageTransTables;

    /**
     * 模式阶段转移表
     * 固定长度255，索引表示控制方式编号，值表示使用的阶段转移表号
     */
    @NotNull(message = "{basic.param.stageTransition.modeTrans.notNull}")
    @Size(min = 255, max = 255, message = "{basic.param.stageTransition.modeTrans.size}")
    @DiscreteValuesList(
            min = 1,
            max = 4,
            message = "{basic.param.stageTransition.modeTrans.discreteValuesList}")
    private List<Integer> modeTrans;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabDeviceStatus;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabDeviceStatusProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_DEVICE_STATUS;
    }

    @Override
    public int oneItemSize() {
        return 14;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabDeviceStatus tabDeviceStatus = new TabDeviceStatus();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);
            // 检测器状态
            List<Integer> detectorStatus = new ArrayList<>();
            tabDeviceStatus.setDetectorStatus(detectorStatus);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    tabDeviceStatus.getDetectorStatus().add((data >> t) & 0x01);
                }
            }
            // 主控板状态
            tabDeviceStatus.setControlBoardStatus(buf.readUnsignedByte() & 0xff);
            // 检测板状态
            tabDeviceStatus.setDetectionBoardStatus(buf.readUnsignedByte() & 0xff);
            // 信息板状态
            tabDeviceStatus.setInformationBoardStatus(buf.readUnsignedByte() & 0xff);
            // 灯驱板状态
            tabDeviceStatus.setLampDriverStatus(buf.readUnsignedShortLE());
            // 机柜门状态
            tabDeviceStatus.setCabinetDoorStatus(buf.readUnsignedByte() & 0xff);

            buf.release();
            return Optional.of(tabDeviceStatus);
        }

        return Optional.empty();
    }
}

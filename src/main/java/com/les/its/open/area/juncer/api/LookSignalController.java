package com.les.its.open.area.juncer.api;

import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabLookService;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RequestMapping("/openles/look")
@Slf4j
@RestController
public class LookSignalController {


    @Autowired
    private TabLookService tabLookService;

    /**
     * 同步调看信号机参数
     * @param controllerId
     * @param paramMsgType
     * @param offset
     * @param count
     * @return
     */
    @GetMapping("/{controllerId}/{paramMsgType}/{offset}/{count}")
    public JsonResult<?> lookSignalController(@PathVariable String controllerId,
                                              @PathVariable String paramMsgType,
                                              @PathVariable int offset,
                                              @PathVariable int count) {

        ParamMsgType paramMsgType1 = ParamMsgType.fromCode(Integer.parseInt(paramMsgType, 16));
        if(paramMsgType1 == ParamMsgType.PARAM_UNKNOWN){
            return JsonResult.error(ResultCode.NOT_SUPPORT_LOOK_TYPE.message());
        }

        Optional<Object> looked = tabLookService.look(controllerId, paramMsgType1, offset, count);
        if(looked.isPresent()){
            return new JsonResult<>(true, "20000", "加载信号参数返回", looked.get());
        }else{
            return JsonResult.error(ResultCode.FAILED_LOOK.message());
        }
    }

}

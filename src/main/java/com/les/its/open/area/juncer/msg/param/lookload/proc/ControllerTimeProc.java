package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.ControllerTime;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ControllerTimeProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_CONTROLLER_TIME;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        ControllerTime controllerTime = new ControllerTime();

        //时区参数
        {
            int timeZone = buf.readIntLE();
            controllerTime.setTimeZone(timeZone);
        }

        //校时方式
        {
            controllerTime.setTimeSyncType(buf.readUnsignedByte() & 0xff);
        }

        //校时服务器地址类型 0 ipv4启用 1ipv6启用
        {
            controllerTime.setTimeServerType(buf.readUnsignedByte() & 0xff);
        }

        //ipv4
        {
            List<Short> datas = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                datas.add(buf.readUnsignedByte());
            }
            String ip = datas.stream().map(
                    data -> Integer.toString(data & 0xFF)
            ).collect(Collectors.joining("."));
            controllerTime.setTimeServerIpv4(ip);
        }

        //ipv6
        {
            List<Integer> datas = new ArrayList<>();
            for (int i = 0; i < 8; i++) {
                datas.add(buf.readUnsignedShort());
            }
            String ip = datas.stream().map( data -> String.format("%04x", data))
                    .collect(Collectors.joining(":"));
            controllerTime.setTimeServerIpv6(ip);
        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(controllerTime);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {

        //只能有一份数据，且数据类型为 ControllerTime
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof ControllerTime)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        ControllerTime controllerTime = (ControllerTime)objects.get(0);

        //时区
        {
            buf.writeIntLE(controllerTime.getTimeZone());
        }

        //校时方式
        {
            buf.writeByte(controllerTime.getTimeSyncType());
        }

        //0 ipv4启用 1ipv6启用
        {
            buf.writeByte(controllerTime.getTimeServerType());
        }

        //ntp服务器地址
        {
            //4位ip地址
            {
                String[] dataStrs = controllerTime.getTimeServerIpv4().split("\\.");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 10)
                ).toList();
                for (int i = 0; i < 4 && i < datas.size(); i++) {
                    buf.writeByte(datas.get(i));
                }
            }
        }

        //ntp服务器地址
        {
            String[] dataStrs = controllerTime.getTimeServerIpv6().split(":");
            List<Integer> datas = Arrays.stream(dataStrs).map(
                    dataStr -> Integer.valueOf(dataStr, 16)
            ).toList();
            for (int i = 0; i < 8 && i < datas.size(); i++) {
                buf.writeShort(datas.get(i));
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 4+1+1+ 4 + 16;
    }

    @Override
    public Class dataClazz() {
        return ControllerTime.class;
    }
}

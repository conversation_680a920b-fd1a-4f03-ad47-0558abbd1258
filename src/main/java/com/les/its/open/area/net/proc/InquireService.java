package com.les.its.open.area.net.proc;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabInBases;
import com.les.its.open.area.juncer.msg.link.TabInquireSystem;
import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.area.message.handler.status.TabControlModeHandler;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.MessagePublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class InquireService {

    @Autowired
    private ControllerAgentService controllerAgentService;


    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private TabControlModeHandler tabControlModeHandler;


    @Async(GlobalConfigure.JUNCER_ASYC_EXECUTOR)
    @Scheduled(initialDelay = 5000, fixedRate = 1000)
    public void inquire() {

        //遍历所有的信号机, 当信号机逻辑链路在线时, 发送心跳
        controllerAgentService.getControllerAgentIds().forEach(controllerId -> {

            //信号机链路正常、逻辑链路正常
            Optional<ControllerAgent> caOp = controllerAgentService.getControllerAgent(controllerId);
            if(caOp.isEmpty() || !caOp.get().isLogicLink()){
                return;
            }


            //每隔5s发送系统心跳
            int count = caOp.get().getHeartBeatCountSnd().incrementAndGet();

            if(count % 5 == 0) {

                //如果系统收到的信号机心跳应答大于3个周期，则认为信号机逻辑链路断开
                int revCount = caOp.get().getHeartBeatCountRev().incrementAndGet();
                if( revCount > 3){
                    log.warn("信号机{}逻辑链路3个周期未收到心跳,设置逻辑链路断线", controllerId);
                    caOp.get().setLogicLink(false);
                    tabControlModeHandler.sendLinkOff(controllerId);
                }else {
                    List<TabInBase> inBases = new ArrayList<>();
                    {
                        TabInquireSystem tabInquireSystem = new TabInquireSystem();
                        tabInquireSystem.setControllerId(controllerId);
                        tabInquireSystem.setLocalDateTime(LocalDateTime.now());
                        tabInquireSystem.setMsgType(MsgType.TAB_INQUIRE_SYSTEM);
                        inBases.add(tabInquireSystem);
                    }

                    TabInBases tabInBases = new TabInBases();
                    tabInBases.setTabInBases(inBases);
                    messagePublisher.publishMessage(tabInBases);

                    //记录当前心跳发送时间
                    boolean updateHbSndTime = caOp.get().updateHbSndTime();
                    if(!updateHbSndTime){
                        log.warn("信号机{}逻辑链路异常, 心跳发送时间未更新", controllerId);
                    }

                    //重新置0
                    if (count >= 5 * 1000) {
                        caOp.get().getHeartBeatCountSnd().set(0);
                    }
                }
            }
        });
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogicOutput implements DataIndexAble {
    // 逻辑输出编号
    @NotNull(message = "{juncer.param.logicOutput.logicOutputNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.logicOutput.logicOutputNo.range}")
    private Integer logicOutputNo;
    
    // 逻辑输出名称
    @NotNull(message = "{juncer.param.logicOutput.logicOutputName.notNull}")
    private String logicOutputName;

    @Override
    public int getDataNo() {
        return logicOutputNo;
    }
}
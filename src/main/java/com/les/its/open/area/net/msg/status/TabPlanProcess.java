package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabPlan;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabPlanProcess  extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_PLAN;
    }

    @Override
    public int oneItemSize() {
        return 2;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabPlan tabPlan = new TabPlan();
            tabPlan.setCrossingSeqNo((body[0] & 0xff));
            tabPlan.setPlanNo(body[1] & 0xff);
            return Optional.of(tabPlan);
        }

        return Optional.empty();
    }
}

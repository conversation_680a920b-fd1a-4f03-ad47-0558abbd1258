package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 方案控制应答
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabPlanCmdAck  extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;
    

    private TabPlanCmd tabPlanCmd;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_PLAN_CMD.getCode());
    }
}
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Detector implements DataIndexAble {
    // 检测器编号
    @NotNull(message = "检测器编号不能为空")
    @Range(min = 1, max = 64, message = "检测器编号有效范围[1,64]")
    private Integer detectorNo;
    
    // 检测器类型
    @NotNull(message = "检测器类型不能为空")
    private Integer detectorType;
    
    // 流量采集周期
    @NotNull(message = "流量采集周期不能为空")
    private Integer volumeCollectCycle;
    
    // 占有率采集周期
    @NotNull(message = "占有率采集周期不能为空")
    private Integer occupancyCollectCycle;
    
    // 安装位置
    @NotNull(message = "安装位置不能为空")
    private String installLocation;

    // 逻辑输入名称
    @NotNull(message = "逻辑输入名称不能为空")
    private String logicInputName;
    
    // 检测器延迟参数
    @NotNull(message = "检测器延迟参数不能为空")
    @Range(min = 0, max = 65535, message = "延迟参数有效范围[0,65535]")
    private Integer detectorDelay;
    
    // 检测器延长参数
    @NotNull(message = "检测器延长参数不能为空")
    @Range(min = 0, max = 255, message = "延长参数有效范围[0,255]")
    private Integer detectorExtend;
    
    // 检测器排队限制
    @NotNull(message = "检测器排队限制不能为空")
    @Range(min = 0, max = 255, message = "排队限制参数有效范围[0,255]")
    private Integer detectorQueueLimit;
    
    // 检测器输入取反标记
    @NotNull(message = "检测器输入取反标记不能为空")
    @Range(min = 0, max = 1, message = "取反标记有效范围[0,1]")
    private Integer detectorInvert;
    
    // 检测器无活动参数
    @NotNull(message = "检测器无活动参数不能为空")
    @Range(min = 0, max = 65535, message = "无活动参数有效范围[0,65535]")
    private Integer detectorNoActivity;
    
    // 检测器最长存在时间参数
    @NotNull(message = "检测器最长存在时间参数不能为空")
    @Range(min = 0, max = 65535, message = "最长存在时间参数有效范围[0,65535]")
    private Integer detectorMaxPresence;
    
    // 检测器异常计数参数
    @NotNull(message = "检测器异常计数参数不能为空")
    @Range(min = 0, max = 255, message = "异常计数参数有效范围[0,255]")
    private Integer detectorErraticCounts;
    
    // 检测器故障动作
    @NotNull(message = "检测器故障动作不能为空")
    @Range(min = 0, max = 1, message = "检测器故障动作有效范围[0,1]")
    private Integer detectorFailOperation;
    
    // 行人按钮持续时间参数
    @NotNull(message = "行人按钮持续时间参数不能为空")  
    @Range(min = 0, max = 255, message = "行人按钮持续时间参数有效范围[0,255]")
    private Integer pedestrianButtonPushTime;
    
    // 检测器配对检测器
    @NotNull(message = "检测器配对检测器不能为空")
    @Range(min = 0, max = 64, message = "配对检测器有效范围[0,1]")
    private Integer detectorPairedDetector;
    
    // 检测器配对检测器间距
    @NotNull(message = "检测器配对检测器间距不能为空")
    @Range(min = 0, max = 65535, message = "配对检测器间距有效范围[0,65535]")
    private Integer detectorPairedDetectorSpacing;
    
    // 检测器平均车辆长度
    @NotNull(message = "检测器平均车辆长度不能为空")
    @Range(min = 0, max = 4000, message = "检测器平均车辆长度有效范围[0,65535]")
    private Integer detectorAvgVehicleLength;
    
    // 检测器长度参数
    @NotNull(message = "检测器长度参数不能为空")
    @Range(min = 0, max = 4000, message = "检测器长度有效范围[0,65535]")
    private Integer detectorLength;

    // 状态保持
    @NotNull(message = "状态保持不能为空")
    @Range(min = 0, max = 2, message = "状态保持有效范围[0,2]")
    private Integer detectorState;

    // 需求记忆
    @NotNull(message = "需求记忆不能为空")
    @Range(min = 0, max = 1, message = "需求记忆有效范围[0,1]")
    private Integer detectorHold;

    // 持续时间
    @NotNull(message = "持续时间不能为空")
    @Range(min = 0, max = 255, message = "持续时间有效范围[0,255]")
    private Integer detectorDuration;
    
    // 检测器选项参数
    @NotNull(message = "检测器选项参数不能为空")
    @Range(min = 0, max = 255, message = "选项参数有效范围[0,255]")
    private Integer detectorOptions;
    
    // 检测器选项参数2
    @NotNull(message = "检测器选项参数2不能为空")   
    @Range(min = 0, max = 255, message = "选项参数2有效范围[0,255]")
    private Integer detectorOptions2;

    @Override
    public int getDataNo() {
        return detectorNo;
    }
}
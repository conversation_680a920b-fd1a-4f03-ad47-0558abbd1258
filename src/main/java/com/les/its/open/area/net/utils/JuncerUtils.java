package com.les.its.open.area.net.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.log.IPBasedLogger;
import com.les.its.open.area.net.log.LogPropertyFilter;
import com.les.its.open.bussiness.utils.LesUtils;
import com.les.its.open.config.JuncerConfigure;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.les.its.open.event.AckManager.NeedAck;
import com.les.its.open.event.AckManager.response.ResponseMessage;
import com.les.its.open.event.AckManager.response.ResponseStatus;
import com.les.its.open.event.MessageOuterPublisher;
import com.les.its.open.protocol.common.InterProtocol;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 发送数据项
 */
@Slf4j
@Service
public class JuncerUtils {

    public static final int SIZE_CHANGE_ABLE = -1;


    @Autowired
    private MessageOuterPublisher messageOuterPublisher;

    @Autowired
    private JuncerConfigure juncerConfigure;

    @Autowired
    private LogPropertyFilter logPropertyFilter;

    /**
     * 发送数据给信号机
     * @param ip
     * @param tabInBase
     * @return
     */
    public Optional<InvokeFuture> send2Controller(String ip, TabInBase tabInBase, boolean isSimSend, boolean ackDirect){

        //控制报文打印
        if(juncerConfigure.getDisableLogMsgs() ==  null
                || !juncerConfigure.getDisableLogMsgs().contains(tabInBase.getMsgType().getCode())) {
            IPBasedLogger.logMessage(ip, tabInBase.getNoArea(), tabInBase.getNoJunc(),
                    "发送[{}]-{}", tabInBase.getMsgType().getDescription(), JSON.toJSONString(tabInBase,
                            logPropertyFilter.getPropertyFilter()));
        }

        InterProtocol interProtocol = LesUtils.buildMessage(ip, tabInBase, isSimSend, ackDirect);
        /*发送报文*/
        return messageOuterPublisher.sendMessageOuter(interProtocol);
    }


    /**
     * 同步等待信号机数据应答
     * @param ip
     * @param tabInBase
     * @return
     */
    public Optional<Object> send2ControllerAndWaitForAck(String ip, TabInBase tabInBase, boolean isSimSend, boolean ackDirect){

        if(!(tabInBase instanceof NeedAck)){
            log.error("无法等待信号机应答，因为数据项不是需要等待应答的数据项-{}", tabInBase);
            return Optional.empty();
        }

        Optional<InvokeFuture> invokeFuture = send2Controller(ip, tabInBase, isSimSend, ackDirect);
        InvokeFuture future = invokeFuture.get();
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.debug("开始等待-{}应答-{}", JSONObject.toJSONString(tabInBase), startTime);
            ResponseMessage response = future.waitResponse();
            LocalDateTime endTime = LocalDateTime.now();
            long millis = Duration.between(endTime, startTime).toMillis();
            log.debug("请求数据-{} \n 收到了应答-{}, \n 花费时间-{}, \n 报文中时间-{} \n" +
                            "数据应答项-{}", JSONObject.toJSONString(tabInBase)
                    , endTime, millis,
                    System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);
            /*解析应答数据项*/
            if (response.getResponseStatus() != ResponseStatus.SUCCESS) {
                log.error("等待{}应答失败-{}", tabInBase, response.getResponseStatus().des());
            } else {
                log.debug("请求{}应答{}花费时间{}ms", tabInBase,
                        response.getResponseObject(), millis);
                return Optional.of(response.getResponseObject());
            }
        } catch (InterruptedException e) {
            log.error("等待异常", e);
        }

        return Optional.empty();
    }


}

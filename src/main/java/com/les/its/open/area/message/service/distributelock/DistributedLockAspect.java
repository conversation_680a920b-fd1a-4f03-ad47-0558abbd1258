package com.les.its.open.area.message.service.distributelock;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

/**
 * 分布式锁AOP切面
 */
@Aspect
@Component
@Slf4j
public class DistributedLockAspect {

    @Autowired
    private EnhancedDistributedLockService enhancedDistributedLockService;

    @Autowired
    private SpelExpressionParser spelExpressionParser;

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        String lockKey = parseLockKey(distributedLock.key(), joinPoint);
        return enhancedDistributedLockService.executeWithTimeout(
                lockKey,
                distributedLock.waitTime(),
                distributedLock.maxRunTime(),
                distributedLock.timeUnit(),
                () -> {
                    try {
                        return joinPoint.proceed();
                    } catch (Throwable e) {
                        throw new RuntimeException(e);
                    }
                }
        );
    }

    private String parseLockKey(String keyExpression, ProceedingJoinPoint joinPoint) {
        if (!keyExpression.startsWith("#{")) {
            return keyExpression;
        }

        // 解析SpEL表达式
        Expression expression = spelExpressionParser.parseExpression(keyExpression);
        EvaluationContext context = new StandardEvaluationContext();

        // 设置方法参数
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();

        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        return expression.getValue(context, String.class);
    }
}

package com.les.its.open.area.message.param.lookload.sub;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;      

/**
 * 逻辑输入
 */
@Data
public class LogicInput {
    /**
     * 逻辑输入编号
     */
    @NotNull(message = "{basic.param.logicInput.logicInputNo.notNull}")
    @Min(value = 1, message = "{basic.param.logicInput.logicInputNo.min}")
    @Max(value = 64, message = "{basic.param.logicInput.logicInputNo.max}")
    private Integer logicInputNo;

    /**
     * 逻辑输入名称
     */
    @NotNull(message = "{basic.param.logicInput.logicInputName.notNull}")
    private String logicInputName;

    /**
     * 安装位置
     */
    @NotNull(message = "{basic.param.logicInput.installLocation.notNull}")
    private String installLocation;

    /**
     * 状态信号
     * 0:否, 1: 开, 2:关
     */
    @NotNull(message = "{basic.param.logicInput.state.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.state.min}")
    @Max(value = 2, message = "{basic.param.logicInput.state.max}")
    private Integer state;

    /**
     * 需求记忆
     * 0:否, 1: 是
     */
    @NotNull(message = "{basic.param.logicInput.hold.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.hold.min}")
    @Max(value = 1, message = "{basic.param.logicInput.hold.max}")
    private Integer hold;

    /**
     * 持续时长
     */
    @NotNull(message = "{basic.param.logicInput.duration.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.duration.min}")
    @Max(value = 65535, message = "{basic.param.logicInput.duration.max}")
    private Integer duration;

    /**
     * 取反标记
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.invert.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.invert.min}")
    @Max(value = 1, message = "{basic.param.logicInput.invert.max}")
    private Integer invert;

    /**
     * 配对检测器
     */
    @NotNull(message = "{basic.param.logicInput.pairedDetector.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.pairedDetector.min}")
    @Max(value = 64, message = "{basic.param.logicInput.pairedDetector.max}")
    private Integer pairedDetector;

    /**
     * 配对检测器
     * 距离
     * 单位:0.01米
     */
    @NotNull(message = "{basic.param.logicInput.pairedDetectorSpacing.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.pairedDetectorSpacing.min}")
    @Max(value = 65535, message = "{basic.param.logicInput.pairedDetectorSpacing.max}")
    private Integer pairedDetectorSpacing;

    /**
     * 行人检测
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.ped.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.ped.min}")
    @Max(value = 1, message = "{basic.param.logicInput.ped.max}")
    private Integer ped;

    /**
     * 流量检测
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.volume.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.volume.min}")
    @Max(value = 1, message = "{basic.param.logicInput.volume.max}")
    private Integer volume;

    /**
     * 占有率检测
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.occupancy.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.occupancy.min}")
    @Max(value = 1, message = "{basic.param.logicInput.occupancy.max}")
    private Integer occupancy;

    /**
     * 速度检测
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.speed.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.speed.min}")
    @Max(value = 1, message = "{basic.param.logicInput.speed.max}")
    private Integer speed;

    /**
     * 排队检测
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.queue.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.queue.min}")
    @Max(value = 1, message = "{basic.param.logicInput.queue.max}")
    private Integer queue;

    /**
     * 数量检测
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.count.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.count.min}")
    @Max(value = 1, message = "{basic.param.logicInput.count.max}")
    private Integer count;


    /**
     * 身份识别
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.logicInput.identity.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.identity.min}")
    @Max(value = 1, message = "{basic.param.logicInput.identity.max}")
    private Integer identity;

    /**
     * 有效时间
     * 单位:秒
     */
    @NotNull(message = "{basic.param.logicInput.maxPresence.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.maxPresence.min}")
    @Max(value = 65535, message = "{basic.param.logicInput.maxPresence.max}")
    private Integer maxPresence;

    /**
     * 无效时间
     * 单位:秒
     */
    @NotNull(message = "{basic.param.logicInput.noActivity.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.noActivity.min}")
    @Max(value = 65535, message = "{basic.param.logicInput.noActivity.max}")
    private Integer noActivity;

    /**
     * 延长时间
     * 单位:0.1秒
     */
    @NotNull(message = "{basic.param.logicInput.extend.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.extend.min}")
    @Max(value = 255, message = "{basic.param.logicInput.extend.max}")
    private Integer extend;

    /**
     * 延迟时间
     * 单位:0.1秒
     */
    @NotNull(message = "{basic.param.logicInput.delay.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.delay.min}")
    @Max(value = 65535, message = "{basic.param.logicInput.delay.max}")
    private Integer delay;

    /**
     * 故障动作
     * 0:无动作, 1:呼叫最小绿-11-18时间最大允许1-8
     */
    @NotNull(message = "{basic.param.logicInput.failOperation.notNull}")
    @Min(value = 0, message = "{basic.param.logicInput.failOperation.min}")
    @Max(value = 1, message = "{basic.param.logicInput.failOperation.max}")
    private Integer failOperation;
} 
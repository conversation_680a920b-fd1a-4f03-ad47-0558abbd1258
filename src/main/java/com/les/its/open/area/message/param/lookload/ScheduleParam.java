package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 调度参数
 */
@Data
public class ScheduleParam implements SgpTransAble {
    public static final String MqObjectId = "10013";

    /**
     * 调度表编号
     */
    @NotNull(message = "{basic.param.schedule.scheduleNo.notNull}")
    @Min(value = 1, message = "{basic.param.schedule.scheduleNo.min}")
    @Max(value = 128, message = "{basic.param.schedule.scheduleNo.max}")
    private Integer scheduleNo;

    /**
     * 子路口号
     */
    @NotNull(message = "{basic.param.schedule.crossingSeqNo.notNull}")
    @Min(value = 1, message = "{basic.param.schedule.crossingSeqNo.min}")
    @Max(value = 8, message = "{basic.param.schedule.crossingSeqNo.max}")
    private Integer crossingSeqNo;

    /**
     * 优先级
     * 0最大优先级，优先级相同时调度表号由小变大
     */
    @NotNull(message = "{basic.param.schedule.priority.notNull}")
    @Min(value = 0, message = "{basic.param.schedule.priority.min}")
    @Max(value = 255, message = "{basic.param.schedule.priority.max}")
    private Integer priority;

    /**
     * 星期值
     */
    @NotNull(message = "{basic.param.schedule.week.notNull}")
    private Integer week;

    /**
     * 月份
     */
    @NotNull(message = "{basic.param.schedule.month.notNull}")
    private Integer month;

    /**
     * 日期
     */
    @NotNull(message = "{basic.param.schedule.day.notNull}")
    private Integer day;

    /**
     * 日计划号
     */
    @NotNull(message = "{basic.param.schedule.dayPlanNo.notNull}")
    @Min(value = 1, message = "{basic.param.schedule.dayPlanNo.min}")
    @Max(value = 128, message = "{basic.param.schedule.dayPlanNo.max}")
    private Integer dayPlanNo;

    /**
     * 调度计划名称
     */
    @NotNull(message = "{basic.param.schedule.scheduleName.notNull}")
    private String scheduleName;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return scheduleNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
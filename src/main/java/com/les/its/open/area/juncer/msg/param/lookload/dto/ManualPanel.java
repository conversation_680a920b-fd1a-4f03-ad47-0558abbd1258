package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 手控控制
 */
@Data
public class ManualPanel implements DataIndexAble {
    /**
     * 手控按钮编号
     */
    @NotNull(message = "{juncer.param.manualPanel.buttonNo.notNull}")
    @Range(min = 1, max = 8, message = "{juncer.param.manualPanel.buttonNo.range}")
    private Integer buttonNo;

    /**
     * 呼叫阶段编号
     * 针对每个子路口
     */
    @NotNull(message = "{juncer.param.manualPanel.callStageNo.notNull}")
    @Size(min = 8, max = 8, message = "{juncer.param.manualPanel.callStageNo.size}")
    @DiscreteValuesList(min = 0, max = 64, message = "{juncer.param.manualPanel.callStageNo.range}")
    private List<Integer> callStageNo;

    @Override
    public int getDataNo() {
        return buttonNo;
    }
}
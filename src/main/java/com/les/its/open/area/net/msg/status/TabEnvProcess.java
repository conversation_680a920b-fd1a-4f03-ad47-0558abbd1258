package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabEnv;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabEnvProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_ENV;
    }

    @Override
    public int oneItemSize() {
        return 8;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabEnv tabEnv = new TabEnv();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            // 电压
            tabEnv.setVoltage(buf.readUnsignedShortLE());
            // 电流
            tabEnv.setCurrent(buf.readUnsignedShortLE());
            // 温度
            tabEnv.setTemperature(buf.readUnsignedByte() & 0xff);
            // 湿度
            tabEnv.setHumidity(buf.readUnsignedByte() & 0xff);
            // 水浸
            tabEnv.setWaterLeak(buf.readUnsignedByte() & 0xff);
            // 烟雾
            tabEnv.setSmoke(buf.readUnsignedByte() & 0xff);

            buf.release();
            return Optional.of(tabEnv);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabCmd;
import com.les.its.open.area.juncer.msg.cmd.TabCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabCmdAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return (2 + 2 + 8);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabCmdAck tabCmdAck = new TabCmdAck();
            tabCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabCmd tabCmd = new TabCmd();
            tabCmdAck.setTabCmd(tabCmd);
            tabCmd.setCmd(buf.readUnsignedShortLE());
            tabCmd.setParam1(buf.readLongLE());
            tabCmd.setParam2(buf.readUnsignedShortLE());
            tabCmd.setParam3(buf.readUnsignedShortLE());
            tabCmd.setParam4(buf.readUnsignedShortLE());
            tabCmd.setParam5(buf.readUnsignedShortLE());

            buf.release();

            return Optional.of(tabCmdAck);
        }

        return Optional.empty();
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StageTransitionConstraint {
    // 相位阶段编号
    @NotNull(message = "{juncer.param.stageTransitionConstraint.stageNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.stageTransitionConstraint.stageNo.range}")
    private Integer stageNo;
    
    // 相位阶段过渡约束值
    @NotNull(message = "{juncer.param.stageTransitionConstraint.transitionConstraints.notNull}")
    @Size(min = 64, max = 64, message = "{juncer.param.stageTransitionConstraint.transitionConstraints.size}")
    @DiscreteValuesList(acceptedValues = {255},
            min = 0,
            max = 64,
            message = "{juncer.param.stageTransitionConstraint.transitionConstraints.range}")
    private List<Integer> transitionConstraints;
} 
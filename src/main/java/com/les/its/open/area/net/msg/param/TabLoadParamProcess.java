package com.les.its.open.area.net.msg.param;

import com.alibaba.fastjson2.JSONArray;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.TabLoadParam;
import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import com.les.its.open.bussiness.bean.P1049Infos;
import com.les.its.open.bussiness.process.P1049InfosProcess;
import com.les.its.open.event.MessagePublisher;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Component
@Slf4j
public class TabLoadParamProcess extends TabInnerBaseMsgProcess implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private final Map<ParamMsgType, LookLoadBase> paramLookLoadBase = new ConcurrentHashMap<>();

    private final DataValidatorFactory dataValidatorFactory;

    private final MessagePublisher messagePublisher;

    public TabLoadParamProcess(DataValidatorFactory dataValidatorFactory, MessagePublisher messagePublisher) {
        this.dataValidatorFactory = dataValidatorFactory;
        this.messagePublisher = messagePublisher;
    }

    @PostConstruct
    public void init() {
        Arrays.stream(ParamMsgType.values()).forEach(
                paramMsgType -> {
                    Map<String, LookLoadBase> beansOfType = applicationContext.getBeansOfType(LookLoadBase.class);
                    beansOfType.keySet().stream().forEach(key ->
                    {
                        paramLookLoadBase.put(beansOfType.get(key).msgType(), beansOfType.get(key));
                    });
                }
        );
    }

    @Override
    public MsgType msgType() {
        return MsgType.TAB_LOAD_PARAM;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
         int paramType = ( (msg3 & 0xff) << 8) + (msg4 & 0xff) ;

        ParamMsgType paramMsgType = ParamMsgType.fromCode(paramType);
        LookLoadBase lookLoadBase = paramLookLoadBase.get(paramMsgType);
        if(lookLoadBase == null){
            return Optional.empty();
        }
        return Optional.of(lookLoadBase.dataClazz());
    }
    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabLoadParam tabLoadParam) {
            msgCode.set(tabLoadParam.getLoadId());

            if(ParamMsgType.PARAM_UNKNOWN == tabLoadParam.getParamMsgType()) {
                return Optional.empty();
            }

            LookLoadBase lookLoadBase = paramLookLoadBase.get(tabLoadParam.getParamMsgType());
            if(lookLoadBase == null){
                return Optional.empty();
            }

            //数据参数校验
            tabLoadParam.getObjects().forEach(
                    object -> {
                        StringBuilder stringBuilder = new StringBuilder();
                        boolean validateData = dataValidatorFactory.validateData(object, stringBuilder);
                        if(!validateData){
                            log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", tabInBase.getControllerId(),
                                    stringBuilder.toString(), object);
                        }
                    }
            );


            Optional<byte[]> sendBytes = lookLoadBase.procLoad(tabLoadParam.getObjects());

            //发布数据存储数据项
            if(sendBytes.isPresent()){
                P1049Infos p1049Infos = P1049InfosProcess.build(tabLoadParam.getObjects(), tabInBase.getControllerId());
                messagePublisher.publishMessage(p1049Infos);
            }

            return sendBytes;

        }else {
            return Optional.empty();
        }
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     *
     * @param paramMsgType
     * @param data
     * @return
     */
    public Optional<List<Object>> change(ParamMsgType paramMsgType, String data) {
        LookLoadBase lookLoadBase = paramLookLoadBase.get(paramMsgType);
        if(lookLoadBase == null){
            return Optional.empty();
        }

        List list = JSONArray.parseArray(data, lookLoadBase.dataClazz());
        List<Object> objects = Arrays.asList(list.toArray());
        return Optional.of(objects);
    }

}

package com.les.its.open.area.juncer.msg;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.message.MqMessageProcess;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.MsgType;
import jakarta.validation.constraints.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
@Slf4j
public class DataItemGenerator {

    @Autowired
    private MqMessageProcess mqMessageProcess;

    @Autowired
    private ControllerService controllerService;


    /**
     * 生成默认mq 命令数据项
     * @param controllerId
     * @param objectId
     * @return
     */
    public Optional<Object> genDefaultCmdMqMsg(String controllerId, String objectId) {

        Optional<Object> objectOp = genDefaultCmdByObjectId(controllerId, objectId);
        if(objectOp.isEmpty()){
            return Optional.empty();
        }

        List<Object> objects = new ArrayList<>();
        objects.add(objectOp.get());
        MqMessage mqMessage = OpenLesMqUtils.buildSimSetMqMsgWithObjects(controllerId, objectId, objects);
        return Optional.of(mqMessage);

    }

    /**
     * 生成默认mq 调看数据项
     * @param controllerId
     * @param objectId
     * @return
     */
    public Optional<Object> genDefaultLookMqMsg(String controllerId, String objectId) {
        List<Object> objects = new ArrayList<>();
        objects.add(1);
        MqMessage mqMessage = OpenLesMqUtils.buildSimQueryMqMsg(controllerId, objectId, objects);
        return Optional.of(mqMessage);

    }

    /**
     * 生成默认数据想
     * @param objectId
     * @return
     */
    public Optional<Object> genDefaultCmdByObjectId(String controllerId, String objectId) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if(signalInfoOp.isEmpty()){
            return Optional.empty();
        }

        Optional<MqMsgBaseHandler> msgBaseHandlerOp = mqMessageProcess.getMqMsgBaseHandlerMap().values().stream().filter(
                mqMsgBaseHandler -> Integer.valueOf(mqMsgBaseHandler.getObjectId()).intValue() == Integer.valueOf(objectId).intValue()
        ).findAny();

        if(msgBaseHandlerOp.isEmpty()){
            return  Optional.empty();
        }

         {
             MqMsgBaseHandler mqMsgBaseHandler = msgBaseHandlerOp.get();
             Optional<CmdMqObject> mqObjectOp = CmdMqObject.getTypeByObjectId(mqMsgBaseHandler.getObjectId());
                    if(mqObjectOp.isPresent()){
                        Object data = this.generateDefaultData(mqMsgBaseHandler.dataType());
                        log.error("data-{}-{}-{}", mqObjectOp.get().objectId(),
                                mqObjectOp.get().des(), data);


                        Optional<MsgType> msgTypeOp = MsgType.parseMsgType(((mqObjectOp.get().value() >> 24) & 0xff),
                                ((mqObjectOp.get().value() >> 16) & 0xff),
                                ((mqObjectOp.get().value() >> 8) & 0xff),
                                ((mqObjectOp.get().value() & 0xff)));

                        if(data instanceof TabInBase tabInBase){
                            tabInBase.setControllerId(controllerId);
                            tabInBase.setNoArea(signalInfoOp.get().getNoArea());
                            tabInBase.setNoJunc(signalInfoOp.get().getNoJunc());
                            tabInBase.setIp(signalInfoOp.get().getIp());
                            msgTypeOp.ifPresent(tabInBase::setMsgType);
                            msgTypeOp.ifPresent(msgType -> tabInBase.setMsgTypeCode(msgType.getCode()));
                            tabInBase.setLocalDateTime(LocalDateTime.now());
                        }

                        return Optional.of(data);
                    }
         }

        return Optional.empty();
    }

    /**
     * 为指定类生成默认数据对象
     */
    public <T> T generateDefaultData(Class<T> clazz) {
        try {
            T instance = clazz.getDeclaredConstructor().newInstance();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                Object defaultValue = generateDefaultValue(field);
                if (defaultValue != null) {
                    field.set(instance, defaultValue);
                }
            }

            return instance;
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate default data for " + clazz.getSimpleName(), e);
        }
    }

    /**
     * 根据字段约束生成默认值
     */
    private Object generateDefaultValue(Field field) {
        Class<?> fieldType = field.getType();

        // 处理字符串类型
        if (fieldType == String.class) {
            return generateStringValue(field);
        }

        // 处理数值类型
        if (isNumericType(fieldType)) {
            return generateNumericValue(field, fieldType);
        }

        // 处理布尔类型
        if (fieldType == Boolean.class || fieldType == boolean.class) {
            return Boolean.TRUE;
        }

        // 处理日期类型
        if (fieldType == LocalDate.class) {
            return LocalDate.now();
        }

        if (fieldType == LocalDateTime.class) {
            return LocalDateTime.now();
        }

        // 处理集合类型
        if (Collection.class.isAssignableFrom(fieldType)) {
            return generateCollectionValue(field, fieldType);
        }

        return null;
    }

    /**
     * 生成字符串默认值
     */
    private String generateStringValue(Field field) {
        Size size = field.getAnnotation(Size.class);
        NotEmpty notEmpty = field.getAnnotation(NotEmpty.class);
        NotBlank notBlank = field.getAnnotation(NotBlank.class);
        Pattern pattern = field.getAnnotation(Pattern.class);
        Email email = field.getAnnotation(Email.class);

        // 邮箱格式
        if (email != null) {
            return "<EMAIL>";
        }

        // 正则表达式约束
        if (pattern != null) {
            return generateStringByPattern(pattern.regexp());
        }

        // 长度约束
        int minLength = 1;
        int maxLength = 10;

        if (size != null) {
            minLength = Math.max(size.min(), minLength);
            maxLength = size.max() == Integer.MAX_VALUE ? 10 : Math.min(size.max(), maxLength);
        }

        if (notEmpty != null || notBlank != null) {
            minLength = Math.max(minLength, 1);
        }

        return generateRandomString(minLength, maxLength);
    }

    /**
     * 生成数值默认值
     */
    private Object generateNumericValue(Field field, Class<?> fieldType) {
        Min min = field.getAnnotation(Min.class);
        Max max = field.getAnnotation(Max.class);
        DecimalMin decimalMin = field.getAnnotation(DecimalMin.class);
        DecimalMax decimalMax = field.getAnnotation(DecimalMax.class);
        Positive positive = field.getAnnotation(Positive.class);
        PositiveOrZero positiveOrZero = field.getAnnotation(PositiveOrZero.class);
        DigitsInSet digitsInSet = field.getAnnotation(DigitsInSet.class);
        Range range = field.getAnnotation(Range.class);

        // 处理DigitsInSet注解
        if (digitsInSet != null && digitsInSet.acceptedValues().length > 0) {
            int[] acceptedValues = digitsInSet.acceptedValues();
            // 随机选择一个允许的值
            return acceptedValues[new Random().nextInt(acceptedValues.length)];
        }


        long minValue = 0;
        long maxValue = 100;

        if (min != null) {
            minValue = min.value();
        }
        if (max != null) {
            maxValue = max.value();
        }
        if (decimalMin != null) {
            minValue = Long.parseLong(decimalMin.value());
        }
        if (decimalMax != null) {
            maxValue = Long.parseLong(decimalMax.value());
        }
        if (positive != null) {
            minValue = Math.max(minValue, 1);
        }
        if (positiveOrZero != null) {
            minValue = Math.max(minValue, 0);
        }

        // 范围数据项
        if (range != null ) {
            minValue =  range.min() ;
            maxValue =  range.max();
        }

        long value = minValue + (long) (Math.random() * (maxValue - minValue));

        // 根据字段类型返回对应的数值
        if (fieldType == Integer.class || fieldType == int.class) {
            return (int) value;
        }
        if (fieldType == Long.class || fieldType == long.class) {
            return value;
        }
        if (fieldType == Double.class || fieldType == double.class) {
            return (double) value;
        }
        if (fieldType == Float.class || fieldType == float.class) {
            return (float) value;
        }
        if (fieldType == BigDecimal.class) {
            return BigDecimal.valueOf(value);
        }

        return value;
    }

    /**
     * 生成集合默认值
     */
    private Object generateCollectionValue(Field field, Class<?> fieldType) {
        Size size = field.getAnnotation(Size.class);
        NotEmpty notEmpty = field.getAnnotation(NotEmpty.class);
        DiscreteValuesList discreteValuesList = field.getAnnotation(DiscreteValuesList.class);

        int minSize = (notEmpty != null) ? 1 : 0;
        int maxSize = 3;

        if (size != null) {
            minSize = size.min();
            maxSize = size.max();
        }

        int actualSize = minSize;

        //如果最小最大一致表示是固定长度的数组
        if(minSize != maxSize) {
            actualSize = minSize + (int) (Math.random() * (maxSize - minSize + 1));
        }

        // 获取泛型参数类型
        Class<?> elementType = getGenericType(field);

        // 生成集合元素
        List<Object> elements = IntStream.range(0, actualSize)
                .mapToObj(i -> generateElementValue(elementType, i, discreteValuesList))
                .collect(Collectors.toList());

        // 根据集合类型返回相应的实现
        if (List.class.isAssignableFrom(fieldType) || Collection.class == fieldType) {
            return new ArrayList<>(elements);
        }

        if (Set.class.isAssignableFrom(fieldType)) {
            return new HashSet<>(elements);
        }

        if (fieldType.isArray()) {
            return convertToArray(elements, elementType);
        }

        return elements;
    }

    /**
     * 获取泛型参数类型
     */
    private Class<?> getGenericType(Field field) {
        Type genericType = field.getGenericType();

        if (genericType instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericType;
            Type[] actualTypes = parameterizedType.getActualTypeArguments();

            if (actualTypes.length > 0 && actualTypes[0] instanceof Class) {
                return (Class<?>) actualTypes[0];
            }
        }

        // 处理数组类型
        if (field.getType().isArray()) {
            return field.getType().getComponentType();
        }

        // 默认返回String类型
        return String.class;
    }

    /**
     * 根据元素类型生成单个元素值
     */
    private Object generateElementValue(Class<?> elementType, int index, DiscreteValuesList discreteValuesList) {
        if (elementType == String.class) {
            return "item" + (index + 1);
        }

        if (elementType == Integer.class || elementType == int.class) {
            if(discreteValuesList != null){
                if(discreteValuesList.acceptedValues() != null){
                    return discreteValuesList.acceptedValues()[0];
                } else if(discreteValuesList.min() != Integer.MIN_VALUE){
                    return discreteValuesList.min();
                }
            }
            return index + 1;
        }

        if (elementType == Long.class || elementType == long.class) {
            if(discreteValuesList != null){
                if(discreteValuesList.acceptedValues() != null){
                    return discreteValuesList.acceptedValues()[0];
                } else if(discreteValuesList.min() != Integer.MIN_VALUE){
                    return discreteValuesList.min();
                }
            }
            return (long) (index + 1);
        }

        if (elementType == Double.class || elementType == double.class) {
            return (double) (index + 1);
        }

        if (elementType == Float.class || elementType == float.class) {
            return (float) (index + 1);
        }

        if (elementType == BigDecimal.class) {
            return BigDecimal.valueOf(index + 1);
        }

        if (elementType == Boolean.class || elementType == boolean.class) {
            return index % 2 == 0;
        }

        if (elementType == LocalDate.class) {
            return LocalDate.now().plusDays(index);
        }

        if (elementType == LocalDateTime.class) {
            return LocalDateTime.now().plusHours(index);
        }

        // 处理枚举类型
        if (elementType.isEnum()) {
            Object[] enumConstants = elementType.getEnumConstants();
            return enumConstants[index % enumConstants.length];
        }

        // 处理复杂对象类型 - 递归生成
        if (!elementType.isPrimitive() && !elementType.getName().startsWith("java.")) {
            try {
                return generateDefaultData(elementType);
            } catch (Exception e) {
                // 如果无法生成复杂对象，返回null
                return null;
            }
        }

        return "item" + (index + 1);
    }

    /**
     * 将List转换为数组
     */
    private Object convertToArray(List<Object> elements, Class<?> componentType) {
        if (componentType == String.class) {
            return elements.toArray(new String[0]);
        }

        if (componentType == Integer.class || componentType == int.class) {
            return elements.stream().mapToInt(o -> (Integer) o).toArray();
        }

        if (componentType == Long.class || componentType == long.class) {
            return elements.stream().mapToLong(o -> (Long) o).toArray();
        }

        if (componentType == Double.class || componentType == double.class) {
            return elements.stream().mapToDouble(o -> (Double) o).toArray();
        }

        // 对于其他类型，使用反射创建数组
        try {
            Object array = java.lang.reflect.Array.newInstance(componentType, elements.size());
            for (int i = 0; i < elements.size(); i++) {
                java.lang.reflect.Array.set(array, i, elements.get(i));
            }
            return array;
        } catch (Exception e) {
            return elements.toArray();
        }
    }

    /**
     * 根据正则表达式生成字符串（简化版本）
     */
    private String generateStringByPattern(String regex) {
        // 这里可以使用更复杂的正则表达式解析库
        // 简化处理一些常见模式
        if (regex.contains("\\d")) {
            return "123456";
        }
        if (regex.contains("[a-zA-Z]")) {
            return "abcdef";
        }
        return "default";
    }

    /**
     * 生成随机字符串
     */
    private String generateRandomString(int minLength, int maxLength) {
        int length = minLength + (int) (Math.random() * (maxLength - minLength + 1));
        StringBuilder sb = new StringBuilder();
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt((int) (Math.random() * chars.length())));
        }

        return sb.toString();
    }

    /**
     * 判断是否为数值类型
     */
    private boolean isNumericType(Class<?> type) {
        return type == Integer.class || type == int.class ||
                type == Long.class || type == long.class ||
                type == Double.class || type == double.class ||
                type == Float.class || type == float.class ||
                type == BigDecimal.class;
    }
}
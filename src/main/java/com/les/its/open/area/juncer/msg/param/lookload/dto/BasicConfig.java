package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicConfig implements DataIndexAble {
    // 系统控制最大时间
    @NotNull(message = "系统控制最大时间不能为空")
    @Range(min = 0, max = 1800, message = "系统控制最大时间有效范围[0,1800]")
    private Integer maxSystemControlTime;
    
    // 协调零点
    @NotNull(message = "协调零点不能为空")
    @Range(min = 0, max = 1439, message = "协调零点有效范围[0,1440)")
    private Integer systemCordRef;
    
    // 看门狗最大绿
    @NotNull(message = "看门狗最大绿不能为空")
    @Range(min = 0, max = 255, message = "看门狗最大绿有效范围[0,255]")
    private Integer maxGreenWatchDog;
    
    // 启动绿间隔
    @NotNull(message = "启动绿间隔不能为空")
    @Range(min = 0, max = 32, message = "启动绿间隔有效范围[0,32]")
    private Integer startInterGreen;
    
    // 可变标记个数
    @NotNull(message = "可变标记个数不能为空")
    @Range(min = 0, max = 32, message = "可变标记有效范围[0,32]")
    private Integer switchedSigns;
    
    // 阶段跳转禁止
    @NotNull(message = "阶段跳转禁止不能为空")
    @Range(min = 0, max = 1, message = "阶段跳转禁止有效范围[0,1]")
    private Integer stageSkipProhibited;

    // 降级动作
    @NotNull(message = "降级动作不能为空")
    @Range(min = 0, max = 1, message = "降级动作有效范围[0,1]")
    private Integer degradeAction;

    // 紧急优先执行绿看门狗
    @NotNull(message = "紧急优先执行绿看门狗不能为空")
    @Range(min = 0, max = 255, message = "紧急优先执行绿看门狗有效范围[0,255]")
    private Integer hurryCallExecuteWatchDog;

    // 黄灯行人灯态标志
    @NotNull(message = "黄灯行人灯态标志不能为空")
    @Range(min = 0, max = 1, message = "黄灯行人灯态标志有效范围[0,1]")
    private Integer yellowAsWaitIndicator;

    @NotNull(message = "最大手动控制时间不能为空")
    @Range(min = 0, max = 255, message = "最大手动控制时间有效范围[0,255]")
    private Integer maxManualControlTime;

    @NotNull(message = "闪灯亮灯时长不能为空")
    @Range(min = 400, max = 1000, message = "闪灯亮灯时长有效范围[400,1000]")
    private Integer flashOnTime;

    @NotNull(message = "闪灯灭灯时长不能为空")
    @Range(min = 400, max = 1000, message = "闪灯灭灯时长有效范围[400,1000]")
    private Integer flashOffTime;

    @Override
    public int getDataNo() {
        return 1;
    }
}
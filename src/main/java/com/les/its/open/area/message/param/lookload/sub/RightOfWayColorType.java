package com.les.its.open.area.message.param.lookload.sub;

import lombok.Getter;

import java.io.Serializable;

/**
 * @PackageName : com.les.ads.ds.enums
 * @ClassName : RightOfWayColorType
 * @Description : 过渡灯色
 * <AUTHOR> Alan_PC
 * @Date: 2025/4/9 16:04
 * @Version: 1.0
 */
@Getter
public enum RightOfWayColorType implements Serializable {

    Blank(0, "灭灯"),
    <PERSON>(1, "红灯"),
    <PERSON>(2, "黄灯"),
    <PERSON>(3, "绿灯"),
    Red_Yellow(4, "红黄"),
    Red_FY(5, "红灯_黄闪"),
    FR(6, "红闪"),
    FR_Yellow(7, "红闪_黄灯"),
    FR_FY(8, "红闪_黄闪"),
    Yellow_Green(9, "黄绿"),
    Yellow_FG(10, "黄灯_绿闪"),
    FY_Green(11, "黄闪_绿灯"),
    FG(12, "绿闪"),
    FY(13, "黄闪"),
    FY_FG(14, "黄闪_绿闪"),
    UN_KNOWN(99, "未知");

    private final Integer colorType;
    private final String colorName;

    RightOfWayColorType(Integer colorType, String colorName) {
        this.colorType = colorType;
        this.colorName = colorName;
    }

    /**
     * 具有通行权的灯色状态
     */
    public static final RightOfWayColorType[] RIGHT_OF_WAY_COLOR_TYPES = new RightOfWayColorType[]{
            RightOfWayColorType.Yellow,
            RightOfWayColorType.Green,
            RightOfWayColorType.Yellow_Green,
            RightOfWayColorType.Yellow_FG,
            RightOfWayColorType.FY_Green,
            RightOfWayColorType.FG,
            RightOfWayColorType.FY_FG
    };

    public static RightOfWayColorType parse(Integer colorType) {
        for (RightOfWayColorType rightOfWayColorType : RightOfWayColorType.values()) {
            if (rightOfWayColorType.getColorType().equals(colorType)) {
                return rightOfWayColorType;
            }
        }
        return UN_KNOWN;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.BasicParam;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class BasicParamProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_BASIC_PARAM;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        BasicParam basicParam= new BasicParam();

        basicParam.setMaxLightsGroupNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxPhaseNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxPhaseTableNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxStageNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxPlanNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxDayPlanNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxScheduleNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxDetectorNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxEmergencyNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxPriorityNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxLogicOutNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxTimerNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxUserFlagNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxConditionNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxActionNum(buf.readUnsignedByte() & 0xff);
        basicParam.setMaxSwitchedSigns(buf.readUnsignedByte() & 0xff);

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(basicParam);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof BasicParam)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        BasicParam basicParam = (BasicParam)objects.get(0);
        buf.writeByte(basicParam.getMaxLightsGroupNum());
        buf.writeByte(basicParam.getMaxPhaseNum());
        buf.writeByte(basicParam.getMaxPhaseTableNum());
        buf.writeByte(basicParam.getMaxStageNum());
        buf.writeByte(basicParam.getMaxPlanNum());
        buf.writeByte(basicParam.getMaxDayPlanNum());
        buf.writeByte(basicParam.getMaxScheduleNum());
        buf.writeByte(basicParam.getMaxDetectorNum());
        buf.writeByte(basicParam.getMaxEmergencyNum());
        buf.writeByte(basicParam.getMaxPriorityNum());
        buf.writeByte(basicParam.getMaxLogicOutNum());
        buf.writeByte(basicParam.getMaxTimerNum());
        buf.writeByte(basicParam.getMaxUserFlagNum());
        buf.writeByte(basicParam.getMaxConditionNum());
        buf.writeByte(basicParam.getMaxActionNum());
        buf.writeByte(basicParam.getMaxSwitchedSigns());

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 10 + 5 + 1;
    }

    @Override
    public Class dataClazz() {
        return BasicParam.class;
    }
}

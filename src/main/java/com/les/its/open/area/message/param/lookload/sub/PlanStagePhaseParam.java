package com.les.its.open.area.message.param.lookload.sub;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 相位运行早截断参数
 */
@Data
public class PlanStagePhaseParam {
    /**
     * 相位编号
     * [1,64]
     */
    @NotNull(message = "{basic.param.planStagePhase.phaseNo.notNull}")
    @Min(value = 1, message = "{basic.param.planStagePhase.phaseNo.min}")
    @Max(value = 64, message = "{basic.param.planStagePhase.phaseNo.max}")
    private Integer phaseNo;

    /**
     * 迟后时长
     * 单位秒
     */
    @NotNull(message = "{basic.param.planStagePhase.laggingTime.notNull}")
    @Min(value = 0, message = "{basic.param.planStagePhase.laggingTime.min}")
    @Max(value = 255, message = "{basic.param.planStagePhase.laggingTime.max}")
    private Integer laggingTime;

    /**
     * 早截时长
     * 单位秒
     */
    @NotNull(message = "{basic.param.planStagePhase.delayCutOffTime.notNull}")
    @Min(value = 0, message = "{basic.param.planStagePhase.delayCutOffTime.min}")
    @Max(value = 255, message = "{basic.param.planStagePhase.delayCutOffTime.max}")
    private Integer delayCutOffTime;
} 
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class TscBaseInfo implements DataIndexAble {

    // 安装路口 (128字节)
    @NotNull(message = "安装路口不能为空")
    private String installIntersection;

    // 信号机ID (4字节)
    @NotNull(message = "信号机ID不能为空")
    private long tscId;

    // 信号机控制路口数量 (1字节)
    @Range(min = 1, max = 8, message = "信号机控制路口有效范围是[1,8]")
    private int controlledJunctionNum;

    // 经度 (4字节) - 传输值：*10,000,000
    @Range(min = -180, max = 180, message = "经度有效范围是[-180,180]")
    private double longitude;

    // 纬度 (4字节) - 传输值：*10,000,000
    @Range(min = -90, max = 90, message = "纬度有效范围是[-90,90]")
    private double latitude;

    @Override
    public int getDataNo() {
        return 1;
    }
}


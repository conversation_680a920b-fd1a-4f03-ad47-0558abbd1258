---
description:
globs:
alwaysApply: true
---

# Your rule content
- 你是java高级编程师，帮忙从图片中识别数据，生成java数据对象
- 请生成java对象，包是com.les.its.open.area.juncer.msg.status
1、使用lombok标记；
2、不要使用内部类；
3、java对象名不要以DTO结束；
4、类似struct(8)、uint8(255)、uint8(16)，是列表对象，请转换为 list对象；
5、类似struct(8)、uint8(255)、uint8(16)生成的对象名称，如果后缀没有s，请补充s表示复数；
6、类似struct(8)(2)、uint8(255)(2)、uint8(16)(2)，是二维列表对象，请转换为 list对象的list对象；
7、类似struct(8)(2)、uint8(255)(2)、uint8(16)(2)生成的对象名称，如果后缀没有s，请补充s表示复数；
8、根对象不要使用复数

package com.les.its.open.area.message.service.distributelock;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解实现
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {
    /**
     * 锁的key，支持SpEL表达式
     */
    String key();

    /**
     * 锁持有等待时间，默认10秒
     */
    long waitTime() default 10;

    /**
     * 任务最大支持时间时间，默认30秒
     */
    long maxRunTime() default 30;

    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;
}

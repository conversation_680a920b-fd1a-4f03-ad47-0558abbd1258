package com.les.its.open.area.message.service.ratelimit;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 流控注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {
    /**
     * 限流key，支持SpEL表达式
     */
    String key() default "";

    /**
     * 限流类型
     */
    RateLimitType type() default RateLimitType.USER;

    /**
     * 容量
     */
    long capacity() default 100;

    /**
     * 令牌数
     */
    long tokens() default 100;

    /**
     * 时间窗口
     */
    long period() default 1;

    /**
     * 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 被限流时的错误消息
     */
    String message() default "Too many requests";
}


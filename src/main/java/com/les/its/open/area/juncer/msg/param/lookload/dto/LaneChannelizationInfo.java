package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LaneChannelizationInfo implements DataIndexAble {
    // 子路口号
    @Range(min = 1, max = 8)
    private Integer crossingSeqNo;
    
    // 车道信息列表
    @Size(min = 128, max = 128)
    @Valid
    private List<Lane> lanes;

    @Override
    public int getDataNo() {
        return crossingSeqNo;
    }
}
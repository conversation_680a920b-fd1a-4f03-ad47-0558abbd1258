package com.les.its.open.area.juncer.msg.link;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabAnswerSignal extends TabOutBase {

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}心跳应答", controllerAgent.getControllerId());
        int decrementAndGet = controllerAgent.getHeartBeatCountRev().decrementAndGet();
        if(decrementAndGet < 0) {
            controllerAgent.getHeartBeatCountRev().set(0);
        }

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }
}

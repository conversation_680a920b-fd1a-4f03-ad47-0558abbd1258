package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.LogicOutput;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Timer;
import com.les.its.open.area.juncer.msg.param.lookload.dto.UserFlag;
import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 用户标志列表
 */
@Data
public class LogicParam implements SgpTransAble {
    public static final String MqObjectId = "10015";

    /**
     * 逻辑输出
     */
    @NotNull(message = "{basic.param.logic.logicOutputs.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.logic.logicOutputs.size}")
    @Valid
    private List<LogicOutput> logicOutputs;

    /**
     * 定时器
     */
    @NotNull(message = "{basic.param.logic.timers.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.logic.timers.size}")
    @Valid
    private List<Timer> timers;

    /**
     * 用户标志列表
     */
    @NotNull(message = "{basic.param.logic.userFlags.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.logic.userFlags.size}")
    @Valid
    private List<UserFlag> userFlags;


    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
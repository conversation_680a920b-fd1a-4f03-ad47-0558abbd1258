package com.les.its.open.area.message.param.cmd;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 交易验证状态枚举
 * 定义交易验证的不同状态
 */
@Getter
public enum TransactionVerifyStatus {

    NOT_DONE(1, "未完成"),
    DONE_WITH_ERROR(2, "完成但有错误"),
    DONE_WITH_NO_ERROR(3, "完成且无错误");

    private final int code;
    private final String description;

    TransactionVerifyStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取对应的验证状态
     *
     * @param code 状态代码
     * @return 验证状态枚举
     */
    public static Optional<TransactionVerifyStatus> fromCode(int code) {
        return Arrays.stream(TransactionVerifyStatus.values())
                .filter(status -> status.getCode() == code)
                .findFirst();
    }

    /**
     * 判断代码是否为有效的验证状态
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return fromCode(code).isPresent();
    }

    @Override
    public String toString() {
        return String.format("%s(%d) - %s", name(), code, description);
    }
}
package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Demand;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Extension;
import com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseDemandExtension;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PhaseDemandExtensionProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            PhaseDemandExtension phaseDemandExtension = new PhaseDemandExtension();
            objects.add(phaseDemandExtension);

            //相位编号
            phaseDemandExtension.setPhaseNo(buf.readUnsignedByte() & 0xff);

            // 需求配置列表 (最多8个)
            List<Demand> demands = new ArrayList<>();
            phaseDemandExtension.setDemands(demands);
            for (int i = 0; i < 8; i++) {
                Demand demand = new Demand();
                demands.add(demand);

                demand.setInputType(buf.readUnsignedByte() & 0xff);
                demand.setInputNo(buf.readUnsignedByte() & 0xff);
                demand.setInputFlag(buf.readUnsignedByte() & 0xff);
            }

            // 延长配置列表 (最多8个)
            List<Extension> extensions = new ArrayList<>();
            phaseDemandExtension.setExtensions(extensions);
            for (int i = 0; i < 8; i++) {
                Extension extension = new Extension();
                extensions.add(extension);

                extension.setInputType(buf.readUnsignedByte() & 0xff);
                extension.setInputNo(buf.readUnsignedByte() & 0xff);
                extension.setInputFlag(buf.readUnsignedByte() & 0xff);
            }

            // 结束手动运行期时插入相位需求
            phaseDemandExtension.setDemandsInsertLeavingManualAndFixPhase(buf.readUnsignedByte() & 0xff);
            // 启动时插入相位需求
            phaseDemandExtension.setDemandsInsertStartUpPhase(buf.readUnsignedByte() & 0xff);
            // 紧急调用结束
            phaseDemandExtension.setDemandsInsertLeavingHurryCall(buf.readUnsignedByte() & 0xff);
            // 中心控制结束
            phaseDemandExtension.setDemandsInsertLeavingSystem(buf.readUnsignedByte() & 0xff);
            // 无条件
            phaseDemandExtension.setUnconditionalDemand(buf.readUnsignedByte() & 0xff);
            // 非锁定需求启动相位最大绿
            phaseDemandExtension.setUnlatchedDemandStartMaxGreenPhase(buf.readUnsignedByte() & 0xff);
            // 最小绿需求
            phaseDemandExtension.setMinGreenDemand(buf.readUnsignedByte() & 0xff);
            // 最大绿需求
            phaseDemandExtension.setMaxGreenDemand(buf.readUnsignedByte() & 0xff);
            // 相位最大绿跟随相位需求
            phaseDemandExtension.setRevertivePhaseDemand(buf.readUnsignedByte() & 0xff);

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof PhaseDemandExtension)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            PhaseDemandExtension phaseDemandExtension
                    = (PhaseDemandExtension)objects.get(i);

            //相位编号
            buf.writeByte(phaseDemandExtension.getPhaseNo());

            // 需求配置列表 (最多8个)
            List<Demand> demands = phaseDemandExtension.getDemands();
            for (int m = 0; m < 8; m++) {
                if(m < demands.size()){
                    buf.writeByte(demands.get(m).getInputType());
                    buf.writeByte(demands.get(m).getInputNo());
                    buf.writeByte(demands.get(m).getInputFlag());
                }else{
                    buf.writeByte(0);
                    buf.writeByte(0);
                    buf.writeByte(0);
                }
            }

            // 延长配置列表 (最多8个)
            List<Extension> extensions = phaseDemandExtension.getExtensions();
            for (int m = 0; m < 8; m++) {
                if(m  < extensions.size()) {
                    buf.writeByte(extensions.get(m).getInputType());
                    buf.writeByte(extensions.get(m).getInputNo());
                    buf.writeByte(extensions.get(m).getInputFlag());
                }else {
                    buf.writeByte(0);
                    buf.writeByte(0);
                    buf.writeByte(0);
                }
            }

            // 结束手动运行期时插入相位需求
            buf.writeByte(phaseDemandExtension.getDemandsInsertLeavingManualAndFixPhase());
            // 启动时插入相位需求
            buf.writeByte(phaseDemandExtension.getDemandsInsertStartUpPhase());
            // 紧急调用结束
            buf.writeByte(phaseDemandExtension.getDemandsInsertLeavingHurryCall());
            // 中心控制结束
            buf.writeByte(phaseDemandExtension.getDemandsInsertLeavingSystem());
            // 无条件
            buf.writeByte(phaseDemandExtension.getUnconditionalDemand());
            // 非锁定需求启动相位最大绿
            buf.writeByte(phaseDemandExtension.getUnlatchedDemandStartMaxGreenPhase());
            // 最小绿需求
            buf.writeByte(phaseDemandExtension.getMinGreenDemand());
            // 最大绿需求
            buf.writeByte(phaseDemandExtension.getMaxGreenDemand());
            // 相位最大绿跟随相位需求
            buf.writeByte(phaseDemandExtension.getRevertivePhaseDemand());
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 8 * (3) + 8 * 3 + 9;
    }

    @Override
    public Class dataClazz() {
        return PhaseDemandExtension.class;
    }
}

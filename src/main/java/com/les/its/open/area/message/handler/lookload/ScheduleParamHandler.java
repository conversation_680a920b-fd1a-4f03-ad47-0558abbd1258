package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.ScheduleParam;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class ScheduleParamHandler implements MqMsgBaseHandler {


    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public ScheduleParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_ScheduleParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return ScheduleParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看调度计划
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_SCHEDULE, 1,
                    ParamMsgType.PARAM_SCHEDULE.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());


        List<Integer> dataNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dataNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        if(dataNos.isEmpty()){
            IntStream.rangeClosed(1, ParamMsgType.PARAM_SCHEDULE.getMax()).forEach(dataNos::add);
        }

        Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.Schedule>> schedulesOp
                = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_SCHEDULE);
        if(schedulesOp.isEmpty()){
            log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_SCHEDULE);
            return Optional.empty();
        }
        log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), schedulesOp.get());


        //构建返回数据项
        dataNos.forEach(dataNo -> {
            //根据编号查找数据项
            schedulesOp.get().stream().filter(
                    plan -> plan.getScheduleNo().intValue() == dataNo
            ).findAny().ifPresent( schedule ->

            //构建返回数据项
            {
                ScheduleParam scheduleParam = new ScheduleParam();
                //设置信号机编号
                scheduleParam.setSignalControllerID(requestMessage.getSignalControllerID());

                // 调度计划编号
                scheduleParam.setScheduleNo(schedule.getScheduleNo());
                // 子路口号
                scheduleParam.setCrossingSeqNo(schedule.getCrossingSeqNo());
                // 优先级 (0:基本优先级，优先级相同时调度表号最小最大)
                scheduleParam.setPriority(schedule.getPriority());
                // 星期值
                scheduleParam.setWeek(schedule.getWeek());
                // 月份
                scheduleParam.setMonth(schedule.getMonth());
                // 日期
                scheduleParam.setDay(schedule.getDay().intValue());
                // 日计划号
                scheduleParam.setDayPlanNo(schedule.getDayPlanNo());
                //调度计划名称
                scheduleParam.setScheduleName(schedule.getName());
                //应答参数
                datas.add(scheduleParam);
            });
        });

        if(datas.isEmpty()){
            errorMsgRet.append("数据返回异常");
            return Optional.empty();
        }
        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //转换格式

        List<com.les.its.open.area.juncer.msg.param.lookload.dto.Schedule> scheduleList
                = new ArrayList<>();

        for (int i = 0; i < datas.size(); i++) {
            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            ScheduleParam scheduleParam = jsonObject.toJavaObject(ScheduleParam.class);
            log.error("设置参数-{}", scheduleParam);

            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(scheduleParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, scheduleParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }

            {
                com.les.its.open.area.juncer.msg.param.lookload.dto.Schedule schedule = new
                        com.les.its.open.area.juncer.msg.param.lookload.dto.Schedule();
                scheduleList.add(schedule);

                // 调度表编号
                schedule.setScheduleNo(scheduleParam.getScheduleNo());
                // 子路口号
                schedule.setCrossingSeqNo(scheduleParam.getCrossingSeqNo());
                // 优先级 (0:基本优先级，优先级相同时调度表号最小最大)
                schedule.setPriority(scheduleParam.getPriority());
                // 星期值
                schedule.setWeek(scheduleParam.getWeek());
                // 月份
                schedule.setMonth(scheduleParam.getMonth());
                // 日期
                schedule.setDay(scheduleParam.getDay().longValue());
                // 日计划号
                schedule.setDayPlanNo(scheduleParam.getDayPlanNo());
                //名称
                schedule.setName(scheduleParam.getScheduleName());
            }
        }

        //生成加载参数
        AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_SCHEDULE,
                scheduleList, signalInfoOp.get());
        areaMessages.add(areaMessage);


        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage,
                                                    List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return false;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

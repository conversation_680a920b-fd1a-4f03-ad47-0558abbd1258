package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StageTransitionConstraintInfo  implements DataIndexAble {
    // 过渡约束索引号
    @NotNull(message = "过渡约束索引号不能为空")
    @Range(min = 1, max = 4, message = "过渡约束索引号有效范围是[1,4]")
    private Integer stageTransitionConstraintNo;
    
    // 过渡约束配置 - 最多支持64个阶段过渡约束
    @NotNull(message = "过渡约束配置不能为空")
    @Size(min = 64, max = 64, message = "过渡约束配置应是64个阶段过渡约束")
    @Valid
    private List<StageTransitionConstraint> stageTransitionConstraints;

    @Override
    public int getDataNo() {
        return stageTransitionConstraintNo;
    }
}
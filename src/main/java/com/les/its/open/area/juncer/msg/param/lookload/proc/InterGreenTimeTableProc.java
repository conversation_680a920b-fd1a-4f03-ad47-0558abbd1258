package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTime;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTimeTable;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class InterGreenTimeTableProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_INTER_GREEN_TIME;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            InterGreenTimeTable interGreenTimeTable= new InterGreenTimeTable();
            objects.add(interGreenTimeTable);

            interGreenTimeTable.setInterGreenTimeTableNo(buf.readUnsignedByte() & 0xff);
            List<InterGreenTime> interGreenTimes =  new ArrayList<>();
            interGreenTimeTable.setInterGreenTimes(interGreenTimes);
            for (int i = 0; i < 64; i++) {
                InterGreenTime interGreenTime = new InterGreenTime();
                interGreenTimes.add(interGreenTime);
                interGreenTime.setPhaseNo(buf.readUnsignedByte() & 0xff);
                List<Integer> interGreenTimeSeq = new ArrayList<>();
                interGreenTime.setInterGreenTimeSeq(interGreenTimeSeq);
                for (int j = 0; j < 64; j++) {
                    interGreenTimeSeq.add(buf.readUnsignedByte() & 0xff);
                }
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof InterGreenTimeTable)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            InterGreenTimeTable interGreenTimeTable
                    = (InterGreenTimeTable)objects.get(i);
            buf.writeByte(interGreenTimeTable.getInterGreenTimeTableNo());
            List<InterGreenTime> interGreenTimes = interGreenTimeTable.getInterGreenTimes();
            for (int j = 0; j < 64; j++) {
                InterGreenTime interGreenTime = interGreenTimes.get(j);
                //相位编号
                buf.writeByte(interGreenTime.getPhaseNo());
                //绿间隔时间序列
                List<Integer> interGreenTimeSeq = interGreenTime.getInterGreenTimeSeq();
                for (int k = 0; k < 64; k++) {
                    buf.writeByte(interGreenTimeSeq.get(k));
                }
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 64 * ( 1 + 64);
    }

    @Override
    public Class dataClazz() {
        return InterGreenTimeTable.class;
    }
}

package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabActuatedPhaseStatus;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabActuatedPhaseStatusProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_ACTUATED_PHASE_STATUS;
    }

    @Override
    public int oneItemSize() {
        return ( 10 * 8);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabActuatedPhaseStatus tabActuatedPhaseStatus = new TabActuatedPhaseStatus();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);


            // 相位状态组红灯
            List<Integer> phaseStatusGroupRed = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupReds(phaseStatusGroupRed);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupRed.add((data >> t) & 0x01);
                }
            }

            // 相位状态组黄灯
            List<Integer> phaseStatusGroupYellow = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupYellows(phaseStatusGroupYellow);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupYellow.add((data >> t) & 0x01);
                }
            }
            // 相位状态组绿灯
            List<Integer> phaseStatusGroupGreen = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupGreens(phaseStatusGroupGreen);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupGreen.add((data >> t) & 0x01);
                }
            }
            // 相位状态组"请勿通行"状态
            List<Integer> phaseStatusGroupDontWalk = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupDontWalks(phaseStatusGroupDontWalk);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupDontWalk.add((data >> t) & 0x01);
                }
            }
            // 相位状态组行人清空状态
            List<Integer> phaseStatusGroupPedClear = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupPedClears(phaseStatusGroupPedClear);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupPedClear.add((data >> t) & 0x01);
                }
            }
            // 相位状态组"通行"状态
            List<Integer> phaseStatusGroupWalk = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupWalks(phaseStatusGroupWalk);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupWalk.add((data >> t) & 0x01);
                }
            }
            // 相位状态组车辆呼叫状态
            List<Integer> phaseStatusGroupVehCall = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupVehCalls(phaseStatusGroupVehCall);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupVehCall.add((data >> t) & 0x01);
                }
            }
            // 相位状态组行人呼叫状态
            List<Integer> phaseStatusGroupPedCall = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupPedCalls(phaseStatusGroupPedCall);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupPedCall.add((data >> t) & 0x01);
                }
            }
            // 相位状态组相位启动状态
            List<Integer> phaseStatusGroupPhaseOn = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupPhaseOns(phaseStatusGroupPhaseOn);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupPhaseOn.add((data >> t) & 0x01);
                }
            }
            // 相位状态组下一相位状态
            List<Integer> phaseStatusGroupNext = new ArrayList<>();
            tabActuatedPhaseStatus.setPhaseStatusGroupNexts(phaseStatusGroupNext);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    phaseStatusGroupNext.add((data >> t) & 0x01);
                }
            }

            buf.release();
            return Optional.of(tabActuatedPhaseStatus);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.StartupStage;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class StartupStageProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_STARTUP_STAGE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            StartupStage startupStage = new StartupStage();
            objects.add(startupStage);

            //无需求进入阶段
            List<Integer> noDemandStages = new ArrayList<>();
            startupStage.setNoDemandStages(noDemandStages);
            for (int i = 0; i < 8; i++) {
                noDemandStages.add(buf.readUnsignedByte() & 0xff);
            }

            //启动进入阶段
            List<Integer> startupStages = new ArrayList<>();
            startupStage.setStartupStages(startupStages);
            for (int i = 0; i < 8; i++) {
                startupStages.add(buf.readUnsignedByte() & 0xff);
            }

            // 结束手动定周期时插入阶段需求 (针对每个阶段)
            List<Integer> demandsInsertLeavingManualAndFixStages = new ArrayList<>();
            startupStage.setDemandsInsertLeavingManualAndFixStages(demandsInsertLeavingManualAndFixStages);
            long data = buf.readLongLE();
            for (int i = 0; i < 64; i++) {
                demandsInsertLeavingManualAndFixStages.add((int)(( data >> i ) & 0x01));
            }

            // 启动时插入阶段需求 (针对每个阶段)
            List<Integer> demandsInsertStartUpStages = new ArrayList<>();
            startupStage.setDemandsInsertStartUpStages(demandsInsertStartUpStages);
            data = buf.readLongLE();
            for (int i = 0; i < 64; i++) {
                demandsInsertStartUpStages.add((int)(( data >> i ) & 0x01));
            }

            // 窗口时间
            List<Integer> windowsTimes = new ArrayList<>();
            startupStage.setWindowsTimes(windowsTimes);
            for (int i = 0; i < 64; i++) {
                windowsTimes.add(buf.readUnsignedByte() & 0xff);
            }

            /**
             * 紧急调用结束后插入需求
             */
            List<Integer> demandsInsertLeavingHurryCalls = new ArrayList<>();
            startupStage.setDemandsInsertLeavingHurryCalls(demandsInsertLeavingHurryCalls);
            data = buf.readLongLE();
            for (int i = 0; i < 64; i++) {
                demandsInsertLeavingHurryCalls.add((int)(( data >> i ) & 0x01));
            }

            /**
             * 中心控制结末后插入需求
             */
            List<Integer> demandsInsertLeavingSystems = new ArrayList<>();
            startupStage.setDemandsInsertLeavingSystems(demandsInsertLeavingSystems);
            data = buf.readLongLE();
            for (int i = 0; i < 64; i++) {
                demandsInsertLeavingSystems.add((int)(( data >> i ) & 0x01));
            }

            /**
             * 无条件默认需求
             */
            List<Integer> unconditionalDemands = new ArrayList<>();
            startupStage.setUnconditionalDemands(unconditionalDemands);
            data = buf.readLongLE();
            for (int i = 0; i < 64; i++) {
                unconditionalDemands.add((int)(( data >> i ) & 0x01));
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof StartupStage)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            StartupStage startupStage
                    = (StartupStage)objects.get(i);

            //无需求进入阶段
            for (int m = 0; m < 8; m++) {
                buf.writeByte(startupStage.getNoDemandStages().get(m) & 0xff);
            }

            //启动进入阶段
            for (int m = 0; m < 8; m++) {
                buf.writeByte(startupStage.getStartupStages().get(m) & 0xff);
            }

            // 结束手动定周期时插入阶段需求 (针对每个阶段)
            List<Integer> demandsInsertLeavingManualAndFixStages = startupStage.getDemandsInsertLeavingManualAndFixStages();
            long data = 0x00;
            for (int m = 0; m < 64 && m < demandsInsertLeavingManualAndFixStages.size(); m++) {
                data |= ( (long)( demandsInsertLeavingManualAndFixStages.get(m) & 0x01 ) << m);
            }
            buf.writeLongLE(data);


            // 启动时插入阶段需求 (针对每个阶段)
            List<Integer> demandsInsertStartUpStages = startupStage.getDemandsInsertStartUpStages();
            data = 0x00;
            for (int m = 0; m < 64 && m < demandsInsertStartUpStages.size(); m++) {
                data |= ( (long)( demandsInsertStartUpStages.get(m) & 0x01 ) << m);
            }
            buf.writeLongLE(data);

            // 窗口时间
            List<Integer> windowsTimes = startupStage.getWindowsTimes();
            for (int j = 0; j < 64 && j < windowsTimes.size(); j++) {
                buf.writeByte(startupStage.getWindowsTimes().get(j) & 0xff);
            }

            // 紧急调用结束后插入需求 (针对每个阶段)
            List<Integer> demandsInsertLeavingHurryCalls = startupStage.getDemandsInsertLeavingHurryCalls();
            data = 0x00;
            for (int m = 0; m < 64 && m < demandsInsertLeavingHurryCalls.size(); m++) {
                data |= ( (long)( demandsInsertLeavingHurryCalls.get(m) & 0x01 ) << m);
            }
            buf.writeLongLE(data);

            // 中心控制结末后插入需求 (针对每个阶段)
            List<Integer> demandsInsertLeavingSystems = startupStage.getDemandsInsertLeavingSystems();
            data = 0x00;
            for (int m = 0; m < 64 && m < demandsInsertLeavingSystems.size(); m++) {
                data |= ( (long)( demandsInsertLeavingSystems.get(m) & 0x01 ) << m);
            }
            buf.writeLongLE(data);

            // 无条件默认需求 (针对每个阶段)
            List<Integer> unconditionalDemands = startupStage.getUnconditionalDemands();
            data = 0x00;
            for (int m = 0; m < 64 && m < unconditionalDemands.size(); m++) {
                data |= ( (long)( unconditionalDemands.get(m) & 0x01 ) << m);
            }
            buf.writeLongLE(data);

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 7*8 + 64;
    }

    @Override
    public Class dataClazz() {
        return StartupStage.class;
    }
}

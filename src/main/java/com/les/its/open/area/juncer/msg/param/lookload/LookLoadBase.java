package com.les.its.open.area.juncer.msg.param.lookload;

import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;

import java.util.List;
import java.util.Optional;

public interface LookLoadBase {

    ParamMsgType msgType();

    /**
     * 调看信号机参数
     * @param juncerMsg
     * @return
     */
    Optional<List<Object>> procLook(JuncerMsg juncerMsg);

    /**
     * 加载信号机参数
     * @param objects
     * @return
     */
    Optional<byte[]> procLoad(List<Object> objects);

    /**
     * 一项数据的长度
     * @return
     */
    int getOneDataSize();

    Class dataClazz();

}

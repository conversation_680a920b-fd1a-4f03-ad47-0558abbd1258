package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 特殊控制
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabCmd extends TabInBase implements NeedAck {

    /**
     * 控制命令
     */
    @NotNull(message = "控制命令不能为空")
    private Integer cmd;

    /**
     * 命令参数
     */
    @NotNull(message = "命令参数不能为空")
    private Long param1;

    /**
     * 命令参数
     */
    @NotNull(message = "命令参数2不能为空")
    private Integer param2;

    /**
     * 命令参数
     */
    @NotNull(message = "命令参数3不能为空")
    private Integer param3;

    /**
     * 命令参数
     */
    @NotNull(message = "命令参数4不能为空")
    private Integer param4;

    /**
     * 命令参数
     */
    @NotNull(message = "命令参数5不能为空")
    private Integer param5;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_CMD.getCode());
    }
}
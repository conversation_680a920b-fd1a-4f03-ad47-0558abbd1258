package com.les.its.open.area.message.handler;


import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.event.AckManager.InvokeFuture;

import java.util.List;
import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 9:18
 */
public interface MqMsgBaseHandler {

    /**
     * 获取处理的objectId对象
     * @return
     */
    String getObjectId();

    /**
     * 数据发送routingKey
     *
     * @return
     */
    String getRoutingKey();

    /**
     * 获取数据对象类型
     *
     * @return
     */
    Class<?> dataType();

    /**
     * 根据信号机编号获取需要发送的数据项
     *
     * @param controllerId
     * @return
     */
    Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas);

    /**
     * 根据返回应答数据项及原始请求报文，生成应答报文
     *
     * @param requestMessage
     * @param invokeFutures
     * @return
     */
    Optional<MqMessage> getResponseMsg(MqMessage requestMessage,
                                       List<InvokeFuture> invokeFutures,
                                       StringBuilder errorMsg);

    /**
     * 查询数据是否需要同步请求
     *
     * @param controllerId
     * @return
     */
    boolean requestMsgNeedSerial(String controllerId);

    /**
     * 根据信号机编号获取需要发送的数据项
     *
     * @param controllerId
     * @return
     */
    Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas,
                                                    StringBuilder errorMsg);

    /**
     * 根据返回应答数据项及原始请求报文，生成应答报文
     *
     * @param requestMessage
     * @param invokeFutures
     * @return
     */
    Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage,
                                             List<InvokeFuture> invokeFutures,
                                             StringBuilder errorMsg);


    /**
     * 查询数据是否需要同步请求
     *
     * @param controllerId
     * @return
     */
    boolean configRequestMsgNeedSerial(String controllerId);

    /**
     * 消息是否需要立即应答
     *
     * @param requestMessage
     * @return
     */
    Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack);


}

package com.les.its.open.area.net.proc;

import com.les.its.open.area.juncer.msg.param.TabLook;
import com.les.its.open.area.juncer.msg.param.TabLookParam;
import com.les.its.open.area.juncer.msg.param.TabLookParamErr;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.area.net.utils.JuncerUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class TabLookService {

    @Autowired
    private InMsgService inMsgService;

    @Autowired
    private JuncerUtils juncerUtils;


    /**
     * 同步等待数据项应答，会阻塞所在线程，请特别注意
     * @param paramMsgType
     * @param offset
     * @param count
     * @return
     */
    public Optional<Object> look(String controllerId, ParamMsgType paramMsgType, int offset, int count) {
        TabLook tabLook = new TabLook();
        tabLook.setControllerId(controllerId);
        tabLook.setMsgType(MsgType.TAB_LOOK);
        tabLook.setOffset(offset);
        tabLook.setCount(count);
        tabLook.setParamMsgType(paramMsgType);
        boolean chg2Outer = inMsgService.chg2Outer(tabLook);
        if(chg2Outer){
            Optional<Object> op = juncerUtils.send2ControllerAndWaitForAck(tabLook.getIp(), tabLook, false, false);
            if(op.isPresent()){
               if( op.get() instanceof TabLookParam tabLookParam){
                   return Optional.of(tabLookParam.getParams());
               }else if( op.get() instanceof TabLookParamErr tabLookParamErr){
                   log.error("参数调看失败-{}", tabLookParamErr);
                   return Optional.of(tabLookParamErr);
               }
            }
        }
        return Optional.empty();
    }

}

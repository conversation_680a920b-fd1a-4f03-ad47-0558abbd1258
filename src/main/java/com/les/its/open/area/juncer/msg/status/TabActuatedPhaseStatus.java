package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 感应相位状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabActuatedPhaseStatus extends TabOutBase {
    /**
     * 相位状态组红灯
     */
    private List<Integer> phaseStatusGroupReds;
    
    /**
     * 相位状态组黄灯
     */
    private List<Integer> phaseStatusGroupYellows;
    
    /**
     * 相位状态组绿灯
     */
    private List<Integer> phaseStatusGroupGreens;
    
    /**
     * 相位状态组"请勿通行"状态
     */
    private List<Integer> phaseStatusGroupDontWalks;
    
    /**
     * 相位状态组行人清空状态
     */
    private List<Integer> phaseStatusGroupPedClears;
    
    /**
     * 相位状态组"通行"状态
     */
    private List<Integer> phaseStatusGroupWalks;
    
    /**
     * 相位状态组车辆呼叫状态
     */
    private List<Integer> phaseStatusGroupVehCalls;
    
    /**
     * 相位状态组行人呼叫状态
     */
    private List<Integer> phaseStatusGroupPedCalls;
    
    /**
     * 相位状态组相位启动状态
     */
    private List<Integer> phaseStatusGroupPhaseOns;
    
    /**
     * 相位状态组下一相位状态
     */
    private List<Integer> phaseStatusGroupNexts;
} 
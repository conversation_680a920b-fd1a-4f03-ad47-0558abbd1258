package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.*;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class LampFaultThresholdProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_LAMP_FAULT_THRESHOLD;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            LampFaultThreshold lampFaultThreshold = new LampFaultThreshold();
            objects.add(lampFaultThreshold);

            lampFaultThreshold.setLampNo(buf.readUnsignedByte() & 0xff);

            Threshold greenThreshold = new Threshold();
            greenThreshold.setVoltageUpper(buf.readUnsignedByte() & 0xff);
            greenThreshold.setVoltageLower(buf.readUnsignedByte() & 0xff);
            greenThreshold.setCurrentUpper(buf.readUnsignedByte() & 0xff);
            greenThreshold.setCurrentLower(buf.readUnsignedByte() & 0xff);
            lampFaultThreshold.setGreenThreshold(greenThreshold);
            greenThreshold.chg2Look();

            // 黄灯电压电流门限值
            Threshold yellowThreshold = new Threshold();
            yellowThreshold.setVoltageUpper(buf.readUnsignedByte() & 0xff);
            yellowThreshold.setVoltageLower(buf.readUnsignedByte() & 0xff);
            yellowThreshold.setCurrentUpper(buf.readUnsignedByte() & 0xff);
            yellowThreshold.setCurrentLower(buf.readUnsignedByte() & 0xff);
            lampFaultThreshold.setYellowThreshold(yellowThreshold);
            yellowThreshold.chg2Look();

            // 红灯电压电流门限值
             Threshold redThreshold = new Threshold();
             redThreshold.setVoltageUpper(buf.readUnsignedByte() & 0xff);
             redThreshold.setVoltageLower(buf.readUnsignedByte() & 0xff);
             redThreshold.setCurrentUpper(buf.readUnsignedByte() & 0xff);
             redThreshold.setCurrentLower(buf.readUnsignedByte() & 0xff);
             lampFaultThreshold.setRedThreshold(redThreshold);
             redThreshold.chg2Look();

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof LampFaultThreshold)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            LampFaultThreshold lampFaultThreshold
                    = (LampFaultThreshold)objects.get(i);
            lampFaultThreshold.getGreenThreshold().chg2Load();
            lampFaultThreshold.getYellowThreshold().chg2Load();
            lampFaultThreshold.getRedThreshold().chg2Load();

            buf.writeByte(lampFaultThreshold.getLampNo());
            buf.writeByte(lampFaultThreshold.getGreenThreshold().getVoltageUpper());
            buf.writeByte(lampFaultThreshold.getGreenThreshold().getVoltageLower());
            buf.writeByte(lampFaultThreshold.getGreenThreshold().getCurrentUpper());
            buf.writeByte(lampFaultThreshold.getGreenThreshold().getCurrentLower());
            buf.writeByte(lampFaultThreshold.getYellowThreshold().getVoltageUpper());
            buf.writeByte(lampFaultThreshold.getYellowThreshold().getVoltageLower());
            buf.writeByte(lampFaultThreshold.getYellowThreshold().getCurrentUpper());
            buf.writeByte(lampFaultThreshold.getYellowThreshold().getCurrentLower());
            buf.writeByte(lampFaultThreshold.getRedThreshold().getVoltageUpper());
            buf.writeByte(lampFaultThreshold.getRedThreshold().getVoltageLower());
            buf.writeByte(lampFaultThreshold.getRedThreshold().getCurrentUpper());
            buf.writeByte(lampFaultThreshold.getRedThreshold().getCurrentLower());
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 4 *3;
    }

    @Override
    public Class dataClazz() {
        return LampFaultThreshold.class;
    }
}

package com.les.its.open.area.net.msg.link;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.link.TabAnswerSignal;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabAnswerSignalProcess extends TabOuterBaseMsgProcess {

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        TabAnswerSignal tabRequest = new TabAnswerSignal();
       return Optional.of(tabRequest);
    }

    @Override
    public MsgType msgType() {
        return MsgType.TAB_ANSWER_SIGNAL;
    }

    @Override
    public int oneItemSize() {
        return 0;
    }
}

package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 信号机ipv6信息
 */
@Data
public class Ipv6 {
    /**
     * 地址
     * 格式 fe80:1122:3352:96ae:0000:0000:3352:96ae
     */
    @NotNull(message = "{basic.param.ipv6.ip.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "{basic.param.ipv6.ip.invalid}"
    )
    private String ip;

    /**
     * 子网掩码
     * 格式 fe80:1122:3352:96ae:0000:0000:3352:0000
     */
    @NotNull(message = "{basic.param.ipv6.mask.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "{basic.param.ipv6.mask.invalid}"
    )
    private String mask;

    /**
     * 网关
     * 格式 fe80:1122:3352:96ae:0000:0000:3352:0001
     */
    @NotNull(message = "{basic.param.ipv6.gateway.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "{basic.param.ipv6.gateway.invalid}"
    )
    private String gateway;



} 
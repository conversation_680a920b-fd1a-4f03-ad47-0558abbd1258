package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Constraint implements DataIndexAble {
    // 所有相位最小绿最小约束
    @NotNull(message = "{juncer.param.constraint.minGreenConstraintLower.notNull}")
    @Range(min = 0, max = 30, message = "{juncer.param.constraint.minGreenConstraintLower.range}")
    private Integer minGreenConstraintLower;
    
    // 所有相位最小绿最大约束
    @NotNull(message = "{juncer.param.constraint.minGreenConstraintUpper.notNull}")
    @Range(min = 0, max = 30, message = "{juncer.param.constraint.minGreenConstraintUpper.range}")
    private Integer minGreenConstraintUpper;
    
    // 所有相位最大绿最小约束
    @NotNull(message = "{juncer.param.constraint.maxGreenConstraintLower.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.constraint.maxGreenConstraintLower.range}")
    private Integer maxGreenConstraintLower;
    
    // 所有相位最大绿最大约束
    @NotNull(message = "{juncer.param.constraint.maxGreenConstraintUpper.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.constraint.maxGreenConstraintUpper.range}")
    private Integer maxGreenConstraintUpper;
    
    // 所有行人相位绿闪小约束
    @NotNull(message = "{juncer.param.constraint.pedFlashingGreenLower.notNull}")
    @Range(min = 0, max = 32, message = "{juncer.param.constraint.pedFlashingGreenLower.range}")
    private Integer pedFlashingGreenLower;
    
    // 所有行人相位绿闪大约束
    @NotNull(message = "{juncer.param.constraint.pedFlashingGreenUpper.notNull}")
    @Range(min = 0, max = 32, message = "{juncer.param.constraint.pedFlashingGreenUpper.range}")
    private Integer pedFlashingGreenUpper;
    
    // 所有相位绿间隔最小约束
    @NotNull(message = "{juncer.param.constraint.interGreenLower.notNull}")
    @Range(min = 0, max = 30, message = "{juncer.param.constraint.interGreenLower.range}")
    private Integer interGreenLower;
    
    // 所有相位绿间隔最大约束
    @NotNull(message = "{juncer.param.constraint.interGreenUpper.notNull}")
    @Range(min = 0, max = 30, message = "{juncer.param.constraint.interGreenUpper.range}")
    private Integer interGreenUpper;

    // 所有相位绿延长最小约束
    @NotNull(message = "{juncer.param.constraint.extGreenLower.notNull}")
    @Range(min = 0, max = 150, message = "{juncer.param.constraint.extGreenLower.range}")
    private Integer extGreenLower;

    // 所有相位绿延长最大约束
    @NotNull(message = "{juncer.param.constraint.extGreenUpper.notNull}")
    @Range(min = 0, max = 150, message = "{juncer.param.constraint.extGreenUpper.range}")
    private Integer extGreenUpper;

    // 最小绿约束上限应大于下限
    @AssertTrue(message = "{juncer.param.constraint.minGreenConstraint.valid}")
    public boolean isMinGreenConstraintValid(){
        if(minGreenConstraintLower == null || minGreenConstraintUpper == null){
            return true;
        }
        return minGreenConstraintLower < minGreenConstraintUpper;
    }

    // 最大绿约束上限应大于下限
    @AssertTrue(message = "{juncer.param.constraint.maxGreenConstraint.valid}")
    public boolean isMaxGreenConstraintValid(){
        if(maxGreenConstraintLower == null || maxGreenConstraintUpper == null){
            return true;
        }
        return maxGreenConstraintLower < maxGreenConstraintUpper;
    }

    // 行人绿闪约束上限应大于下限
    @AssertTrue(message = "{juncer.param.constraint.pedFlashingGreenConstraint.valid}")
    public boolean isPedFlashingGreenConstraintValid(){
        if(pedFlashingGreenLower == null || pedFlashingGreenUpper == null){
            return true;
        }
        return pedFlashingGreenLower < pedFlashingGreenUpper;
    }

    // 绿间隔约束上限应大于下限
    @AssertTrue( message = "{juncer.param.constraint.interGreenConstraint.valid}")
    public boolean isInterGreenConstraintValid(){
        if(interGreenLower == null || interGreenUpper == null){
            return true;
        }
        return interGreenLower < interGreenUpper;
    }

    // 绿延长约束上限应大于下限
    @AssertTrue(message = "{juncer.param.constraint.extGreenConstraint.valid}")
    public boolean isExtGreenConstraintValid(){
        if(extGreenLower == null || extGreenUpper == null){
            return true;
        }
        return extGreenLower < extGreenUpper;
    }

    @Override
    public int getDataNo() {
        return 1;
    }
}
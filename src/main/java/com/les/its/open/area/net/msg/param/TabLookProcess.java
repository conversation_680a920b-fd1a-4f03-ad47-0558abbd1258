package com.les.its.open.area.net.msg.param;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.TabLook;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabLookProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_LOOK;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabLook.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabLook tabLook) {
            msgCode.set(tabLook.getLookId());
            byte[] body = new byte[2];
            body[0] = (byte) (tabLook.getOffset() & 0xff);
            body[1] = (byte) (tabLook.getCount() & 0xff) ;
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}

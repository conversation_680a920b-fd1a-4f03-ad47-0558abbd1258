package com.les.its.open.area.message.param.lookload;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidMac;
import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.HostIp;
import com.les.its.open.area.message.param.lookload.sub.Ipv4;
import com.les.its.open.area.message.param.lookload.sub.Ipv6;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 基础参数信息
 */
@Data
public class BasicParam implements SgpTransAble {
    public static final String MqObjectId = "10001";

    /**
     * 区域编号
     * [0,39]
     */
    @NotNull(message = "{basic.param.noArea.notNull}")
    @Range(min = 0, max = 39, message = "{basic.param.noArea.range}")
    private Integer noArea;

    /**
     * 路口编号
     * [0,499]
     */
    @NotNull(message = "{basic.param.noJunc.notNull}")
    @Range(min = 0, max = 499, message = "{basic.param.noJunc.range}")
    private Integer noJunc;

    /**
     * 信号机型
     */
    @NotNull(message = "{basic.param.type.notNull}")
    private String type;

    /**
     * 出厂日期
     */
    @NotNull(message = "{basic.param.productionDate.notNull}")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate productionDate;

    /**
     * 经度
     */
    @NotNull(message = "{basic.param.lon.notNull}")
    private Double lon;

    /**
     * 纬度
     */
    @NotNull(message = "{basic.param.lat.notNull}")
    private Double lat;

    /**
     * 控制路口数
     * [1-8]
     */
    @NotNull(message = "{basic.param.controlledJunctionNum.notNull}")
    @Range(min = 1, max = 8, message = "{basic.param.controlledJunctionNum.range}")
    private Integer controlledJunctionNum;

    /**
     * 安装路口
     * 128字节
     */
    @NotNull(message = "{basic.param.installIntersection.notNull}")
    private String installIntersection;

    /**
     * 通讯模式
     * 0:串口，1:ipv4，2:ipv6
     */
    @NotNull(message = "{basic.param.ipEnabled.notNull}")
    @Range(min = 0, max = 2, message = "{basic.param.ipEnabled.range}")
    private Integer cmmType;

    /**
     * 信号机mac地址
     * 格式 1C:69:7A:F9:50:CE
     */
    @NotNull(message = "{basic.param.macAddress.notNull}")
    @ValidMac(message = "{basic.param.macAddress.invalid}")
    private String macAddress;

    /**
     * 信号机ipv4信息 允许为null
     */
    @Valid
    private Ipv4 ipv4;

    /**
     * 信号机ipv6信息 允许为null
     */
    @Valid
    private Ipv6 ipv6;

    /**
     * 上位机ipv4地址
     * 支持配置4组
     */
    @NotNull(message = "{basic.param.hostIpv4s.notNull}")
    @Size(min = 0, max = 4, message = "{basic.param.hostIpv4s.size}")
    @Valid
    private List<HostIp> hostIpv4s;

    /**
     * 上位机ipv6地址
     * 支持配置4组
     */
    @NotNull(message = "{basic.param.hostIpv6s.notNull}")
    @Size(min = 0, max = 4, message = "{basic.param.hostIpv6s.size}")
    @Valid
    private List<HostIp> hostIpv6s;

    @AssertTrue(message = "{basic.param.cmmType.match}")
    private boolean isMatchCmmType() {
        if (cmmType == null) {
            return true; // 让 @NotNull 处理空值验证
        }
        if (cmmType == 0) {
            return true;
        } else if (cmmType == 1) {
            return ipv4 != null;
        } else if (cmmType == 2) {
            return ipv6 != null;
        }
        return false;
    }

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.*;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PhaseTableLimitGreensProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PHASE_TABLE_LIMIT_GREEN;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            PhaseTableLimitGreens phaseTableLimitGreens= new PhaseTableLimitGreens();
            objects.add(phaseTableLimitGreens);

            //相位表编号
            phaseTableLimitGreens.setPhaseTableNo(buf.readUnsignedByte() & 0xff);
            List<PhaseTableLimitGreenInfo> phaseTableLimitGreenInfoInfos = new ArrayList<>();
            phaseTableLimitGreens.setPhaseTableLimitGreenInfoInfos(phaseTableLimitGreenInfoInfos);
            //相位表信息
            for (int i = 0; i < 64; i++) {
                PhaseTableLimitGreenInfo phaseTableLimitGreenInfo = new PhaseTableLimitGreenInfo();
                phaseTableLimitGreenInfoInfos.add(phaseTableLimitGreenInfo);

                // 相位最小绿时间1
                phaseTableLimitGreenInfo.setMinGreenTime1(buf.readUnsignedByte() & 0xff);
                // 相位最小绿时间2
                phaseTableLimitGreenInfo.setMinGreenTime2(buf.readUnsignedByte() & 0xff);
                // 相位最小绿时间3
                phaseTableLimitGreenInfo.setMinGreenTime3(buf.readUnsignedByte() & 0xff);
                // 相位最小绿时间4
                phaseTableLimitGreenInfo.setMinGreenTime4(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间1
                phaseTableLimitGreenInfo.setMaxGreenTime1(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间2
                phaseTableLimitGreenInfo.setMaxGreenTime2(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间3
                phaseTableLimitGreenInfo.setMaxGreenTime3(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间4
                phaseTableLimitGreenInfo.setMaxGreenTime4(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间5
                phaseTableLimitGreenInfo.setMaxGreenTime5(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间6
                phaseTableLimitGreenInfo.setMaxGreenTime6(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间7
                phaseTableLimitGreenInfo.setMaxGreenTime7(buf.readUnsignedByte() & 0xff);
                // 相位最大绿时间8
                phaseTableLimitGreenInfo.setMaxGreenTime8(buf.readUnsignedByte() & 0xff);

            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof PhaseTableLimitGreens)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            PhaseTableLimitGreens phaseTableLimitGreens
                    = (PhaseTableLimitGreens)objects.get(i);
            //相位表编号
            buf.writeByte(phaseTableLimitGreens.getPhaseTableNo());
            //相位表限制绿时列表
            List<PhaseTableLimitGreenInfo> phaseTableLimitGreenInfoInfos= phaseTableLimitGreens.getPhaseTableLimitGreenInfoInfos();
            for (int k = 0; k < 64; k++) {
                PhaseTableLimitGreenInfo phaseTableLimitGreenInfo = phaseTableLimitGreenInfoInfos.get(k);

                // 相位最小绿时间1
                buf.writeByte(phaseTableLimitGreenInfo.getMinGreenTime1());
                // 相位最小绿时间2
                buf.writeByte(phaseTableLimitGreenInfo.getMinGreenTime2());
                // 相位最小绿时间3
                buf.writeByte(phaseTableLimitGreenInfo.getMinGreenTime3());
                // 相位最小绿时间4
                buf.writeByte(phaseTableLimitGreenInfo.getMinGreenTime4());
                // 相位最大绿时间1
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime1());
                // 相位最大绿时间2
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime2());
                // 相位最大绿时间3
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime3());
                // 相位最大绿时间4
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime4());
                // 相位最大绿时间5
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime5());
                // 相位最大绿时间6
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime6());
                // 相位最大绿时间7
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime7());
                // 相位最大绿时间8
                buf.writeByte(phaseTableLimitGreenInfo.getMaxGreenTime8());

            }

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 64 * (12);
    }

    @Override
    public Class dataClazz() {
        return PhaseTableLimitGreens.class;
    }
}

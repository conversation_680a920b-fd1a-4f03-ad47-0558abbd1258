package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.LightsGroup;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class LightsGroupProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_LIGHTS_GROUP;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            LightsGroup lightsGroup = new LightsGroup();
            objects.add(lightsGroup);
            //灯组编号
            lightsGroup.setLightsGroupNo(buf.readUnsignedByte() & 0xff);
            //子路口号
            lightsGroup.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            //灯组类型
            lightsGroup.setType(buf.readUnsignedByte() & 0xff);
            //方向
            lightsGroup.setDirection(buf.readUnsignedByte() & 0xff);
            //名称
            {
                byte[] name = new byte[64];
                buf.readBytes(name);
                lightsGroup.setName(new String(name, StandardCharsets.UTF_8).trim());
            }

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof LightsGroup)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            LightsGroup lightsGroup
                    = (LightsGroup)objects.get(i);
            buf.writeByte(lightsGroup.getLightsGroupNo());
            buf.writeByte(lightsGroup.getCrossingSeqNo());
            buf.writeByte(lightsGroup.getType());
            buf.writeByte(lightsGroup.getDirection());
            {
                byte[] name = new byte[64];
                byte[] bytes = lightsGroup.getName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 64 + 4;
    }

    @Override
    public Class dataClazz() {
        return LightsGroup.class;
    }
}

package com.les.its.open.area.message.service;


import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.config.TestSignalConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/7/17 15:12
 */
@Service
@Slf4j
public class SignalCrossingDataCheckService {
    @Autowired
    private CrossingService crossingService;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private TestSignalConfigure testSignalConfigure;


    @Scheduled(initialDelay = 120000, fixedRate = 180000)
    public void queryStatus() {
        if (!GlobalConfigure.enableCheckSignalAndCrossingData) {
            return;
        }
        if (testSignalConfigure.isUseTest()) {
            return;
        }
        boolean controllerChange = controllerService.updateControllerFromSgp();
        boolean crossingChange = crossingService.updateCrossingFromSgp();

    }
}

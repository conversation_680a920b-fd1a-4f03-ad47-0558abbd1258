package com.les.its.open.area.message.param.lookload.sub;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 方案阶段参数
 */
@Data
public class PlanStageParam {
    /**
     * 阶段编号
     */
    @NotNull(message = "{basic.param.planStage.stageNo.notNull}")
    @Min(value = 1, message = "{basic.param.planStage.stageNo.min}")
    @Max(value = 64, message = "{basic.param.planStage.stageNo.max}")
    private Integer stageNo;

    /**
     * 阶段时长
     */
    @NotNull(message = "{basic.param.planStage.stageTime.notNull}")
    @Min(value = 0, message = "{basic.param.planStage.stageTime.min}")
    @Max(value = 65535, message = "{basic.param.planStage.stageTime.max}")
    private Integer stageTime;

    /**
     * 阶段出现类型
     * 1固定 2按需hold 3按需skip
     */
    @NotNull(message = "{basic.param.planStage.stageActivationType.notNull}")
    @Min(value = 1, message = "{basic.param.planStage.stageActivationType.min}")
    @Max(value = 3, message = "{basic.param.planStage.stageActivationType.max}")
    private Integer stageActivationType;

    /**
     * 协调强制关闭
     * 1固定(默认) 2动态
     */
    @NotNull(message = "{basic.param.planStage.coordinatedForceOff.notNull}")
    @Min(value = 1, message = "{basic.param.planStage.coordinatedForceOff.min}")
    @Max(value = 2, message = "{basic.param.planStage.coordinatedForceOff.max}")
    private Integer coordinatedForceOff;

    /**
     * 相位运行早截断参数
     */
    @NotNull(message = "{basic.param.planStage.planStagePhaseParams.notNull}")
    @Size(min = 0, max = 64, message = "{basic.param.planStage.planStagePhaseParams.size}")
    @Valid
    private List<PlanStagePhaseParam> planStagePhaseParams;
} 
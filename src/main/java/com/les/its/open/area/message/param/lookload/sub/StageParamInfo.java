package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 阶段相位参数信息
 */
@Data
public class StageParamInfo {
    /**
     * 阶段编号
     * [1,64]
     */
    @NotNull(message = "{basic.param.stageParam.stageNo.notNull}")
    @Min(value = 1, message = "{basic.param.stageParam.stageNo.min}")
    @Max(value = 64, message = "{basic.param.stageParam.stageNo.max}")
    private Integer stageNo;

    /**
     * 阶段名称
     */
    @NotNull(message = "{basic.param.stageParam.stageName.notNull}")
    private String stageName;

    /**
     * 结束手动运行期时插入阶段需求
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.stageParam.demandsInsertLeavingManualAndFixStage.notNull}")
    @DigitsInSet(acceptedValues = {0, 1}, message = "{basic.param.stageParam.demandsInsertLeavingManualAndFixStage.discrete}")
    private Integer demandsInsertLeavingManualAndFixStage;

    /**
     * 启动时插入阶段需求
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.stageParam.demandsInsertStartUpStage.notNull}")
    @DigitsInSet(acceptedValues = {0, 1}, message = "{basic.param.stageParam.demandsInsertStartUpStage.discrete}")
    private Integer demandsInsertStartUpStage;


    /**
     * 紧急调用结束后插入需求
     */
    @NotNull(message = "{basic.param.stageParam.demandsInsertLeavingHurryCall.notNull}")
    @DigitsInSet(acceptedValues = {0, 1}, message = "{basic.param.stageParam.demandsInsertLeavingHurryCall.range}")
    private Integer demandsInsertLeavingHurryCall;

    /**
     * 中心控制结末后插入需求
     */
    @NotNull(message = "{basic.param.stageParam.demandsInsertLeavingSystem.notNull}")
    @DigitsInSet(acceptedValues = {0, 1}, message = "{basic.param.stageParam.demandsInsertLeavingSystem.range}")
    private Integer demandsInsertLeavingSystem;

    /**
     * 无条件默认需求
     */
    @NotNull(message = "{basic.param.stageParam.unconditionalDemand.notNull}")
    @DigitsInSet(acceptedValues = {0, 1}, message = "{basic.param.stageParam.unconditionalDemand.range}")
    private Integer unconditionalDemand;


    /**
     * 窗口时间
     * 单位秒
     */
    @NotNull(message = "{basic.param.stageParam.windowsTime.notNull}")
    @Min(value = 0, message = "{basic.param.stageParam.windowsTime.min}")
    @Max(value = 255, message = "{basic.param.stageParam.windowsTime.max}")
    private Integer windowsTime;

    /**
     * 相位信息
     */
    @NotNull(message = "{basic.param.stageParam.stagePhaseParams.notNull}")
    @Size(min = 0, max = 64, message = "{basic.param.stageParam.stagePhaseParams.size}")
    @Valid
    private List<StagePhaseParam> stagePhaseParams;
} 
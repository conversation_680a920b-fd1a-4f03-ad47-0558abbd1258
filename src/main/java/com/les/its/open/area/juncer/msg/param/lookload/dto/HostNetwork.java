package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostNetwork implements DataIndexAble {
    // IPv4配置列表
    @Size(min = 4, max = 4, message = "IPv4配置列表个数应该为4")
    @Valid
    private List<HostIpv4> hostIpv4s;
    
    // IPv6配置列表
    @Size(min = 4, max = 4, message = "IPv6配置列表个数应该为4")
    @Valid
    private List<HostIpv6> hostIpv6s;

    @Override
    public int getDataNo() {
        return 1;
    }
}
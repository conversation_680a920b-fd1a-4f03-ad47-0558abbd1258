package com.les.its.open.area.net.proc;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.log.IPBasedLogger;
import com.les.its.open.bussiness.bean.LinkLogEntity;
import com.les.its.open.bussiness.process.LinkLogProcess;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.les.its.open.protocol.common.utils.EquipmentStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class EquipmentStatusService {

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private ControllerAgentService controllerAgentService;

    @Autowired
    private LinkLogProcess linkLogProcess;

    @Autowired
    private WsMessageService wsMessageService;

    @EventListener
    @Async(GlobalConfigure.JUNCER_ASYC_EXECUTOR)
    public void equipmentStatus(EquipmentStatus equipmentStatus) {
       log.error("收到链路{}状态-{}", equipmentStatus.getIp(), equipmentStatus.isActive() ? "激活" : "断开");

       Optional<ControllerBaseInfo> signalInfoByIpOp = controllerService.getSignalInfoByIp(equipmentStatus.getIp());
       if (signalInfoByIpOp.isEmpty()) {
           return;
       }

       IPBasedLogger.logMessage(equipmentStatus.getIp(), signalInfoByIpOp.get().getNoArea(),
                signalInfoByIpOp.get().getNoJunc(), "链路{}-{}", equipmentStatus.getIp(),
                equipmentStatus.isActive() ? "激活" : "断开");

       // 更新链路状态
        Optional<ControllerAgent> controllerAgentOp = controllerAgentService.getControllerAgent(signalInfoByIpOp.get().getSignalId());
        controllerAgentOp.ifPresent(controllerAgent ->
        {
            controllerAgent.setLink(equipmentStatus.isActive());
        });

        //推送前端监控页面
        wsMessageService.revLinkStatus(signalInfoByIpOp.get().getSignalId(), equipmentStatus.isActive());

        //数据存储
        {
            LinkLogEntity linkEntity = new LinkLogEntity();
            linkEntity.setControllerId(signalInfoByIpOp.get().getSignalId());
            linkEntity.setIp(equipmentStatus.getIp() + ":" + equipmentStatus.getPort());
            linkEntity.setLink(equipmentStatus.isActive() ? 1 : 0);
            linkEntity.setTimeStampLog(System.currentTimeMillis());
            linkLogProcess.saveLinkLog(linkEntity);
        }
    }
}

package com.les.its.open.area.juncer.api;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.cmd.TabSetStage;
import com.les.its.open.area.net.proc.TabCmdService;
import com.les.its.open.utils.ResultCode;
import com.les.its.open.utils.ResultVo;
import com.les.its.open.utils.ResultVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RequestMapping("/openles/cmd")
@Slf4j
@RestController
public class CmdController {

    @Autowired
    private TabCmdService tabCmdService;

    /**
     * 指定相位控制命令
     * @param crossingId
     * @param data
     * @return
     */
    @GetMapping("/{crossingId}")
    public ResultVo<?> setStage(@PathVariable String crossingId,
                                            @RequestBody String data) {

        TabSetStage tabSetStage = JSONObject.parseObject(data, TabSetStage.class);
        if(tabSetStage == null) {
            return ResultVoUtil.result(ResultCode.FAILED_SET_STAGE_PARAM);
        }

        Optional<Object> loadOp = tabCmdService.setStage(crossingId, tabSetStage);
        if(loadOp.isPresent()){
            return ResultVoUtil.success(loadOp.get());
        }else{
            return ResultVoUtil.result(ResultCode.FAILED_CMD);
        }
    }

}

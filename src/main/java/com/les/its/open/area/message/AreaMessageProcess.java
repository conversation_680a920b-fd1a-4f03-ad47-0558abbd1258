package com.les.its.open.area.message;


import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.MessagePublisher;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:18
 */
@Service
@Slf4j
public class AreaMessageProcess {

    public final MessagePublisher messagePublisher;

    public final ControllerService controllerService;

    public final InMsgService inMsgService;

    public AreaMessageProcess(MessagePublisher messagePublisher,
                              ControllerService controllerService,
                              InMsgService inMsgService) {
        this.messagePublisher = messagePublisher;
        this.controllerService = controllerService;
        this.inMsgService = inMsgService;
    }

    @PostConstruct
    public void setDataProcessMap() {
    }

    @RabbitListener(queues = {"${global.mq.centralExchange.queue}"})
    public void consumer(String cmd) {
        try {
            AreaMessage cmdObject = JSONObject.parseObject(cmd, AreaMessage.class);
            cmdObject.setTimeStamp(System.currentTimeMillis());
            log.debug("#####收到命令-{}", cmdObject);
            messagePublisher.publishMessage(cmdObject);
        } catch (Exception e) {
            log.error("解析MQ报文出现异常", e);
        }
    }

    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    @EventListener
    public void mqMessageProcess(AreaMessage areaMessage) {
        if (areaMessage == null) {
            log.error("异常的mq数据请求-{}", areaMessage);
            return;
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(areaMessage.getNoArea(), areaMessage.getNoJunc());
        if(signalInfoOp.isEmpty()) {
            log.error("没有找到信号机-{}", areaMessage);
            return;
        }

        if (!controllerService.processMqMsg(signalInfoOp.get().getSignalId())) {
            log.error("信号机编号不处理-{}", signalInfoOp.get().getSignalId());
            return;
        }

        inMsgService.procMqMessage(areaMessage);
    }


}

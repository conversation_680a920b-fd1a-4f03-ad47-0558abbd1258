package com.les.its.open.area.net.msg.link;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.link.TabInquireSystem;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabInquireSystemProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_INQUIRE_SYSTEM;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabInquireSystem.class);
    }


    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {
        msgCode.set(msgType().getCode());
        return Optional.of(new byte[0]);
    }
}

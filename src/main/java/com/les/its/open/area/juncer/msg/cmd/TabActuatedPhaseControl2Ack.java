package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;

/**
 * 感应需求相位控制应答
 */
@Data
public class TabActuatedPhaseControl2Ack extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;
    
    /**
     * 相位编号
     */
    private Integer phaseNo;
    
    /**
     * 实时相位动作参数
     */
    private Integer phaseStartup;
    
    /**
     * 实时相位操作参数
     */
    private Integer phaseOptions;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_ACTIVATED_PHASE_CONTROL2.getCode());
    }
} 
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostIpv4 {
    // 是否启用
    @Range(min = 0, max = 1, message = "是否启用的有效范围是[0,1]")
    private int enabled;
    
    // 地址
    @NotNull(message = "IPv4地址不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "必须是有效的IPv4地址"
    )
    private String ip;
    
    // 通信端口
    @NotNull(message = "通信端口不能为空")
    @Range(min = 0, max = 65535, message = "端口的有效范围是(0,65535]")
    private Integer port;
    
    // 通信类型 1.tcp 2.udp 3.rs232
    @NotNull(message = "通信类型不能为空")
    @Range(min = 1, max = 3, message = "通信类型的有效范围是[1,3]")
    private Integer commType;
    
    // 协议类型 1.荣斯私有 2.gb/t20999 3.920 4.1743 5.43229
    @NotNull(message = "协议类型不能为空")
    @Range(min = 1, max = 5, message = "协议类型的有效范围是[1,5]")
    private Integer protoType;

    /**
     * 生成默认的ipv4地址
     * @return
     */
    public static HostIpv4 genDefaultHostIpv4(){
        HostIpv4 hostIp = new HostIpv4();
        hostIp.setEnabled(0);
        hostIp.setIp("**************");
        hostIp.setPort(7301);
        hostIp.setCommType(1);
        hostIp.setProtoType(1);
        return hostIp;
    }


} 
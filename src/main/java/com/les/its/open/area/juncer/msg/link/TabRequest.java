package com.les.its.open.area.juncer.msg.link;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabRequest extends TabOutBase {

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}联机请求", controllerAgent.getControllerId());

        List<TabInBase> inBases = new ArrayList<>();
        {
            TabResponse tabResponse = new TabResponse();
            tabResponse.setControllerId(controllerAgent.getControllerId());
            tabResponse.setLocalDateTime(LocalDateTime.now());
            tabResponse.setMsgType(MsgType.TAB_RESPONSE);
            inBases.add(tabResponse);
        }
        return inBases;
    }
}

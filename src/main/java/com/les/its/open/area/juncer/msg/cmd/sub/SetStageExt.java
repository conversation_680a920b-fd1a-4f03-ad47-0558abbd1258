package com.les.its.open.area.juncer.msg.cmd.sub;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class SetStageExt {

    // 相位编号
    @NotNull(message = "相位编号不能为空")
    @Range(min = 1, max = 64, message = "相位编号有效范围为[1,64]")
    private Integer phaseNo;

    // 晚启动时间
    @NotNull(message = "晚启动时间不能为空")
    @Range(min = 0, max = 60, message = "晚启动时间有效范围为[0,60]")
    private Integer laggingTime;

    // 早结束时间
    @NotNull(message = "早结束时间不能为空")
    @Range(min = 0, max = 60, message = "早结束时间有效范围为[0,60]")
    private Integer delayCutOffTime;

}

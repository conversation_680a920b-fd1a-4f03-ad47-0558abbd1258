package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 周期切换
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabCycle extends TabOutBase {
    /**
     * 子路口号
     */
    private int crossingSeqNo;
    
    /**
     * 上周期运行时间长
     */
    private int cycleLst;
    
    /**
     * 上周期阶段数
     */
    private int stageNumLst;
    
    /**
     * 上周期阶段链
     */
    private List<Integer> stageChainPre;
    
    /**
     * 上周期时长链
     */
    private List<Integer> timeChainPre;
    
    /**
     * 本周期运行时间长
     */
    private int cycle;
    
    /**
     * 本周期阶段数
     */
    private int stageNum;
    
    /**
     * 本周期阶段链
     */
    private List<Integer> stageChain;
    
    /**
     * 本周期时长链
     */
    private List<Integer> timeChain;
    
    /**
     * 协调控制状态
     */
    private int coordinatedStatus;

    /**
     * 协调过渡模式
     */
    private int coordinatedTransMode;
} 
package com.les.its.open.area.juncer.msg.param.lookload.proc;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.DeviceInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class DeviceInfoProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_DEVICE_INFO;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        DeviceInfo deviceInfo = new DeviceInfo();
        //生成数据项
        //制造厂家
        {
            byte[] manufacturer = new byte[128];
            buf.readBytes(manufacturer);
            deviceInfo.setManufacturer(new String(manufacturer, StandardCharsets.UTF_8).trim());
        }
        //设备版本
        {
            int lowByte = buf.readUnsignedByte() & 0xff;
            int highByte = buf.readUnsignedByte() & 0xff;
           String deviceVersion = String.format("%d.%02d",
                highByte, lowByte);
            deviceInfo.setDeviceVersion(deviceVersion);
        }

        //软件版本
        {
            int lowByte = buf.readUnsignedByte() & 0xff;
            int highByte = buf.readUnsignedByte() & 0xff;
            String softwareVersion = String.format("%d.%02d",
                    highByte, lowByte);
            deviceInfo.setSoftVersion(softwareVersion);
        }
        //设备编号
        {
            byte[] deviceNo = new byte[16];
            buf.readBytes(deviceNo);
            deviceInfo.setDeviceNo(new String(deviceNo, StandardCharsets.UTF_8).trim());
        }
        //出厂日期
        {
            long productionDate = buf.readLongLE();
            deviceInfo.setProductionDate(LocalDateTimeUtil.of(productionDate));
        }
        //配置日期
        {
            long configurationDate = buf.readLongLE();
            deviceInfo.setConfigurationDate(LocalDateTimeUtil.of(configurationDate));
        }

        // 信号机机型
        {
            byte[] serialNumber = new byte[8];
            buf.readBytes(serialNumber);
            deviceInfo.setModel(new String(serialNumber, StandardCharsets.UTF_8).trim());
        }

        //授权码
        {
            long readLongLE = buf.readLongLE();
            String authCode = String.format("%08X", readLongLE);
            deviceInfo.setAuthCode(authCode);
        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(deviceInfo);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        return Optional.empty();
    }

    @Override
    public int getOneDataSize() {
        return  128 + 4 + 16 + 8 + 8  + 2 * 8;
    }

    @Override
    public Class dataClazz() {
        return DeviceInfo.class;
    }

}

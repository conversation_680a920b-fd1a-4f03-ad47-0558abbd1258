package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LightTransition {
    // 灯色类型
    @NotNull(message = "{juncer.param.lightTransition.colorType.notNull}")
    @Range(min = 0, max = 14, message = "{juncer.param.lightTransition.colorType.range}")
    private Integer colorType;
    
    // 灯色时长
    @NotNull(message = "{juncer.param.lightTransition.colorTime.notNull}")
    @Range(min = 0, max = 99, message = "{juncer.param.lightTransition.colorTime.range}")
    private Integer colorTime;
} 
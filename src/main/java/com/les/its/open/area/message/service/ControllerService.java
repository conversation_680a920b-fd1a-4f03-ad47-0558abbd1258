package com.les.its.open.area.message.service;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.les.ads.ds.gis.arm.ControllerDTO;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.bussiness.bean.ControllerNotifyMsg;
import com.les.its.open.bussiness.utils.CentralUtils;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.config.TestSignalConfigure;
import com.les.its.open.event.MessagePublisher;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.IntStream;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/12 14:03
 */
@Service
@Slf4j
public class ControllerService {

    /**
     * key为莱斯信号机id
     */
    @Getter
    private Map<String, ControllerBaseInfo> signalBaseInfoMap = new ConcurrentHashMap<>();

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private MessagePublisher messagePublisher;

    /**
     * 生成默认信号机1049ID
     *
     * @param controllerBaseInfo
     * @return
     */
    public static String genDefault1049SignalId(ControllerBaseInfo controllerBaseInfo) {
        return String.format("%s%02d%03d",
                GlobalConfigure.departmentCode, controllerBaseInfo.getNoArea(), controllerBaseInfo.getNoJunc());
    }

    @PostConstruct
    public void initData() {
        {
            if (testSignalConfigure.isUseTest()) {
                testSignalConfigure.getSignalMap().values().forEach(controllerBaseInfoDto -> {
                    ControllerBaseInfo controllerBaseInfo = new ControllerBaseInfo();
                    controllerBaseInfo.setSignalId(controllerBaseInfoDto.getSignalId());
                    controllerBaseInfo.setNoArea(controllerBaseInfoDto.getNoArea());
                    controllerBaseInfo.setNoJunc(controllerBaseInfoDto.getNoJunc());
                    controllerBaseInfo.setIp(controllerBaseInfoDto.getIp());
                    controllerBaseInfo.setPort(controllerBaseInfoDto.getPort());
                    controllerBaseInfo.setName("测试信号机1");
                    signalBaseInfoMap.put(controllerBaseInfo.getSignalId(), controllerBaseInfo);
                });
            }
        }

    }

    /**
     * 获取信号机基本数据项
     *
     * @param noArea
     * @param noJunc
     * @return
     */
    public Optional<ControllerBaseInfo> getSignalInfo(int noArea, int noJunc) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo ->
                        signalBaseInfo.getNoArea() == noArea
                                && signalBaseInfo.getNoJunc() == noJunc

        ).findFirst();
    }


    /**
     * 获取信号机基本数据项
     *
     * @param ip
     * @param port
     * @return
     */
    public Optional<ControllerBaseInfo> getSignalInfoByIpAndPort(String ip, int port) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo -> signalBaseInfo.getIp().equals(ip)
                        && signalBaseInfo.getPort() == port

        ).findFirst();
    }

    /**
     * 根据信号机ip获取信号机基本数据项
     *
     * @param ip
     * @return
     */
    public Optional<ControllerBaseInfo> getSignalInfoByIp(String ip) {
        return signalBaseInfoMap.values().stream().filter(
                signalBaseInfo ->
                        signalBaseInfo.getIp().compareToIgnoreCase(ip) == 0

        ).findFirst();
    }

    /**
     * 获取信号机基本数据项
     *
     * @param signalId
     * @return
     */
    public Optional<ControllerBaseInfo> getSignalInfo(String signalId) {
        ControllerBaseInfo controllerBaseInfo = signalBaseInfoMap.get(signalId);
        return Optional.ofNullable(controllerBaseInfo);
    }

    /**
     * 判定是否处理这个信号机的调看命令
     *
     * @param signalId
     * @return
     */
    public boolean processMqMsg(String signalId) {
        return signalBaseInfoMap.containsKey(signalId);
    }

    /**
     * 判定是否是Telvent的信号机
     *
     * @param signalController
     * @return
     */
    private boolean isGB1049Controller(ControllerDTO signalController) {
        return true;
    }

    /**
     * 判定是否需要根据品牌进行数据过滤
     *
     * @param signalController
     * @return
     */
    private boolean isBrandProcess(ControllerDTO signalController) {
        if (signalController.getControllerId() == null) {
            return false;
        }

        if (GlobalConfigure.brandList == null
                || GlobalConfigure.brandList.isEmpty()) {
            return true;
        }

        Optional<String> brand = CentralUtils.getBrand(signalController.getControllerId());
        if (brand.isPresent()) {
            return GlobalConfigure.brandList.contains(brand.get());
        }

        return false;
    }

    /**
     * 判定区域时候进行处理
     *
     * @param signalController
     * @return
     */
    private boolean isAreaProcess(ControllerDTO signalController) {
        if (GlobalConfigure.areaList == null
                || GlobalConfigure.areaList.isEmpty()) {
            return true;
        }

        if (signalController.getNoArea() == null) {
            return false;
        }

        return GlobalConfigure.areaList.contains(String.valueOf(signalController.getNoArea()));

    }


    /**
     * 由于当前没有通知机制，只有重新调取数据项进行数据比对，修改本地信号机数据项
     */
    public boolean updateControllerFromSgp() {
        log.info("比对数据项，向sgp-url-{}请求信号机参数", GlobalConfigure.signalParamIp);
        AtomicBoolean dataChange = new AtomicBoolean(false);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/arm/signalController";
            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);

            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取信号机数据返回异常");
                return dataChange.get();
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回信号机的数据项个数是-{}", jsonArray.size());

            ConcurrentHashMap<String, ControllerDTO> currentSignalData = new ConcurrentHashMap<>();
            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {
                        ControllerDTO signalController = jsonArray.getObject(index, ControllerDTO.class);




                        if (signalController.getControllerId() == null) {
                            return;
                        }

                        //针对非处理的信号机进行过滤
                        if (!isGB1049Controller(signalController) || !isBrandProcess(signalController)) {
                            return;
                        }

                        //判定区域是否处理
                        if (!isAreaProcess(signalController)) {
                            log.error("处理区域-{} 信号机数据-{} 区域不匹配,忽略-{}", GlobalConfigure.areaList,
                                    signalController.getControllerId(), signalController);
                            return;
                        }

                        //判定ip是否异常
                        if (signalController.getIp() == null || signalController.getIp().isEmpty()) {
                            log.debug("信号机数据-{} ip为null或为空", signalController.getControllerId());
                            signalController.setIp("");
                            return;
                        }

                        //临时fix 前端拼接 "192-168-11-114"
                        signalController.setIp(signalController.getIp().replace("-", "."));

                        //判定端口是否异常
                        if (signalController.getPort() == null) {
                            log.debug("信号机数据-{} port为null或为空", signalController.getControllerId());
                            signalController.setPort(0);
                        }


                        currentSignalData.put(signalController.getControllerId(), signalController);
                    }
            );

            //根据当前的信号机数据项进行数据更新或者插入
            //1、遍历本地数据项，移除原先的信号机数据项
            signalBaseInfoMap.keySet().stream().forEach(
                    signalId -> {
                        if (!currentSignalData.containsKey(signalId)) {
                            log.error("数据更新，移除信号机-{}", signalId);
                            dataChange.set(true);
                            signalBaseInfoMap.remove(signalId);
                        }
                    }
            );
            //2、遍历新的信号数据项，进行更新或者插入
            currentSignalData.keySet().stream().forEach(
                    signalId -> {
                        ControllerDTO signalController = currentSignalData.get(signalId);
                        ControllerBaseInfo controllerBaseInfo = signalBaseInfoMap.get(signalId);
                        if (controllerBaseInfo != null) {
                            if ((!controllerBaseInfo.getIp().equals(signalController.getIp()))
                                    || (controllerBaseInfo.getPort() != signalController.getPort().intValue())) {
                                log.error("更新信号机数据项-原始数据{}", controllerBaseInfo);
                                controllerBaseInfo.setIp(signalController.getIp());
                                controllerBaseInfo.setPort(signalController.getPort());
                                log.error("更新信号机数据项-更新后数据{}", controllerBaseInfo);
                            } else {
                                log.warn("信号参数未发生变化-{}", controllerBaseInfo);
                            }
                        } else {
                            controllerBaseInfo = new ControllerBaseInfo();
                            controllerBaseInfo.setSignalId(signalController.getControllerId());
                            controllerBaseInfo.setNoArea(signalController.getNoArea());
                            controllerBaseInfo.setNoJunc(signalController.getNoJunc());
                            controllerBaseInfo.setIp(signalController.getIp() != null ? signalController.getIp() : "");
                            controllerBaseInfo.setPort(signalController.getPort() != null ? signalController.getPort() : 0);
                            controllerBaseInfo.setName(signalController.getControllerId());
                            dataChange.set(true);
                            log.error("添加信号机数据项-{}", controllerBaseInfo);
                            signalBaseInfoMap.put(controllerBaseInfo.getSignalId(), controllerBaseInfo);

                            //通知信号机增加
                            messagePublisher.publishMessage(ControllerNotifyMsg.builder().controllerId(controllerBaseInfo.getSignalId()).build());

                        }
                    }
            );
        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }

        return dataChange.get();
    }

    /**
     * 从sgp读取信号机基本参数
     */
    public void getControllerFromSgp() {
        log.info("向sgp-url-{}请求信号机参数", GlobalConfigure.signalParamIp);
        try {
            String queryUrl = "http://" + GlobalConfigure.signalParamIp + "/arm/signalController";

            String str = restTemplate.getForObject(queryUrl, String.class);
            log.debug("准备请求数据,queryUrl-{}", queryUrl);


            JSONObject jsonObject = JSONObject.parseObject(str);
            Boolean success = jsonObject.getBoolean("success");
            if (success == null || !success) {
                log.error("读取信号机数据返回异常");
                return;
            }

            JSONArray jsonArray = jsonObject.getJSONArray("data");
            log.debug("返回信号机的数据项个数是-{}", jsonArray.size());

            IntStream.range(0, jsonArray.size()).forEach(
                    index -> {

                        try {

                            ControllerDTO signalController = jsonArray.getObject(index, ControllerDTO.class);

                            //针对非处理的scats信号机进行过滤
                            if (!isGB1049Controller(signalController) || !isBrandProcess(signalController)) {
                                return;
                            }

                            //判定区域是否处理
                            if (!isAreaProcess(signalController)) {
                                log.error("处理区域-{} 信号机数据-{} 区域不匹配,忽略-{}", GlobalConfigure.areaList,
                                        signalController.getControllerId(), signalController);
                                return;
                            }

                            //判定ip是否异常
                            if (signalController.getIp() == null || signalController.getIp().isEmpty()) {
                                log.error("信号机数据-{} ip为null或为空", signalController.getControllerId());
                                signalController.setIp("");
                                return;
                            }
                            //临时fix 前端拼接 "192-168-11-114"
                            signalController.setIp(signalController.getIp().replace("-", "."));

                            //判定端口是否异常
                            if (signalController.getPort() == null) {
                                log.error("信号机数据-{} 端口为null", signalController.getControllerId());
                                signalController.setPort(0);
                            }

                            //构建数据项存入本地
                            {
                                ControllerBaseInfo controllerBaseInfo = new ControllerBaseInfo();
                                controllerBaseInfo.setSignalId(signalController.getControllerId());
                                controllerBaseInfo.setNoArea(signalController.getNoArea());
                                controllerBaseInfo.setNoJunc(signalController.getNoJunc());
                                controllerBaseInfo.setIp(signalController.getIp() != null ? signalController.getIp() : "");
                                controllerBaseInfo.setPort(signalController.getPort() != null ? signalController.getPort() : 0);
                                controllerBaseInfo.setName(signalController.getControllerId());
                                log.error("添加信号机数据项-{}", controllerBaseInfo);
                                signalBaseInfoMap.put(controllerBaseInfo.getSignalId(), controllerBaseInfo);
                            }
                        } catch (Exception e) {
                            log.error("请求路口基础数据的时候出现异常.", e);
                            log.error("请求路口基础数据的时候出现异常-{}-\n原始数据-{}", e.getCause(), jsonArray.get(index));
                        }
                    }
            );

        } catch (Exception e) {
            log.error("请求路口基础数据的时候出现异常.", e);
        }
    }


}

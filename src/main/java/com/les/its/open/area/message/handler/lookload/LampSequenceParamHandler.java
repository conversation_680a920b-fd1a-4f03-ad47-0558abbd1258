package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LampSequenceParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.sub.RightOfWayColorType;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class LampSequenceParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public LampSequenceParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_LampSequenceParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class dataType() {
        return LampSequenceParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看相位信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_LAMP_SEQUENCE, 1,
                    ParamMsgType.PARAM_LAMP_SEQUENCE.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {

            //相位灯序
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.LampSequence>> lampSequences
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_LAMP_SEQUENCE);
            if(lampSequences.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_LAMP_SEQUENCE);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), lampSequences.get());

            lampSequences.get().stream().forEach(
                    lampSequence -> {
                        LampSequenceParam  lampSequenceParam = new LampSequenceParam();
                        //设置信号机编号
                        lampSequenceParam.setSignalControllerID(requestMessage.getSignalControllerID());


                        lampSequenceParam.setLampSequenceNo(lampSequence.getLampSequenceNo());
                        lampSequenceParam.setSequenceType(lampSequence.getSequenceType());
                        lampSequenceParam.setSequenceName(lampSequence.getSequenceName());

                        //转换系统参数
                        if(RightOfWayColorType.FY.getColorType().intValue() == lampSequence.getDefaultColor().getPartTime()){
                            lampSequenceParam.setPartTimeColor(0);
                        }else{
                            lampSequenceParam.setPartTimeColor(1);
                        }
                        lampSequenceParam.setLoseLightTransitions(lampSequence.getLoseLightTransitions());
                        lampSequenceParam.setObtainLightTransitions(lampSequence.getObtainLightTransitions());
                        lampSequenceParam.setLoseLightTransitionStartups(lampSequence.getLoseLightTransitionStartups());
                        lampSequenceParam.setObtainLightTransitionStartups(lampSequence.getObtainLightTransitionStartups());
                        lampSequenceParam.setLoseLightTransitionYellowFlashToNormals(lampSequence.getLoseLightTransitionYellowFlashToNormals());
                        lampSequenceParam.setObtainLightTransitionYellowFlashToNormals(lampSequence.getObtainLightTransitionYellowFlashToNormals());
                        lampSequenceParam.setLoseLightTransitionYellowFlashs(lampSequence.getLoseLightTransitionYellowFlashs());
                        lampSequenceParam.setObtainLightTransitionYellowFlashs(lampSequence.getObtainLightTransitionYellowFlashs());
                        datas.add(lampSequenceParam);
                    }
            );
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //转换格式

        List<com.les.its.open.area.juncer.msg.param.lookload.dto.LampSequence> lampSequenceList
                = new ArrayList<>();

        for (int i = 0; i < datas.size(); i++) {
            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            LampSequenceParam lampSequenceParam = jsonObject.toJavaObject(LampSequenceParam.class);
            log.error("设置参数-{}", lampSequenceParam);

            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(lampSequenceParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, lampSequenceParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }

            {
                com.les.its.open.area.juncer.msg.param.lookload.dto.LampSequence lampSequence = new
                        com.les.its.open.area.juncer.msg.param.lookload.dto.LampSequence();
                lampSequenceList.add(lampSequence);

                //设置参数项
                lampSequence.setLampSequenceNo(lampSequenceParam.getLampSequenceNo());
                lampSequence.setSequenceType(lampSequenceParam.getSequenceType());
                lampSequence.setSequenceName(lampSequenceParam.getSequenceName());
                lampSequence.setDefaultColor(new com.les.its.open.area.juncer.msg.param.lookload.dto.DefaultColor());

                //0 黄闪、1灭灯
                if(1 == lampSequenceParam.getPartTimeColor().intValue()){
                    lampSequence.getDefaultColor().setPartTime(RightOfWayColorType.Blank.getColorType());
                }else{
                    lampSequence.getDefaultColor().setPartTime(RightOfWayColorType.FY.getColorType());
                }
                lampSequence.getDefaultColor().setRow(RightOfWayColorType.Green.getColorType());
                lampSequence.getDefaultColor().setNotAtRow(RightOfWayColorType.Red.getColorType());

                lampSequence.setLoseLightTransitions(lampSequenceParam.getLoseLightTransitions());
                lampSequence.setObtainLightTransitions(lampSequenceParam.getObtainLightTransitions());
                lampSequence.setLoseLightTransitionStartups(lampSequenceParam.getLoseLightTransitionStartups());
                lampSequence.setObtainLightTransitionStartups(lampSequenceParam.getObtainLightTransitionStartups());
                lampSequence.setLoseLightTransitionYellowFlashs(lampSequenceParam.getLoseLightTransitionYellowFlashs());
                lampSequence.setObtainLightTransitionYellowFlashs(lampSequenceParam.getObtainLightTransitionYellowFlashs());
                lampSequence.setLoseLightTransitionYellowFlashToNormals(lampSequenceParam.getLoseLightTransitionYellowFlashToNormals());
                lampSequence.setObtainLightTransitionYellowFlashToNormals(lampSequenceParam.getObtainLightTransitionYellowFlashToNormals());
            }

        }

        //生成加载参数
        AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_LAMP_SEQUENCE,
                lampSequenceList, signalInfoOp.get());
        areaMessages.add(areaMessage);


        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

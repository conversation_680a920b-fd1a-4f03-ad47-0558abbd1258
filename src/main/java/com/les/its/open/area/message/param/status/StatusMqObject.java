package com.les.its.open.area.message.param.status;

import java.util.Arrays;
import java.util.Optional;

public enum StatusMqObject {

    TAB_CONTROL_MODE(0x03010100,  "11001", "控制方式切换"),
    TAB_PLAN(0x03010200, "11002", "方案切换"),
    TAB_DAY_PLAN(0x03010300, "11003","日计划切换"),
    TAB_SCHEDULE(0x03010400, "11004","调度计划切换"),
    TAB_STAGE(0x03010500, "11005","阶段切换"),
    TAB_EMERGENCY(0x03010600, "11006","紧急状态切换"),
    TAB_PRIORITY(0x03010700, "11007","优先状态切换"),
    TAB_SHIELD(0x03010800, "11008","屏蔽状态切换"),
    TAB_PROHIBIT(0x03010900, "11009","禁止状态切换"),
    TAB_EMERGENCY_PRIORITY_SHIELD(0x03010A00, "11010","紧急优先屏蔽状态切换"),
    TAB_PHASE_TABLE(0x03010B00, "11011","相位表切换"),
    TAB_CYCLE(0x03020000, "11012", "周期切换"),
    TAB_PHASE_STATUS(0x03030100, "11013", "实时相位/灯组状态"),
    TAB_STAGE_STATUS(0x03030200, "", "实时阶段状态"),
    TAB_PLAN_STATUS(0x03030300, "","实时控制方式/方案/日计划/调度计划"),
    TAB_ACTUATED_PHASE_STATUS(0x03030400, "11014", "感应相位状态"),
    TAB_UI_STATUS(0x03030500, "11015", "灯电流电压状态"),
    TAB_NOTIFY(0x03080100, "11016","数据变更"),
    TAB_TRANSACTION(0x03090100, "11017","事务交易控制"),
    TAB_RTT(0xffffffff, "11018", "信号机RTT状态"),

    TAB_ALARM(0x03040100,"12001", "报警"),
    TAB_FAULT(0x03050100,"12002", "故障"),

    TAB_DEVICE_STATUS(0x03060100, "", "设备状态(电流/电压/温度)"),
    TAB_ENV(0x03060200, "", "环境状态"),
    TAB_DETECTOR_REALTIME(0x03070100, "13001", "交通数据-存在数据"),
    TAB_DETECTOR_STATISTICS(0x03070200, "13002", "交通数据-统计数据"),;

    private int value;
    private String objectId;
    private String des;

    StatusMqObject(int value, String objectId, String des){
        this.value = value;
        this.objectId = objectId;
        this.des = des;
    }

    public int value(){
        return this.value;
    }

    public String objectId() {
        return this.objectId;
    }

    public String des(){
        return this.des;
    }

    public static Optional<StatusMqObject> getType(int orgValue) {

        return Arrays.stream(StatusMqObject.values())
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StartupStage implements DataIndexAble {
    // 无需求进入阶段 (8个子路口配置)
    @NotNull(message = "无需求进入阶段不能为空")
    @Size(min = 8, max = 8, message = "无需求进入阶段数量必须为8")
    @DiscreteValuesList(min = 0, max = 64, message = "无需求进入阶段有效范围为[0,64]")
    private List<Integer> noDemandStages;
    
    // 启动进入阶段 (8个子路口配置)
    @NotNull(message = "启动进入阶段不能为空")
    @Size(min = 8, max = 8, message = "启动进入阶段数量必须为8")
    @DiscreteValuesList(min = 0, max = 64, message = "启动进入阶段有效范围为[0,64]")
    private List<Integer> startupStages;

    // 结束手动定周期时插入阶段需求 (针对每个阶段)
    @NotNull(message = "结束手动定周期时插入阶段需求不能为空")
    @Size(min = 64, max = 64, message = "结束手动定周期时插入阶段需求数量必须为64")
    @DiscreteValuesList(min = 0, max = 1, message = "结束手动定周期时插入阶段需求有效范围为[0,1]")
    private List<Integer> demandsInsertLeavingManualAndFixStages;

    // 启动时插入阶段需求 (针对每个阶段)
    @NotNull(message = "启动时插入阶段需求不能为空")
    @Size(min = 64, max = 64, message = "启动时插入阶段需求数量必须为64")
    @DiscreteValuesList(min = 0, max = 1, message = "启动时插入阶段需求有效范围为[0,1]")
    private List<Integer> demandsInsertStartUpStages;

    // 窗口时间
    @NotNull(message = "窗口时间不能为空")
    @Size(min = 64, max = 64, message = "窗口时间数量必须为64")
    @DiscreteValuesList(min = 0, max = 255, message = "窗口时间有效范围为[0,255]")
    private List<Integer> windowsTimes;

    /**
     * 紧急调用结束后插入需求
     */
    @NotNull(message = "紧急调用结束后插入需求不能为空")
    @Size(min = 64, max = 64, message = "紧急调用结束后插入需求数量必须为64")
    @DiscreteValuesList(min = 0, max = 1, message = "紧急调用结束后插入需求有效范围为[0,1]")
    private List<Integer> demandsInsertLeavingHurryCalls;

    /**
     * 中心控制结末后插入需求
     */
    @NotNull(message = "中心控制结末后插入需求不能为空")
    @Size(min = 64, max = 64, message = "中心控制结末后插入需求数量必须为64")
    @DiscreteValuesList(min = 0, max = 1, message = "中心控制结末后插入需求有效范围为[0,1]")
    private List<Integer> demandsInsertLeavingSystems;

    /**
     * 无条件默认需求
     */
    @NotNull(message = "无条件默认需求不能为空")
    @Size(min = 64, max = 64, message = "无条件默认需求数量必须为64")
    @DiscreteValuesList(min = 0, max = 1, message = "无条件默认需求有效范围为[0,1]")
    private List<Integer> unconditionalDemands;

    @Override
    public int getDataNo() {
        return 1;
    }
}
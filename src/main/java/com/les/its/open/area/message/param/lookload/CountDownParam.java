package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownDisplayParam;
import com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownInfo;

import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 倒计时参数
 */
@Data
public class CountDownParam implements SgpTransAble {
    public static final String MqObjectId = "10014";

    /**
     * 倒计时配置
     */
    @NotNull(message = "{basic.param.countDown.countDown.notNull}")
    @Valid
    private CountdownInfo countDown;

    /**
     * 倒计时显示参数
     */
    @NotNull(message = "{basic.param.countDown.countdownDisplayParams.notNull}")
    @Size(min = 1, max = 32, message = "{basic.param.countDown.countdownDisplayParams.size}")
    @Valid
    private List<CountdownDisplayParam> countdownDisplayParams;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
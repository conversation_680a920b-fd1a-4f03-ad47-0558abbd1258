package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Conditioning;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ConditioningProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_CONDITIONING;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Conditioning conditioning = new Conditioning();
            objects.add(conditioning);
            // 逻辑输出号
            conditioning.setConditioningNo(buf.readUnsignedByte() & 0xff);
            // 结果类型
            conditioning.setOutputType(buf.readUnsignedByte() & 0xff);
            // 结果编号
            conditioning.setOutputNo(buf.readUnsignedByte() & 0xff);
            // 操作符1 (0:无操作, 1:NOT)
            conditioning.setOp1(buf.readUnsignedByte() & 0xff);
            // 变量类型1
            conditioning.setParamType1(buf.readUnsignedByte() & 0xff);
            // 变量编号1
            conditioning.setParamNo1(buf.readUnsignedByte() & 0xff);
            // 操作符2 (可以是 AND, OR, AND NOT, OR NOT)
            conditioning.setOp2(buf.readUnsignedByte() & 0xff);
            // 变量类型2
            conditioning.setParamType2(buf.readUnsignedByte() & 0xff);
            // 变量编号2
            conditioning.setParamNo2(buf.readUnsignedByte() & 0xff);
            // 操作符3
            conditioning.setOp3(buf.readUnsignedByte() & 0xff);
            // 变量类型3
            conditioning.setParamType3(buf.readUnsignedByte() & 0xff);
            // 变量编号3
            conditioning.setParamNo3(buf.readUnsignedByte() & 0xff);
            // 操作符4
            conditioning.setOp4(buf.readUnsignedByte() & 0xff);
            // 变量类型4
            conditioning.setParamType4(buf.readUnsignedByte() & 0xff);
            // 变量编号4
            conditioning.setParamNo4(buf.readUnsignedByte() & 0xff);
            // 条件计算名称
            conditioning.setConditioningName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Conditioning)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Conditioning conditioning
                    = (Conditioning)objects.get(i);

            // 条件计算编号
            buf.writeByte(conditioning.getConditioningNo());
            // 结果类型
            buf.writeByte(conditioning.getOutputType());
            // 结果编号
            buf.writeByte(conditioning.getOutputNo());
            // 操作符1 (0:无操作, 1:NOT)
            buf.writeByte(conditioning.getOp1());
            // 变量类型1
            buf.writeByte(conditioning.getParamType1());
            // 变量编号1
            buf.writeByte(conditioning.getParamNo1());
            // 操作符2 (可以是 AND, OR, AND NOT, OR NOT)
            buf.writeByte(conditioning.getOp2());
            // 变量类型2
            buf.writeByte(conditioning.getParamType2());
            // 变量编号2
            buf.writeByte(conditioning.getParamNo2());
            // 操作符3
            buf.writeByte(conditioning.getOp3());
            // 变量类型3
            buf.writeByte(conditioning.getParamType3());
            // 变量编号3
            buf.writeByte(conditioning.getParamNo3());
            // 操作符4
            buf.writeByte(conditioning.getOp4());
            // 变量类型4
            buf.writeByte(conditioning.getParamType4());
            // 变量编号4
            buf.writeByte(conditioning.getParamNo4());
            // 条件计算名称
            {
                byte[] name = new byte[64];
                byte[] bytes = conditioning.getConditioningName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 15 + 64;
    }

    @Override
    public Class dataClazz() {
        return Conditioning.class;
    }
}

package com.les.its.open.area.message.param.lookload.sub;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 启动阶段参数
 * 用于配置子路口的启动阶段和无需求进入阶段
 */
@Data
public class StartupStage {
    /**
     * 子路口号
     * [1,8]
     */
    @NotNull(message = "{basic.param.startupStage.crossingSeqNo.notNull}")
    @Min(value = 1, message = "{basic.param.startupStage.crossingSeqNo.min}")
    @Max(value = 8, message = "{basic.param.startupStage.crossingSeqNo.max}")
    private Integer crossingSeqNo;

    /**
     * 无需求进入阶段
     * [0,64],,0未配置(非控子路口)
     * 8个子路口配置
     */
    @NotNull(message = "{basic.param.startupStage.noDemandStage.notNull}")
    @Min(value = 0, message = "{basic.param.startupStage.noDemandStage.min}")
    @Max(value = 64, message = "{basic.param.startupStage.noDemandStage.max}")
    private Integer noDemandStage;

    /**
     * 启动进入阶段
     * [0,64],0未配置(非控子路口)
     * 8个子路口配置
     */
    @NotNull(message = "{basic.param.startupStage.startupStage.notNull}")
    @Min(value = 0, message = "{basic.param.startupStage.startupStage.min}")
    @Max(value = 64, message = "{basic.param.startupStage.startupStage.max}")
    private Integer startupStage;
}

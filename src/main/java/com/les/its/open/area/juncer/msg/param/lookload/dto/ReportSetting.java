package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportSetting implements DataIndexAble {
    // 实时相位/灯组状态
    @NotNull(message = "实时相位/灯组状态不能为空")
    @Range(min = 0, max = 1, message = "实时相位/灯组状态有效范围为[0,1]")
    private Integer phaseStatus;
    
    // 实时阶段状态
    @NotNull(message = "实时阶段状态不能为空")
    @Range(min = 0, max = 1, message = "实时阶段状态有效范围为[0,1]")
    private Integer stageStatus;
    
    // 上传间隔
    @NotNull(message = "上传间隔不能为空")
    @Range(min = 0, max = 6000, message = "上传间隔有效范围为[0,6000]")
    private Integer stageStatusInterval;
    
    // 实时控制方式/方案/日计划/调度计划
    @NotNull(message = "实时控制方式/方案/日计划/调度计划不能为空")
    @Range(min = 0, max = 1, message = "实时控制方式/方案/日计划/调度计划有效范围为[0,1]")
    private Integer planStatus;
    
    // 上传间隔
    @NotNull(message = "上传间隔不能为空")
    @Range(min = 0, max = 600, message = "上传间隔有效范围为[0,600]")
    private Integer planStatusInterval;
    
    // 设备状态(电流/电压/温度)
    @NotNull(message = "设备状态(电流/电压/温度)不能为空")
    @Range(min = 0, max = 1, message = "设备状态(电流/电压/温度)有效范围为[0,1]")
    private Integer deviceStatus;
    
    // 上传间隔
    @NotNull(message = "上传间隔不能为空")
    @Range(min = 0, max = 600, message = "上传间隔有效范围为[0,600]")
    private Integer deviceStatusInterval;
    
    // 交通数据-存在数据
    @NotNull(message = "交通数据-存在数据不能为空")
    @Range(min = 0, max = 1, message = "交通数据-存在数据有效范围为[0,1]")
    private Integer existenceData;
    
    // 交通数据-统计数据
    @NotNull(message = "交通数据-统计数据不能为空")
    @Range(min = 0, max = 1, message = "交通数据-统计数据有效范围为[0,1]")
    private Integer statisticalData;
    
    // 上传间隔
    @NotNull(message = "上传间隔不能为空")
    @Range(min = 0, max = 6000, message = "上传间隔有效范围为[0,6000]")
    private Integer statisticalDataInterval;
    
    // 灯电压电流状态
    @NotNull(message = "灯电压电流状态不能为空")
    @Range(min = 0, max = 1, message = "灯电压电流状态有效范围为[0,1]")
    private Integer lampUI;
    
    // 上传间隔
    @NotNull(message = "上传间隔不能为空")
    @Range(min = 0, max = 6000, message = "上传间隔有效范围为[0,6000]")
    private Integer lampUIInterval;

    @Override
    public int getDataNo() {
        return 1;
    }
}
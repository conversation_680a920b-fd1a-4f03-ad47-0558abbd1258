package com.les.its.open.area.message.handler;


import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.TabLookParam;
import com.les.its.open.area.juncer.msg.param.TabLookParamErr;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.MsgTypeCat;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 调看参数返回处理
 */
@Service
@Slf4j
public class ParamHandler {


     @Autowired
     private ControllerService controllerService;

     @Autowired
     private MessageSender messageSender;


     @EventListener
     @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
     public void processTabLookParam(TabOutBase tabOutBase)
     {
          log.debug("收到信号机{}-{}", tabOutBase.getControllerId(), tabOutBase);
          Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabOutBase.getControllerId());

          if(signalInfoOp.isEmpty()){
               return;
          }
          AreaMessage areaMessage = AreaMessage.genMqMessage(tabOutBase, signalInfoOp.get());

          MsgType msgType = tabOutBase.getMsgType();
          StringBuilder routingKey = new StringBuilder("All.IP.");
          boolean notify = false;
          if(msgType == MsgType.TAB_LOOK_PARAM ) {
               if(tabOutBase instanceof TabLookParam tabLookParam) {
                    notify = true;
                    //转换子参数
                    areaMessage.setMsg3((tabLookParam.getParamMsgType().getCode() >> 8) & 0xFF);
                    areaMessage.setMsg4((tabLookParam.getParamMsgType().getCode()) & 0xFF);

                    routingKey.append(String.format("PARAM.%s", tabLookParam.getAClass().getSimpleName().toUpperCase()));
               }
          }else if(msgType == MsgType.TAB_LOOK_PARAM_ERR) {
               if(tabOutBase instanceof TabLookParamErr tabLookParamErr) {
                    notify = true;
                    //转换子参数
                    areaMessage.setMsg3((tabLookParamErr.getParamMsgType().getCode() >> 8) & 0xFF);
                    areaMessage.setMsg4((tabLookParamErr.getParamMsgType().getCode()) & 0xFF);

                    routingKey.append(String.format("PARAM.%s", tabLookParamErr.getClass().getSimpleName().toUpperCase()));
               }
          }else if(msgType == MsgType.TAB_LOAD) {
               if(tabOutBase instanceof TabLoad tabLoad) {
                    notify = true;
                    //转换子参数
                    areaMessage.setMsg3((tabLoad.getParamMsgType().getCode() >> 8) & 0xFF);
                    areaMessage.setMsg4((tabLoad.getParamMsgType().getCode()) & 0xFF);

                    routingKey.append(String.format("PARAM.%s", tabLoad.getClass().getSimpleName().toUpperCase()));
               }
          }else {
               int msg1 = (tabOutBase.getMsgType().getCode() >> 24) & 0xff;
               if(MsgTypeCat.CAT_STATUS.getCode() == msg1) {
                    notify = true;
                    routingKey.append(String.format("BROADCAST.%s", tabOutBase.getClass().getSimpleName().toUpperCase()));
               }else  if(MsgTypeCat.CAT_CMD.getCode() == msg1) {
                    notify = true;
                    routingKey.append(String.format("COMMAND.%s", tabOutBase.getClass().getSimpleName().toUpperCase()));
               }
          }

          if(notify){
               routingKey.append(String.format(".%d.%d", signalInfoOp.get().getNoArea(), signalInfoOp.get().getNoJunc()));;
               //messageSender.send(routingKey.toString(), areaMessage);
          }
     }

}

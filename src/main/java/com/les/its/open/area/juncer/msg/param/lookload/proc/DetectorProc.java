package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Detector;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class DetectorProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_DETECTOR;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Detector detector = new Detector();
            objects.add(detector);

            detector.setDetectorNo(buf.readUnsignedByte() & 0xff);
            detector.setDetectorType(buf.readUnsignedByte() & 0xff);
            detector.setVolumeCollectCycle(buf.readUnsignedShortLE());
            detector.setOccupancyCollectCycle(buf.readUnsignedShortLE());

            //安裝位置
            {
                byte[] location = new byte[128];
                buf.readBytes(location);
                detector.setInstallLocation(new String(location, StandardCharsets.UTF_8).trim());
            }

            //逻辑输入名称
            {
                byte[] logicName = new byte[64];
                buf.readBytes(logicName);
                detector.setLogicInputName(new String(logicName, StandardCharsets.UTF_8).trim());
            }

            detector.setDetectorDelay(buf.readUnsignedShortLE());
            detector.setDetectorExtend(buf.readUnsignedByte() & 0xff);
            detector.setDetectorQueueLimit(buf.readUnsignedByte() & 0xff);
            detector.setDetectorInvert(buf.readUnsignedByte() & 0xff);
            detector.setDetectorNoActivity(buf.readUnsignedShortLE() & 0xff);
            detector.setDetectorMaxPresence(buf.readUnsignedShortLE());
            detector.setDetectorErraticCounts(buf.readUnsignedByte() & 0xff);
            detector.setDetectorFailOperation(buf.readUnsignedByte() & 0xff);
            detector.setPedestrianButtonPushTime(buf.readUnsignedByte() & 0xff);
            detector.setDetectorPairedDetector(buf.readUnsignedByte() & 0xff);
            detector.setDetectorPairedDetectorSpacing(buf.readUnsignedShortLE());
            detector.setDetectorAvgVehicleLength(buf.readUnsignedShortLE());
            detector.setDetectorLength(buf.readUnsignedShortLE());
            detector.setDetectorState(buf.readUnsignedByte() & 0xff);
            detector.setDetectorHold(buf.readUnsignedByte() & 0xff);
            detector.setDetectorDuration(buf.readUnsignedByte() & 0xff);
            detector.setDetectorOptions(buf.readUnsignedByte() & 0xff);
            detector.setDetectorOptions2(buf.readUnsignedByte() & 0xff);
        }

        buf.release();
        return Optional.of(objects);

    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Detector)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Detector detector
                    = (Detector)objects.get(i);
            buf.writeByte(detector.getDetectorNo());
            buf.writeByte(detector.getDetectorType());
            buf.writeShortLE(detector.getVolumeCollectCycle());
            buf.writeShortLE(detector.getOccupancyCollectCycle());

            {
                byte[] installLocation = new byte[128];
                byte[] bytes = detector.getInstallLocation().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, installLocation, 0, Math.min(bytes.length, 128));
                buf.writeBytes(installLocation);
            }

            {
                byte[] name = new byte[64];
                byte[] bytes = detector.getLogicInputName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }

            buf.writeShortLE(detector.getDetectorDelay());
            buf.writeByte(detector.getDetectorExtend());
            buf.writeByte(detector.getDetectorQueueLimit());
            buf.writeByte(detector.getDetectorInvert());
            buf.writeShortLE(detector.getDetectorNoActivity());
            buf.writeShortLE(detector.getDetectorMaxPresence());
            buf.writeByte(detector.getDetectorErraticCounts());
            buf.writeByte(detector.getDetectorFailOperation());
            buf.writeByte(detector.getPedestrianButtonPushTime());
            buf.writeByte(detector.getDetectorPairedDetector());
            buf.writeShortLE(detector.getDetectorPairedDetectorSpacing());
            buf.writeShortLE(detector.getDetectorAvgVehicleLength());
            buf.writeShortLE(detector.getDetectorLength());
            buf.writeByte(detector.getDetectorState());
            buf.writeByte(detector.getDetectorHold());
            buf.writeByte(detector.getDetectorDuration());
            buf.writeByte(detector.getDetectorOptions());
            buf.writeByte(detector.getDetectorOptions2());
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 222;
    }

    @Override
    public Class dataClazz() {
        return Detector.class;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class BoardInfo implements DataIndexAble {

    // 主控板序列号
    @NotNull
    private String controlBoardSerialNumber;

    // 检测板最大数
    @Min(value = 0, message = "检测板最小个数为0")
    @Max(value = 4, message = "检测板最大个数为4")
    private int detectionBoardMaxNumber;

    // 信息板最大数
    @Min(value = 0, message = "信息板最小个数为0")
    @Max(value = 1, message = "信息板最大个数为1")
    private int informationBoardMaxNumber;

    // 灯驱板最大数
    @Min(value = 1, message = "灯驱板最小个数为1")
    @Max(value = 12, message = "灯驱板最大个数为12")
    private int lampDriverBoardMaxNumber;

    // 检测板信息
    @Valid
    @NotNull(message = "检测板信息不能为空")
    @Size(min = 4, max = 4, message = "检测板信息个数应该为4")
    private List<DetectionBoard> detectionBoards;

    // 信息板信息
    @Valid
    @NotNull(message = "信息板信息不能为空")
    private InformationBoard informationBoard;

    // 灯控板信息
    @Valid
    @NotNull(message = "灯控板信息不能为空")
    @Size(min = 12, max = 12, message = "灯控板信息个数应该为12")
    private List<LampDriverBoard> lampDriverBoards;

    // 灯控板灯组数
    @DigitsInSet(acceptedValues = {4, 6, 8}, message = "灯组数必须是4、6或8")
    private int lampGroupNumber;

    @Override
    public int getDataNo() {
        return 1;
    }
}


package com.les.its.open.area.message;


import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.les.its.open.event.AckManager.response.ResponseMessage;
import com.les.its.open.event.AckManager.response.ResponseStatus;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description: 用于20999 数据项发送
 * @Date: create in 2021/5/12 10:54
 */
@Service
@Slf4j
public class OpenLesSender {

    public final InMsgService inMsgService;

    public OpenLesSender(InMsgService inMsgService) {
        this.inMsgService = inMsgService;
    }

    /**
     * 发送指定的报文数据项
     * @param areaMessages
     */
    public JsonResult<?> sendMsgAsync(List<AreaMessage> areaMessages){
        if(areaMessages == null || areaMessages.isEmpty() ){
            return JsonResult.error("发送参数异常-" + areaMessages);
        }

        //根据数据项进行同步等待
        List<InvokeFuture> invokeFutures = Collections.synchronizedList(new ArrayList<>());
        areaMessages.forEach(
            requestMsg -> {
                Optional<InvokeFuture> invokeFutureSendOp = inMsgService.procMqMessage(requestMsg);
                invokeFutureSendOp.ifPresent(invokeFutures::add);
            }
        );
        return new JsonResult<>(true, "异步发送结果", invokeFutures);
    }


    /**
     * 发送指定的报文数据项,根据索引数据项
     *
     * @param areaMessages
     */
    public JsonResult<?> sendMsgAsync(List<AreaMessage> areaMessages, int dataIndex) {
        if (areaMessages == null || areaMessages.isEmpty() || dataIndex < 0 || dataIndex >= areaMessages.size()) {
            return JsonResult.error("发送参数异常-" + areaMessages);
        }

        //根据数据项进行同步等待
        List<InvokeFuture> invokeFutures = Collections.synchronizedList(new ArrayList<>());

        AreaMessage areaMessage = areaMessages.get(dataIndex);
        Optional<InvokeFuture> invokeFutureSendOp = inMsgService.procMqMessage(areaMessage);
        if(invokeFutureSendOp.isPresent()){
            invokeFutures.add(invokeFutureSendOp.get());
            return new JsonResult<>(true, "异步发送结果", invokeFutures);
        }else{
            return JsonResult.error("发送参数异常-" + areaMessages);
        }
    }


    /**
     * 处理数据数据项
     *
     * @param invokeFutures
     * @return
     */
    public JsonResult<?> analyzeData(List<InvokeFuture> invokeFutures){
        Optional<InvokeFuture> invokeFuture = invokeFutures.stream().filter(
                future -> !future.isDone()
        ).findAny();
        if(invokeFuture.isPresent()){
            return new JsonResult<>(false, "还有数据项没有处理完整");
        }

        //根据数据项进行同步等待
        final List<JsonResult<?>> results = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger failedCount = new AtomicInteger(0);
        //等待应答
        StringBuilder errorMsg = new StringBuilder();
        invokeFutures.parallelStream().forEach(
                future -> {
                    try {
                        LocalDateTime startTime = LocalDateTime.now();
                        log.trace("开始等待-{}应答-{}", future.getRequest().getAckKey(), startTime);
                        ResponseMessage response = future.waitResponse();
                        LocalDateTime endTime = LocalDateTime.now();
                        long millis = Duration.between(endTime, startTime).toMillis();
                        log.trace("{}- 收到了应答-{},花费时间-{},报文中时间-{} 数据应答项-{}", future.getRequest().getAckKey(), endTime, millis,
                                System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);
                        /**解析应答数据项*/
                        if (response.getResponseStatus() != ResponseStatus.SUCCESS) {
                            JsonResult<?> error = new JsonResult<>(false, response.getResponseStatus().des(), future.getRequest().getAckKey());
                            log.error("{}", error);
                            results.add(error);
                            failedCount.getAndIncrement();
                            errorMsg.append(response.getResponseStatus().des()).append(";");
                        } else {
                            JsonResult<?> result = new JsonResult<>(true, future.getRequest().getAckKey() + "花费时间" + millis + "ms",
                                    response.getResponseObject());
                            log.trace("{}", result);
                            results.add(result);
                        }
                    } catch (InterruptedException e) {
                        log.error("等待异常", e);
                    }
                }
        );

        String msg = errorMsg.isEmpty() ? "存在请求异常的数据项," : errorMsg.toString();
        return new JsonResult<>(failedCount.get() == 0,
                failedCount.get() == 0 ?
                "数据处理完成" : msg, results);
    }
}

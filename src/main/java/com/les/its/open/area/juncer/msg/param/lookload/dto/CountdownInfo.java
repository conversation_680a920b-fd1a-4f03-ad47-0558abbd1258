package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountdownInfo implements DataIndexAble {

    // 倒计时启用类型
    @NotNull(message = "{juncer.param.countdownInfo.enableType.notNull}")            
    @Range(min = 0, max = 3, message = "{juncer.param.countdownInfo.enableType.range}")
    private Integer enableType;
    
    // 通信波特率
    @NotNull(message = "{juncer.param.countdownInfo.baudRate.notNull}")
    @Range(min = 0, max = 3, message = "{juncer.param.countdownInfo.baudRate.range}")
    private Integer baudRate;
    
    // 校验位
    @NotNull(message = "{juncer.param.countdownInfo.parity.notNull}")
    @Range(min = 0, max = 2, message = "{juncer.param.countdownInfo.parity.range}")
    private Integer parity;
    
    // 脉冲亮屏宽度
    @NotNull(message = "{juncer.param.countdownInfo.pulseBrightWidth.notNull}")
    @Range(min = 0, max = 5, message = "{juncer.param.countdownInfo.pulseBrightWidth.range}")
    private Integer pulseBrightWidth;
    
    // 脉冲亮屏灯色
    @NotNull(message = "{juncer.param.countdownInfo.pulseBrightColor.notNull}")
    @Range(min = 0, max = 1, message = "{juncer.param.countdownInfo.pulseBrightColor.range}")
    private Integer pulseBrightColor;
    
    // 脉冲灭屏宽度
    @NotNull(message = "{juncer.param.countdownInfo.pulseDarkWidth.notNull}")
    @Range(min = 0, max = 5, message = "{juncer.param.countdownInfo.pulseDarkWidth.range}")
    private Integer pulseDarkWidth;
    
    // 脉冲灭屏灯色
    @NotNull(message = "{juncer.param.countdownInfo.pulseDarkColor.notNull}")
    @Range(min = 0, max = 2, message = "{juncer.param.countdownInfo.pulseDarkColor.range}")
    private Integer pulseDarkColor;
    
    // 触发红灯倒计时机动车标志
    @NotNull(message = "{juncer.param.countdownInfo.redVehicleFlag.notNull}")
    @Range(min = 0, max = 1, message = "{juncer.param.countdownInfo.redVehicleFlag.range}")
    private Integer redVehicleFlag;
    
    // 触发红灯倒计时机动车剩余时间
    @NotNull(message = "{juncer.param.countdownInfo.redVehicleTime.notNull}")
    @Range(min = 1, max = 20, message = "{juncer.param.countdownInfo.redVehicleTime.range}")
    private Integer redVehicleTime;
    
    // 触发绿灯倒计时机动车标志
    @NotNull(message = "{juncer.param.countdownInfo.greenVehicleFlag.notNull}")
    @Range(min = 0, max = 1, message = "{juncer.param.countdownInfo.greenVehicleFlag.range}")
    private Integer greenVehicleFlag;
    
    // 触发绿灯倒计时机动车剩余时间
    @NotNull(message = "{juncer.param.countdownInfo.greenVehicleTime.notNull}")
    @Range(min = 1, max = 20, message = "{juncer.param.countdownInfo.greenVehicleTime.range}")
    private Integer greenVehicleTime;
    
    // 触发红灯倒计时行人标志
    @NotNull(message = "{juncer.param.countdownInfo.redPedestrianFlag.notNull}")
    @Range(min = 0, max = 1, message = "{juncer.param.countdownInfo.redPedestrianFlag.range}")
    private Integer redPedestrianFlag;
    
    // 触发红灯倒计时行人剩余时间
    @NotNull(message = "{juncer.param.countdownInfo.redPedestrianTime.notNull}")
    @Range(min = 1, max = 20, message = "{juncer.param.countdownInfo.redPedestrianTime.range}")
    private Integer redPedestrianTime;
    
    // 触发绿灯倒计时行人标志
    @NotNull(message = "{juncer.param.countdownInfo.greenPedestrianFlag.notNull}")
    @Range(min = 0, max = 1, message = "{juncer.param.countdownInfo.greenPedestrianFlag.range}")
    private Integer greenPedestrianFlag;
    
    // 触发绿灯倒计时行人剩余时间
    @NotNull(message = "{juncer.param.countdownInfo.greenPedestrianTime.notNull}")
    @Range(min = 1, max = 20, message = "{juncer.param.countdownInfo.greenPedestrianTime.range}")
    private Integer greenPedestrianTime;
    
    // 感应模式触发倒计时标志
    @NotNull(message = "{juncer.param.countdownInfo.inductionFlag.notNull}")
    @Range(min = 0, max = 1, message = "{juncer.param.countdownInfo.inductionFlag.range}")
    private Integer inductionFlag;

    @Override
    public int getDataNo() {
        return 1;
    }
}
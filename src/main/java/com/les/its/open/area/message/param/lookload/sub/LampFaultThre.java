package com.les.its.open.area.message.param.lookload.sub;

import org.hibernate.validator.constraints.Range;

import com.les.its.open.area.juncer.msg.param.lookload.dto.Threshold;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 灯检测阈值配置
 */
@Data
public class LampFaultThre {
    /**
     * 灯组编号
     */
    @NotNull(message = "{basic.param.lampFaultThre.lampGroupNo.notNull}")
    @Range(min = 1, max = 64, message = "{basic.param.lampFaultThre.lampGroupNo.range}")
    private Integer lampGroupNo;

    /**
     * 检测标记
     * 0:不检测, 1:检测
     */
    @NotNull(message = "{basic.param.lampGroup.detectFlag.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.lampGroup.detectFlag.range}")
    private Integer detectFlag;

    /**
     * 绿灯电压电流阈值配置
     */
    @NotNull(message = "{basic.param.lampFaultThre.greenThreshold.notNull}")
    @Valid
    private Threshold greenThreshold;

    /**
     * 黄灯电压电流阈值配置
     */
    @NotNull(message = "{basic.param.lampFaultThre.yellowThreshold.notNull}")
    @Valid
    private Threshold yellowThreshold;

    /**
     * 红灯电压电流阈值配置
     */
    @NotNull(message = "{basic.param.lampFaultThre.redThreshold.notNull}")
    @Valid
    private Threshold redThreshold;
} 
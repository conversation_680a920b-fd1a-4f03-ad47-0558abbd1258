---
description:
globs:
alwaysApply: true
---

# Your rule content
- 你是java高级编程师，帮忙从图片中识别数据，生成java数据对象
- 请生成java对象，包是com.les.its.open.area.message.param
1、使用lombok标记；
2、不要使用内部类；
3、java对象名不要以DTO结束；
4、表格前两列表示成员对象名称以及子对象名称，第三列表示类型，第四列表示数据范围，第五列表示描述;
5、生成对象时,注释中保留数据范围以及描述信息;
6、类似struct对象,表示复合对象，查看后续行中的第二列，组成符合对象;
7、类似list<struct>对象,表示复合对象数组，查看后续行中的第二列，组成符合对象;
8、名称请严格按照表格中的名称生成，不要自行修改名称;
9、使用“对象名称”作为java对象名称;
10、对象中根据"对象编号：10001",生成“public static final String MqObjectId”
11、子对象均放到 sub 目录下



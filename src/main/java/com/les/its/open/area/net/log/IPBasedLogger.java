package com.les.its.open.area.net.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

public class IPBasedLogger {
    private static final Logger logger = LoggerFactory.getLogger(IPBasedLogger.class);

    public static void logMessage(String clientIP, int noArea, int noJuc, String format, Object... argumentsString) {
        try {
            MDC.put("clientIP", sanitizeIP(clientIP, noArea, noJuc));
            logger.error(format, argumentsString);
        } finally {
            MDC.remove("clientIP");
        }
    }

    private static String sanitizeIP(String ip, int noArea, int noJuc) {
        // 清理IP地址，移除非法字符
        String ipClean =  ip.replaceAll("[^0-9.]", "_");
        return String.format("%02d-%03d-%s", noArea, noJuc, ipClean);
    }
}
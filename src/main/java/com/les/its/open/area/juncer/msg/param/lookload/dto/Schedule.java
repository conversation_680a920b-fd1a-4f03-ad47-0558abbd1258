package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Schedule implements DataIndexAble {
    // 调度表编号
    @NotNull(message = "调度表编号不能为空")
    @Range(min = 1, max = 128, message = "调度表编号有效范围为[1,128]")
    private Integer scheduleNo;
    
    // 子路口号
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号有效范围为[1,8]")
    private Integer crossingSeqNo;
    
    // 优先级 (0:基本优先级，优先级相同时调度表号最小最大)
    @NotNull(message = "优先级不能为空")
    @Range(min = 0, max = 255, message = "优先级有效范围为[0,255]")
    private Integer priority;
    
    // 星期值
    @NotNull(message = "星期值不能为空")
    private Integer week;
    
    // 月份
    @NotNull(message = "月份不能为空")
    private Integer month;
    
    // 日期
    @NotNull(message = "日期不能为空")
    private Long day;
    
    // 日计划号
    @NotNull(message = "日计划号不能为空")
    @Range(min = 1, max = 128, message = "日计划号有效范围为[1,128]")
    private Integer dayPlanNo;
    
    // 名称
    @NotNull(message = "名称不能为空")
    private String name;

    @Override
    public int getDataNo() {
        return scheduleNo;
    }
}
package com.les.its.open.area.message.handler.status;

import com.les.its.open.area.juncer.msg.status.TabStageStatus;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.service.WsMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class TabStageStatusHandler {

    private final ControllerService controllerService;

    private final CrossingService crossingService;

    private final StatusHandler statusHandler;

    private final WsMessageService wsMessageService;

    public TabStageStatusHandler(ControllerService controllerService, CrossingService crossingService,
                                 <PERSON><PERSON>and<PERSON> statusHandler, WsMessageService wsMessageService) {
        this.controllerService = controllerService;
        this.crossingService = crossingService;
        this.statusHandler = statusHandler;
        this.wsMessageService = wsMessageService;
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processTabStageStatus(TabStageStatus tabStageStatus) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabStageStatus.getControllerId());

        if (signalInfoOp.isEmpty()) {
            return;
        }

        wsMessageService.recvStageStatus(tabStageStatus.getControllerId(), tabStageStatus);

    }


}

package com.les.its.open.area.message.handler.status;

import com.les.its.open.area.juncer.msg.cmd.TabTransactionCmd;
import com.les.its.open.area.juncer.msg.status.TabTransaction;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.les.its.open.area.message.param.cmd.TransactionType;
import com.les.its.open.area.message.param.cmd.TransactionVerifyStatus;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.les.its.open.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class TabTransactionHandler {


    private final ControllerService controllerService;

    private final CrossingService crossingService;

    private final MessageSender messageSender;

    private final WsMessageService wsMessageService;

    public TabTransactionHandler(ControllerService controllerService, CrossingService crossingService,
                                 MessageSender messageSender, WsMessageService wsMessageService) {
        this.controllerService = controllerService;
        this.crossingService = crossingService;
        this.messageSender = messageSender;
        this.wsMessageService = wsMessageService;
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processTabTransaction(TabTransaction tabTransaction) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabTransaction.getControllerId());

        if (signalInfoOp.isEmpty()) {
            return;
        }

    }


    /**
     * 临时发送业务成功标记
     * @param controllerId
     */
    public void sendTabTransactionSimu(String controllerId){

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);

        if (signalInfoOp.isEmpty()) {
            return;
        }

        //构建控制方式报文
        TabTransaction tabTransaction = new TabTransaction();
        tabTransaction.setControllerId(controllerId);
        tabTransaction.setMsgType(MsgType.TAB_TRANSACTION);
        tabTransaction.setLocalDateTime(LocalDateTime.now());

        //路口控制方式
        tabTransaction.setTransactionCreate(TransactionType.DONE.getCode());
        tabTransaction.setTransactionTimeout(10000);
        tabTransaction.setTransactionStatus(TransactionVerifyStatus.DONE_WITH_NO_ERROR.getCode());
        tabTransaction.setTransactionErrorCode(0);

        //生成数据项
        MqMessage mqMessage =
                OpenLesMqUtils.buildPushMqMsg(controllerId, CmdMqObject.TAB_TRANSACTION_CMD.objectId(), tabTransaction);

        messageSender.sendNats(controllerId,
                TabTransactionCmd.class.getSimpleName().toLowerCase(),
                signalInfoOp.get().getNoArea(), signalInfoOp.get().getNoJunc(), mqMessage);

    }


}

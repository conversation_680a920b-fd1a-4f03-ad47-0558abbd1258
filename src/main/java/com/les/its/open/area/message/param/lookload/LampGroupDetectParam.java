package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.UIThreshold;
import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.LampFaultThre;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 灯组参数信息
 */
@Data
public class LampGroupDetectParam implements SgpTransAble {
    public static final String MqObjectId = "10020";


    /**
     * 灯检测电压电流阈值配置
     */
    @NotNull(message = "{basic.param.lampGroupParam.uiThreshold.notNull}")
    //@Valid
    private UIThreshold uiThreshold;

    /**
     * 灯检测阈值配置
     */
    @NotNull(message = "{basic.param.lampGroupParam.lampFaultThres.notNull}")
    //@Valid
    private List<LampFaultThre> lampFaultThres;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabEmergency;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabEmergencyProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_EMERGENCY;
    }

    @Override
    public int oneItemSize() {
        return 3;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
           TabEmergency tabEmergency = new TabEmergency();
           ByteBuf buf = Unpooled.buffer(body.length);
           buf.writeBytes(body);
           tabEmergency.setEmergencyNo(buf.readUnsignedByte() & 0xff);
           tabEmergency.setApplyStatus(buf.readUnsignedByte() & 0xff);
           tabEmergency.setExecuteStatus(buf.readUnsignedByte() & 0xff);
           buf.release();
           return Optional.of(tabEmergency);
        }

        return Optional.empty();
    }
}

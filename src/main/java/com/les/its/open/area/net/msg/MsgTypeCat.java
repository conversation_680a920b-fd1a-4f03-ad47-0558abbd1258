package com.les.its.open.area.net.msg;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum MsgTypeCat {

    /*链路*/
    CAT_LINK(0x01, "链路状态类"),
    CAT_PARAM(0x02, "配置参数类"),
    CAT_STATUS(0x03, "实时状态类"),
    CAT_CMD(0x04, "控制命令类"),
    ;

    private final int code;
    private final String description;

    MsgTypeCat(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.format("%s(0x%08X) - %s", name(), code, description);
    }


    /**
     * 获取数据所属类别
     * @param msgType
     * @return
     */
    public static Optional<MsgTypeCat> parseMsgTypeCat(MsgType msgType){
        final int checkId = ((msgType.getCode() >> 24) & 0xff);
        return Arrays.stream(MsgTypeCat.values()).filter(
                msgTypeCat -> msgTypeCat.getCode() == checkId
        ).findAny();
    }

}

package com.les.its.open.area.message;


import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.message.dbsave.DataSaveBean;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.handler.status.TabTransactionHandler;
import com.les.its.open.area.message.log.SignalBasedLogger;
import com.les.its.open.area.message.mq.*;
import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.les.its.open.event.AckManager.response.ResponseStatus;
import com.les.its.open.event.MessagePublisher;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.les.its.open.transport.MessageSender;
import com.myweb.commons.persistence.JsonResult;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.les.its.open.area.message.utils.OpenLesMqUtils.buildErrorMqResponseMsg;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:18
 */
@Service
@Slf4j
public class MqMessageProcess implements ApplicationContextAware {

    private ApplicationContext ac;

    /**
     * 存储数据请求处理对象
     */
    @Getter
    private final Map<String, MqMsgBaseHandler> mqMsgBaseHandlerMap = new ConcurrentHashMap<>();

    /**
     * 用于记录当前正在处理的报文数据对象
     */
    private final Map<Long, MqProcessingMsg> mqProcessingMsgMap = new ConcurrentHashMap<>();

    /**
     * 设置数据项反向查找mq数据报文
     */
    private final Map<String, Long> mqInvokeFutureId2Msg = new ConcurrentHashMap<>();

    @Autowired
    private MessagePublisher messagePublisher;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private OpenLesSender openLesSender;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private WsMessageService wsMessageService;

    @Autowired
    private TabTransactionHandler tabTransactionHandler;

    @PostConstruct
    public void setDataProcessMap(){
        Map<String, MqMsgBaseHandler> beansOfType = ac.getBeansOfType(MqMsgBaseHandler.class);
        beansOfType.values().forEach(
           mqMsgBaseHandler -> {
               mqMsgBaseHandlerMap.put(mqMsgBaseHandler.getObjectId(), mqMsgBaseHandler);
           }
        );
    }

    @RabbitListener(queues = {"${global.mq.exchange2.cmdQueue}"})
    public void consumer(String cmd) {
        try {
            MqMessage cmdObject = JSONObject.parseObject(cmd, MqMessage.class);
            cmdObject.setTimeStampRev(System.currentTimeMillis());
            cmdObject.setAckMsg(false);
            log.error("####收到命令-{}", cmdObject);
            messagePublisher.publishMessage(cmdObject);
            SignalBasedLogger.logMessage(cmdObject.getSignalControllerID(), "收到命令-{}", cmd);
        }catch (Exception e){
            log.error("解析MQ报文出现异常", e);
        }
    }

    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    @EventListener
    public void mqMessageProcess(MqMessage mqMessage){
        if (mqMessage == null || mqMessage.getObjectId() == null) {
            log.error("异常的mq数据请求-{}", mqMessage);
            return;
        }

        if (mqMessage.getSignalControllerID() == null) {
            log.error("异常的mq数据请求,信号机编号异常-{}", mqMessage);
            return;
        }

        //这是用于同步mq消息的应答
        if(mqMessage.isAckMsg()){
            return;
        }

        // 接收到nats消息
        wsMessageService.recvMsgNats(mqMessage.getSignalControllerID(), mqMessage);

        if (!controllerService.processMqMsg(mqMessage.getSignalControllerID())) {
            log.error("信号机编号不处理-{}", mqMessage.getSignalControllerID());
            return;
        }

        MqMsgBaseHandler mqMsgBaseHandler = mqMsgBaseHandlerMap.get(mqMessage.getObjectId());
        if (mqMsgBaseHandler == null) {
            log.error("还不支持数据请求-{}", mqMessage.getObjectId());
            sendErrorResponse(mqMessage, "9000", "还不支持数据请求");
            return;
        }
        //查看信号机是否正常
        if (mqMessage.getSignalControllerID() == null) {
            log.error("异常的信号机编号-{}", mqMessage);
            return;
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(mqMessage.getSignalControllerID());
        if(signalInfoOp.isEmpty()){
            log.error("没有找到信号机-{}", mqMessage);
            sendErrorResponse(mqMessage, "9001", "没有找到这个信号机");
            return;
        }

        //获取请求类型
        Optional<MqMsgType> mqMsgType = MqMsgType.getType(mqMessage.getType());
        if(mqMsgType.isEmpty()){
            log.error("未知的mq消息类型，无法处理-{}", mqMessage);
            sendErrorResponse(mqMessage, "9002", "消息类型异常");
            return;
        }
        //获取操作类型
        Optional<MqMsgOperator> mqMsgOperator = MqMsgOperator.getType(mqMessage.getOperator());
        if(mqMsgOperator.isEmpty()){
            log.error("未知的mq操作类型，无法处理-{}", mqMessage);
            sendErrorResponse(mqMessage, "9003", "消息操作类型异常");
            return;
        }

        //请求-查询
        if(mqMsgType.get().value() == MqMsgType.MqMsgType_Request.value()
               && mqMsgOperator.get().value() == MqMsgOperator.MqMsgOperator_Query.value()){
            List<Integer> integers =  new ArrayList<>();
            try {
                List<Object> objectList = mqMessage.getObjectList();
                objectList.forEach(
                        objectId -> {
                            integers.add((Integer) objectId);
                        }
                );
            }catch (Exception e){
                log.error("解析参数出现异常", e);
            }
            Optional<List<AreaMessage>> requestMsgsOp = mqMsgBaseHandler.getRequestMsg(mqMessage.getSignalControllerID(), integers);
            if(requestMsgsOp.isPresent()) {

                List<AreaMessage> requestMsgs = requestMsgsOp.get();
                List<AreaMessage> messageToSend = requestMsgs;

                //判定是否需要按需发送数据项
                if (mqMsgBaseHandler.requestMsgNeedSerial(mqMessage.getSignalControllerID())) {
                    //发送首数据项
                    JsonResult<?> jsonResult = openLesSender.sendMsgAsync(requestMsgsOp.get(), 0);
                    if (jsonResult.isSuccess()) {
                        List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                        MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                                .mqMessage(mqMessage)
                                .invokeFutures(invokeFutures)
                                .mqMsgBaseHandler(mqMsgBaseHandler)
                                .localDateTime(LocalDateTime.now())
                                .processed(new AtomicBoolean(false))
                                .requestMsgs(messageToSend)
                                .currentSendIndex(new AtomicInteger(0)).build();
                        mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);

                        //存储数据项
                        {
                            invokeFutures.stream().forEach(
                                    invokeFuture -> {
                                        mqInvokeFutureId2Msg.put(invokeFuture.invokeId(), mqMessage.getMapKey());
                                    }
                            );
                        }

                    } else {
                        log.error("query发送数据异常-{}", mqMessage);
                        sendErrorResponse(mqMessage, "9004", "查询发送数据异常");
                    }
                } else {
                    JsonResult<?> jsonResult = openLesSender.sendMsgAsync(requestMsgsOp.get());
                    if (jsonResult.isSuccess()) {
                        List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                        MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                                .mqMessage(mqMessage)
                                .invokeFutures(invokeFutures)
                                .mqMsgBaseHandler(mqMsgBaseHandler)
                                .localDateTime(LocalDateTime.now())
                                .processed(new AtomicBoolean(false))
                                .requestMsgs(messageToSend)
                                .currentSendIndex(new AtomicInteger(0)).build();
                        mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);

                        //存储数据项
                        {
                            invokeFutures.stream().forEach(
                                    invokeFuture -> {
                                        mqInvokeFutureId2Msg.put(invokeFuture.invokeId(), mqMessage.getMapKey());
                                    }
                            );
                        }
                    } else {
                        log.error("query发送数据异常-{}", mqMessage);
                        sendErrorResponse(mqMessage, "9004", "查询发送数据异常");
                    }
                }
            }else{
                log.error("query数据处理异常-{}", mqMessage);
                sendErrorResponse(mqMessage, "9004", "发送数据异常");
            }
        }
        //请求-设置
        else if (mqMsgType.get().value() == MqMsgType.MqMsgType_Request.value()
                && mqMsgOperator.get().value() == MqMsgOperator.MqMsgOperator_Set.value()) {

            //查看是否需要立即应答
            Optional<MqMessage> mqAckMessageOptional = mqMsgBaseHandler.ackMqMessage(mqMessage, 1);
            if (mqAckMessageOptional.isPresent()) {
                //messageSender.send(SystemControlAck.class.getSimpleName().toLowerCase(), mqAckMessageOptional.get());
            }

            StringBuilder errorMsg = new StringBuilder();
            Optional<List<AreaMessage>> requestMsgsOp = mqMsgBaseHandler.getConfigRequestMsg(mqMessage.getSignalControllerID(),
                    mqMessage.getObjectList(), errorMsg);
            if (requestMsgsOp.isPresent()) {

                List<AreaMessage> requestMsgs = requestMsgsOp.get();
                List<AreaMessage> messagesToSend = requestMsgs;

                //判断config数据项是否需要同步发送
                if (mqMsgBaseHandler.configRequestMsgNeedSerial(mqMessage.getSignalControllerID())) {
                    JsonResult<?> jsonResult = openLesSender.sendMsgAsync(requestMsgsOp.get(), 0);
                    if (jsonResult.isSuccess()) {
                        List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                        MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                                .mqMessage(mqMessage)
                                .invokeFutures(invokeFutures)
                                .mqMsgBaseHandler(mqMsgBaseHandler)
                                .localDateTime(LocalDateTime.now())
                                .processed(new AtomicBoolean(false))
                                .requestMsgs(requestMsgs)
                                .currentSendIndex(new AtomicInteger(0)).build();
                        mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);

                        //存储数据项
                        {
                            invokeFutures.stream().forEach(
                                    invokeFuture -> {
                                        mqInvokeFutureId2Msg.put(invokeFuture.invokeId(), mqMessage.getMapKey());
                                    }
                            );
                        }
                    } else {
                        log.error("set发送数据异常-{}", mqMessage);
                    }
                } else {
                    JsonResult<?> jsonResult = openLesSender.sendMsgAsync(requestMsgsOp.get());
                    if (jsonResult.isSuccess()) {
                        List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                        MqProcessingMsg mqProcessingMsg = MqProcessingMsg.builder()
                                .mqMessage(mqMessage)
                                .invokeFutures(invokeFutures)
                                .mqMsgBaseHandler(mqMsgBaseHandler)
                                .localDateTime(LocalDateTime.now())
                                .processed(new AtomicBoolean(false))
                                .requestMsgs(requestMsgs)
                                .currentSendIndex(new AtomicInteger(0)).build();
                        mqProcessingMsgMap.put(mqMessage.getMapKey(), mqProcessingMsg);

                        //存储数据项
                        {
                            invokeFutures.stream().forEach(
                                    invokeFuture -> {
                                        mqInvokeFutureId2Msg.put(invokeFuture.invokeId(), mqMessage.getMapKey());
                                    }
                            );
                        }
                    } else {
                        log.error("set发送数据异常-{}", mqMessage);
                    }
                }
            } else {
                log.error("set数据处理异常-{}", mqMessage);
                sendErrorResponse(mqMessage, "9004", errorMsg.isEmpty() ? "发送数据异常" : errorMsg.toString());
            }
        }
    }

    /**
     * 反射设置路口号以及区域号以及信号机编号数据项
     *
     * @param datas
     * @param clazz
     * @param signalId
     * @return
     */
    public Optional<List<Object>> getConfigRequestObject(List<Object> datas, Class clazz, String signalId) {
        if (datas == null || datas.isEmpty()) {
            return Optional.empty();
        }
        List<Object> objects = new ArrayList<>();
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(signalId);
        try {
            datas.forEach(
                    data -> {
                        JSONObject jsonObject = (JSONObject) data;
                        Object object = jsonObject.toJavaObject(clazz);
                        objects.add(object);

                        if (signalInfoOp.isPresent() && (object instanceof SgpTransAble)) {
                            //通过反射，设置信号机ID
                            try {
                                Field signalControllerIdField =  object.getClass().getDeclaredField("signalControllerID");
                                signalControllerIdField.setAccessible(true);
                                signalControllerIdField.set(object, signalId);
                            } catch (NoSuchFieldException | IllegalAccessException e) {
                                log.error("exception", e);
                            }
                        }
                    });
        } catch (Exception e) {
            log.error("exception", e);
        }
        return Optional.of(objects);
    }


    /**
     * 通知处理完毕
     *
     * @param invokeFuture
     */
    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void messageInvokeResult(InvokeFuture invokeFuture) {

        Long mqKey = mqInvokeFutureId2Msg.get(invokeFuture.invokeId());
        if (mqKey == null) {
            return;
        }

        MqProcessingMsg mqProcessingMsgCurrent = mqProcessingMsgMap.get(mqKey);
        Optional<MqProcessingMsg> mqProcessingMsgOp = Optional.ofNullable(mqProcessingMsgCurrent);

        if (mqProcessingMsgOp.isEmpty()) {
            return;
        }

        //控制多个线程对数据同时处理
        boolean compareAndSet = mqProcessingMsgOp.get().getProcessed().compareAndSet(false, true);
        if (compareAndSet) {

            if (!mqProcessingMsgOp.get().isReady()) {
                //设置为false，未处理数据项
                mqProcessingMsgOp.get().getProcessed().compareAndSet(true, false);
                return;
            }

            log.debug("准备解析数据项，内存mq数据共[{}]-{}", mqProcessingMsgMap.size(),
                    mqProcessingMsgOp.get().getMqMessage());
            log.debug("准备解析数据项，内存invoke数据共[{}]", mqInvokeFutureId2Msg.size());

            MqProcessingMsg mqProcessingMsg = mqProcessingMsgOp.get();

            Optional<MqMessage> responseMsg = Optional.empty();
            StringBuilder errorMsg = new StringBuilder();
            //请求-查询
            if (mqProcessingMsg.getMqMessage().getType() == MqMsgType.MqMsgType_Request.value()
                    && mqProcessingMsg.getMqMessage().getOperator() == MqMsgOperator.MqMsgOperator_Query.value()) {

                //查询请求是否是序列化的请求
                if (mqProcessingMsg.getMqMsgBaseHandler().requestMsgNeedSerial(mqProcessingMsgOp.get().getMqMessage().getSignalControllerID())) {
                    if (mqProcessingMsg.getInvokeFutures().size() != mqProcessingMsg.getAllMsgNumber()) {
                        //查询数据项尚未发送完毕
                        boolean failed = true;

                        try {
                            failed = mqProcessingMsg.getInvokeFutures().stream().anyMatch(
                                    invokeFuture1 -> invokeFuture1.getResponse().getResponseStatus().value()
                                            != ResponseStatus.SUCCESS.value()
                            );
                        } catch (Exception e) {
                            log.error("异常", e);
                        }
                        if (!failed) {
                            //继续发送数据项
                            JsonResult<?> jsonResult = openLesSender.sendMsgAsync(mqProcessingMsg.getRequestMsgs(),
                                    mqProcessingMsg.getCurrentSendIndex().incrementAndGet());
                            if (jsonResult.isSuccess()) {
                                List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                                mqProcessingMsg.getInvokeFutures().addAll(invokeFutures);

                                //存储数据项
                                {
                                    invokeFutures.stream().forEach(
                                            invokeFuture1 -> {
                                                mqInvokeFutureId2Msg.put(invokeFuture1.invokeId(), mqProcessingMsgOp.get().getMqMessage().getMapKey());
                                            }
                                    );
                                }
                            } else {
                                log.error("1.query发送数据异常-{}-index-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()),
                                        mqProcessingMsg.getInvokeFutures().size());
                            }
                            //设置为false，未处理数据项
                            mqProcessingMsgOp.get().getProcessed().compareAndSet(true, false);
                            return;
                        } else {
                            log.error("1.query发送应答数据异常-{}-index-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()),
                                    mqProcessingMsg.getInvokeFutures().size());
                        }
                    } else {
                        responseMsg = mqProcessingMsg.getMqMsgBaseHandler().getResponseMsg(mqProcessingMsg.getMqMessage(),
                                mqProcessingMsg.getInvokeFutures(), errorMsg);
                        //查询成功进行数据存储
                        if (responseMsg.isPresent() && responseMsg.get().getErrorCode().compareToIgnoreCase("0") == 0) {
                            messagePublisher.publishMessage(DataSaveBean.builder()
                                    .objectList(responseMsg.get().getObjectList()).build());
                        } else {
                            log.error("1.查询请求异常-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()));
                        }
                    }

                } else {
                    responseMsg = mqProcessingMsg.getMqMsgBaseHandler().getResponseMsg(mqProcessingMsg.getMqMessage(),
                            mqProcessingMsg.getInvokeFutures(),errorMsg);
                    //查询成功进行数据存储
                    if (responseMsg.isPresent() && responseMsg.get().getErrorCode().compareToIgnoreCase("0") == 0) {
                        messagePublisher.publishMessage(DataSaveBean.builder()
                                .objectList(responseMsg.get().getObjectList()).build());
                    } else {
                        log.error("2.查询请求异常-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()));
                    }
                }
            }
            //请求-设置
            else if (mqProcessingMsg.getMqMessage().getType() == MqMsgType.MqMsgType_Request.value()
                    && mqProcessingMsg.getMqMessage().getOperator()  == MqMsgOperator.MqMsgOperator_Set.value()) {

                //判断配置数据是否需要序列化发送
                if (mqProcessingMsg.getMqMsgBaseHandler().configRequestMsgNeedSerial(mqProcessingMsg.getMqMessage().getSignalControllerID())) {
                    if (mqProcessingMsg.getInvokeFutures().size() != mqProcessingMsg.getAllMsgNumber()) {
                        //设置数据项尚未发送完毕
                        boolean failed = true;

                        try {
                            failed = mqProcessingMsg.getInvokeFutures().stream().anyMatch(
                                    invokeFuture1 -> invokeFuture1.getResponse().getResponseStatus().value()
                                            != ResponseStatus.SUCCESS.value()
                            );
                        } catch (Exception e) {
                            log.error("异常", e);
                        }
                        if (!failed) {
                            //继续发送数据项
                            JsonResult<?> jsonResult = openLesSender.sendMsgAsync(mqProcessingMsg.getRequestMsgs(),
                                    mqProcessingMsg.getCurrentSendIndex().incrementAndGet());
                            if (jsonResult.isSuccess()) {
                                List<InvokeFuture> invokeFutures = (List<InvokeFuture>) (jsonResult.getData());
                                mqProcessingMsg.getInvokeFutures().addAll(invokeFutures);

                                //存储数据项
                                {
                                    invokeFutures.stream().forEach(
                                            invokeFuture1 -> {
                                                mqInvokeFutureId2Msg.put(invokeFuture1.invokeId(), mqProcessingMsgOp.get().getMqMessage().getMapKey());
                                            }
                                    );
                                }
                            } else {
                                log.error("1.set发送数据异常-{}-index-{}", mqProcessingMsg.getRequestMsgs(), mqProcessingMsg.getInvokeFutures().size());
                            }
                            //设置为false，未处理数据项
                            mqProcessingMsgOp.get().getProcessed().compareAndSet(true, false);
                            return;
                        } else {
                            log.error("1.set发送应答数据异常-{}-index-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()), mqProcessingMsg.getInvokeFutures().size());
                        }
                    } else {
                        responseMsg = mqProcessingMsg.getMqMsgBaseHandler().getConfigResponseMsg(mqProcessingMsg.getMqMessage(),
                                mqProcessingMsg.getInvokeFutures(), errorMsg);
                        //设置成功进行数据存储
                        if (responseMsg.isPresent() && responseMsg.get().getErrorCode().compareToIgnoreCase("0") == 0) {
                            Optional<List<Object>> requestObjects = getConfigRequestObject(responseMsg.get().getObjectList(),
                                    mqProcessingMsg.getMqMsgBaseHandler().dataType(),
                                    mqProcessingMsg.getMqMessage().getSignalControllerID());
                            if (requestObjects.isPresent()) {
                                messagePublisher.publishMessage(DataSaveBean.builder()
                                        .objectList(requestObjects.get()).build());
                            } else {
                                log.error("1.1设置请求异常-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()));
                            }
                        } else {
                            log.error("1.2设置请求异常-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()));
                        }
                    }
                } else {
                    responseMsg = mqProcessingMsg.getMqMsgBaseHandler().getConfigResponseMsg(mqProcessingMsg.getMqMessage(),
                            mqProcessingMsg.getInvokeFutures(), errorMsg);
                    //设置成功进行数据存储
                    if (responseMsg.isPresent() && responseMsg.get().getErrorCode().compareToIgnoreCase("0") == 0) {
                        Optional<List<Object>> requestObjects = getConfigRequestObject(mqProcessingMsg.getMqMessage().getObjectList(),
                                mqProcessingMsg.getMqMsgBaseHandler().dataType(),
                                mqProcessingMsg.getMqMessage().getSignalControllerID());
                        if (requestObjects.isPresent()) {
                            messagePublisher.publishMessage(DataSaveBean.builder()
                                    .objectList(requestObjects.get()).build());
                        } else {
                            log.error("2.1设置请求异常-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()));
                        }
                    } else {
                        log.error("2.2设置请求异常-{}", JSONObject.toJSONString(mqProcessingMsg.getRequestMsgs()));
                    }
                }
            }

            //针对非模拟数据项进行中心机应答
            Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(mqProcessingMsg.getMqMessage().getSignalControllerID());
            int noArea = 0;
            int noJunc = 0;
            if(signalInfoOp.isPresent()){
                noArea = signalInfoOp.get().getNoArea();
                noJunc = signalInfoOp.get().getNoJunc();
            }

             {
                if (responseMsg.isPresent()) {
                    //准备发送应答报文
                    log.debug("准备发送-{}", responseMsg.get());
                    messageSender.sendNats(responseMsg.get().getSignalControllerID(),
                            mqProcessingMsg.getMqMsgBaseHandler().getRoutingKey(), noArea, noJunc, responseMsg.get());
                    SignalBasedLogger.logMessage(responseMsg.get().getSignalControllerID(), "发送应答-{}", JSONObject.toJSONString(responseMsg.get()));

                    //用于同步mq消息应答
                    {
                        messagePublisher.publishMessage(responseMsg.get());
                    }

                    //临时模拟发送交易成功
                    //tabTransactionHandler.sendTabTransactionSimu(responseMsg.get().getSignalControllerID());

                } else {
                    log.debug("准备发送异常应答-{}", mqProcessingMsg.getMqMessage());
                    String err = errorMsg.toString();
                    sendErrorResponse(mqProcessingMsg.getMqMessage(), "9004", err.isEmpty() ? "消息处理异常" : err);
                }
            }

            mqProcessingMsgMap.remove(mqProcessingMsg.getMqMessage().getMapKey());

            //移除数据项
            {
                List<InvokeFuture> invokeFutures = mqProcessingMsg.getInvokeFutures();
                if ((invokeFutures != null) && !invokeFutures.isEmpty()) {
                    invokeFutures.forEach(
                            invokeFuture1 -> {
                                mqInvokeFutureId2Msg.remove(invokeFuture1.invokeId());
                            }
                    );
                }

                log.debug("解析数据项完毕，内存invoke数据共[{}]", mqInvokeFutureId2Msg.size());
            }
        }
    }

    /**
     * 发送异常应答报文
     * @param request
     * @param errCode
     * @param msg
     */
    public void sendErrorResponse(MqMessage request, String errCode, String msg){
        MqMessage mqMessage = buildErrorMqResponseMsg(request, errCode, msg);

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(request.getSignalControllerID());

        messageSender.sendNats(request.getSignalControllerID(),  "error",  signalInfoOp.isPresent() ?
                        signalInfoOp.get().getNoArea() : 0,
                signalInfoOp.isPresent() ? signalInfoOp.get().getNoJunc() :  0, mqMessage);


        //用于同步mq消息应答
        {
            messagePublisher.publishMessage(mqMessage);
        }

        SignalBasedLogger.logMessage(request.getSignalControllerID(), "发送异常应答-{}", JSONObject.toJSONString(mqMessage));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ac = applicationContext;
    }
}

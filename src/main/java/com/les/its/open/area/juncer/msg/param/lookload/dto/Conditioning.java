package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Conditioning implements DataIndexAble {
    // 条件计算编号
    @NotNull(message = "{juncer.param.conditioning.conditioningNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.conditioning.conditioningNo.range}")
    private Integer conditioningNo;
    
    // 结果类型
    @NotNull(message = "{juncer.param.conditioning.outputType.notNull}")
    private Integer outputType;
    
    // 结果编号
    @NotNull(message = "{juncer.param.conditioning.outputNo.notNull}")
    private Integer outputNo;
    
    // 操作符1 (0:无操作, 1:NOT)
    @NotNull(message = "{juncer.param.conditioning.op1.notNull}")
    private Integer op1;
    
    // 变量类型1
    @NotNull(message = "{juncer.param.conditioning.paramType1.notNull}")
    private Integer paramType1;
    
    // 变量编号1
    @NotNull(message = "{juncer.param.conditioning.paramNo1.notNull}")
    private Integer paramNo1;
    
    // 操作符2 (可以是 AND, OR, AND NOT, OR NOT)
    @NotNull(message = "{juncer.param.conditioning.op2.notNull}")
    private Integer op2;
    
    // 变量类型2
    @NotNull(message = "{juncer.param.conditioning.paramType2.notNull}")
    private Integer paramType2;
    
    // 变量编号2
    @NotNull(message = "{juncer.param.conditioning.paramNo2.notNull}")
    private Integer paramNo2;
    
    // 操作符3
    @NotNull(message = "{juncer.param.conditioning.op3.notNull}")
    private Integer op3;
    
    // 变量类型3
    @NotNull(message = "{juncer.param.conditioning.paramType3.notNull}")
    private Integer paramType3;
    
    // 变量编号3
    @NotNull(message = "{juncer.param.conditioning.paramNo3.notNull}")
    private Integer paramNo3;
    
    // 操作符4
    @NotNull(message = "{juncer.param.conditioning.op4.notNull}")
    private Integer op4;
    
    // 变量类型4
    @NotNull(message = "{juncer.param.conditioning.paramType4.notNull}")
    private Integer paramType4;
    
    // 变量编号4
    @NotNull(message = "{juncer.param.conditioning.paramNo4.notNull}")
    private Integer paramNo4;

    // 条件计算名称
    @NotNull(message = "{juncer.param.conditioning.conditioningName.notNull}")
    private String conditioningName;

    @Override
    public int getDataNo() {
        return conditioningNo;
    }
}
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanStageParamExt {
    // 晚启动时间
    @NotNull(message = "晚启动时间不能为空")
    @Range(min = 0, max = 60, message = "晚启动时间有效范围为[0,60]")
    private Integer laggingTime;
    
    // 早结束时间
    @NotNull(message = "早结束时间不能为空")
    @Range(min = 0, max = 60, message = "早结束时间有效范围为[0,60]")
    private Integer delayCutOffTime;
} 
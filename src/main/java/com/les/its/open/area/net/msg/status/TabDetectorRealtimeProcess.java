package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabDetectorRealtime;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabDetectorRealtimeProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_DETECTOR_REALTIME;
    }

    @Override
    public int oneItemSize() {
        return 64;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize() ) {
            TabDetectorRealtime tabDetectorFault = new TabDetectorRealtime();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            List<Integer> detectorRealtimeData = new ArrayList<>();
            tabDetectorFault.setDetectorRealtimeData(detectorRealtimeData);
            for (int i = 0; i < 64; i++) {
                tabDetectorFault.getDetectorRealtimeData().add(buf.readUnsignedByte() & 0xff);
            }
            return Optional.of(tabDetectorFault);
        }

        return Optional.empty();
    }
}

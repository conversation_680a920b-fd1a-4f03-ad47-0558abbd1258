package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraint;
import com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraintInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class StageTransitionConstraintInfoProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_STAGE_TRANS_CONSTRAINT;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            StageTransitionConstraintInfo stageTransitionConstraintInfo = new StageTransitionConstraintInfo();
            objects.add(stageTransitionConstraintInfo);
            //过渡约束表号
            stageTransitionConstraintInfo.setStageTransitionConstraintNo(buf.readUnsignedByte() & 0xff);
            //过渡约束配置
            List<StageTransitionConstraint> stageTransitionConstraints = new ArrayList<>();
            stageTransitionConstraintInfo.setStageTransitionConstraints(stageTransitionConstraints);
            for (int m = 0; m < 64; m++) {
                StageTransitionConstraint stageTransitionConstraint = new StageTransitionConstraint();
                stageTransitionConstraints.add(stageTransitionConstraint);

                //相位阶段编号
                stageTransitionConstraint.setStageNo(buf.readUnsignedByte() & 0xff);
                //相位阶段过渡约束值
                List<Integer> transitionConstraints = new ArrayList<>();
                stageTransitionConstraint.setTransitionConstraints(transitionConstraints);
                for (int n = 0; n < 64; n++) {
                    transitionConstraints.add(buf.readUnsignedByte() & 0xff);
                }
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof StageTransitionConstraintInfo)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            StageTransitionConstraintInfo stageTransitionConstraintInfo
                    = (StageTransitionConstraintInfo)objects.get(i);

            //过渡约束表号
            buf.writeByte(stageTransitionConstraintInfo.getStageTransitionConstraintNo());

            List<StageTransitionConstraint> stageTransitionConstraints = stageTransitionConstraintInfo.getStageTransitionConstraints();
            for (int k = 0; k < 64; k++) {
                StageTransitionConstraint stageTransitionConstraint = stageTransitionConstraints.get(k);
                //相位阶段编号
                buf.writeByte(stageTransitionConstraint.getStageNo());
                //相位阶段过渡约束值
                List<Integer> transitionConstraints = stageTransitionConstraint.getTransitionConstraints();
                for (int m = 0; m < 64; m++) {
                    buf.writeByte(transitionConstraints.get(m));
                }
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 1 + 64*(1+64);
    }

    @Override
    public Class dataClazz() {
        return StageTransitionConstraintInfo.class;
    }
}

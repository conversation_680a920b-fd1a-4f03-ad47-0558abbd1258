package com.les.its.open.area.juncer.msg.param.lookload.dto;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StagePhase {

    
    // 相位需求 (0:不关联,1:固定出现 2:按需出现
    @NotNull(message = "相位需求不能为空")
    @Range(min = 0, max = 2, message = "相位需求有效范围为[0,2]")
    private Integer demand;

    // 晚启动时间
    @NotNull(message = "晚启动时间不能为空")
    @Range(min = 0, max = 255, message = "晚启动时间有效范围为[0,255]")
    private Integer laggingTime;

    // 早结束时间
    @NotNull(message = "晚结束时间不能为空")
    @Range(min = 0, max = 255, message = "晚结束时间有效范围为[0,255]")
    private Integer delayCutOffTime;
} 
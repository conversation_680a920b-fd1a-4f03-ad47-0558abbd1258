package com.les.its.open.area.net.msg.param;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.param.TabLookParam;
import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import com.les.its.open.area.net.utils.JuncerUtils;
import com.les.its.open.bussiness.bean.P1049Infos;
import com.les.its.open.bussiness.process.P1049InfosProcess;
import com.les.its.open.event.MessagePublisher;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class TabLookParamProcess extends TabOuterBaseMsgProcess implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private Map<ParamMsgType, LookLoadBase> paramLookLoadBase = new ConcurrentHashMap<>();

    @Autowired
    private DataValidatorFactory dataValidatorFactory;

    @Autowired
    private MessagePublisher messagePublisher;

    @PostConstruct
    public void init() {
        Arrays.stream(ParamMsgType.values()).forEach(
                paramMsgType -> {
                    Map<String, LookLoadBase> beansOfType = applicationContext.getBeansOfType(LookLoadBase.class);
                    beansOfType.keySet().stream().forEach(key ->
                    {
                        paramLookLoadBase.put(beansOfType.get(key).msgType(), beansOfType.get(key));
                    });
                }
        );
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        TabLookParam tabLookParam = new TabLookParam();

        ParamMsgType paramMsgType = ParamMsgType.fromCode((juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_3() << 8)
                + (juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_4()));
        if(ParamMsgType.PARAM_UNKNOWN == paramMsgType) {
            return Optional.empty();
        }

        LookLoadBase lookLoadBase = paramLookLoadBase.get(paramMsgType);
        if(lookLoadBase == null){
            return Optional.empty();
        }

        Optional<List<Object>> objectOp = lookLoadBase.procLook(juncerMsg);
        if(objectOp.isEmpty()){
            return Optional.empty();
        }

        //数据参数校验
        objectOp.get().forEach(
                object -> {
                    StringBuilder stringBuilder = new StringBuilder();
                    boolean validateData = dataValidatorFactory.validateData(object, stringBuilder);
                    if(!validateData){
                        log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", controllerBaseInfo.getSignalId(),
                                stringBuilder.toString(), object);
                    }
                }
        );

        int offset = (juncerMsg.getOpenLesMessage().getLesBody().getBody()[0] & 0xff);
        int count = (juncerMsg.getOpenLesMessage().getLesBody().getBody()[1] & 0xff);

        tabLookParam.setParamMsgType(paramMsgType);
        tabLookParam.setOffset(offset);
        tabLookParam.setCount(count);
        tabLookParam.setAClass(lookLoadBase.dataClazz());
        List<Object> objects = objectOp.get();
        tabLookParam.setParams(objects);

        //发布数据存储数据项
        {
            P1049Infos p1049Infos = P1049InfosProcess.build(objects, controllerBaseInfo);
            messagePublisher.publishMessage(p1049Infos);
        }

        return Optional.of(tabLookParam);
    }

    @Override
    public MsgType msgType() {
        return MsgType.TAB_LOOK_PARAM;
    }

    @Override
    public int oneItemSize() {
        return JuncerUtils.SIZE_CHANGE_ABLE;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 事务交易控制
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabTransactionCmd extends TabInBase implements NeedAck {
    
    /**
     * 交易事务创建
     * 1:normal,2:transaction;3:verifying;6:done
     */
    @NotNull(message = "交易事务创建不能为空")
    @DigitsInSet(acceptedValues = {1, 2, 3, 6}, message = "交易事务创建必须是1、2、3或6")
    private Integer transactionCreate;
    
    /**
     * 交易超时时间
     */
    @NotNull(message = "交易超时时间不能为空")
    @Max(value = 255, message = "交易超时时间不能超过255")
    private Integer transactionTimeout;

    /**
     * 交易备注
     */
    @NotNull(message = "交易备注不能为空")
    private String transactionNote;


    /**
     * 交易验证状态
     */
    private int transactionStatus;

    /**
     * 交易验证错误码
     */
    private int transactionErrorCode;

    @Override
    public String getAckKey() {

        String orgAck = getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_TRANSACTION_CMD.getCode())
                 + "###" + transactionCreate;

        return orgAck;
    }
}
package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 信号机ipv4信息
 */
@Data
public class Ipv4 {
    /**
     * 地址
     * 格式 ************08
     */
    @NotNull(message = "{basic.param.ipv4.ip.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "{basic.param.ipv4.ip.invalid}"
    )
    private String ip;

    /**
     * 子网掩码
     * 格式 *************
     */
    @NotNull(message = "{basic.param.ipv4.mask.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "{basic.param.ipv4.mask.invalid}"
    )
    private String mask;

    /**
     * 网关
     * 格式 ************
     */
    @NotNull(message = "{basic.param.ipv4.gateway.notNull}")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "{basic.param.ipv4.gateway.invalid}"
    )
    private String gateway;




} 
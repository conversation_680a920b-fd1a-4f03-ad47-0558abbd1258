package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.ActuatedPhaseParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class ActuatedPhaseParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public ActuatedPhaseParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }


    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_ActuatedPhaseParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return ActuatedPhaseParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看相位需求与扩展
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_ACTUATED_PHASE, 1,
                    ParamMsgType.PARAM_ACTUATED_PHASE.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        List<Integer> dataNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dataNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        if(dataNos.isEmpty()){
            IntStream.rangeClosed(1, ParamMsgType.PARAM_ACTUATED_PHASE.getMax()).forEach(dataNos::add);
        }

        //设备信息
        Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.ActuatedPhase>> actuatedPhasesOp
                = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_ACTUATED_PHASE);
        if(actuatedPhasesOp.isEmpty()){
            log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_ACTUATED_PHASE);
            return Optional.empty();
        }
        log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), actuatedPhasesOp.get());


        //构建返回数据项
        dataNos.forEach(dataNo ->{
            //根据编号查找数据项
            actuatedPhasesOp.get().stream().filter(
                    actuatedPhase -> actuatedPhase.getPhaseNo().intValue() == dataNo
            ).findAny().ifPresent(actuatedPhase -> {

                //转换MQ数据项
                ActuatedPhaseParam actuatedPhaseParam = new ActuatedPhaseParam();
                //路口编号
                actuatedPhaseParam.setSignalControllerID(requestMessage.getSignalControllerID());
                //相位号
                actuatedPhaseParam.setPhaseNo(actuatedPhase.getPhaseNo());
                // 相位通过时间
                actuatedPhaseParam.setPhasePassage(actuatedPhase.getPhasePassage());
                // 相位可变初始绿
                actuatedPhaseParam.setPhaseAddedInitial(actuatedPhase.getPhaseAddedInitial());
                // 相位最大初始绿
                actuatedPhaseParam.setPhaseMaximumInitial(actuatedPhase.getPhaseMaximumInitial());
                // 相位通过时间调整前时间
                actuatedPhaseParam.setPhaseTimeBeforeReduction(actuatedPhase.getPhaseTimeBeforeReduction());
                // 相位通过时间调整时相位通过时间
                actuatedPhaseParam.setPhaseTimeToReduce(actuatedPhase.getPhaseTimeToReduce());
                // 相位车辆间隔
                actuatedPhaseParam.setPhaseReduceBy(actuatedPhase.getPhaseReduceBy());
                // 相位车辆最小间隔
                actuatedPhaseParam.setPhaseMinimumGap(actuatedPhase.getPhaseMinimumGap());
                // 相位动态最大值
                actuatedPhaseParam.setPhaseDynamicMaxLimit(actuatedPhase.getPhaseDynamicMaxLimit());
                // 相位动态最大值步长
                actuatedPhaseParam.setPhaseDynamicMaxStep(actuatedPhase.getPhaseDynamicMaxStep());

                //应答参数
                datas.add(actuatedPhaseParam);
            });
        });

        if(datas.isEmpty()){
            errorMsgRet.append("数据返回异常");
            return Optional.empty();
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //转换格式

        List<com.les.its.open.area.juncer.msg.param.lookload.dto.ActuatedPhase> actuatedPhaseList
                = new ArrayList<>();

        for (int i = 0; i < datas.size(); i++) {
            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            ActuatedPhaseParam actuatedPhaseParam = jsonObject.toJavaObject(ActuatedPhaseParam.class);
            log.error("设置参数-{}", actuatedPhaseParam);

            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(actuatedPhaseParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, actuatedPhaseParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }

            {
                com.les.its.open.area.juncer.msg.param.lookload.dto.ActuatedPhase actuatedPhase = new
                        com.les.its.open.area.juncer.msg.param.lookload.dto.ActuatedPhase();
                //相位号
                actuatedPhase.setPhaseNo(actuatedPhaseParam.getPhaseNo());
                // 相位行人步行参数
                actuatedPhase.setPhaseWalk(0);
                // 相位行人清除参数
                actuatedPhase.setPhasePedestrianClear(0);
                // 相位通过时间参数
                actuatedPhase.setPhasePassage(actuatedPhaseParam.getPhasePassage());
                // 相位可变初始绿
                actuatedPhase.setPhaseAddedInitial(actuatedPhaseParam.getPhaseAddedInitial());
                // 相位最大初始绿
                actuatedPhase.setPhaseMaximumInitial(actuatedPhaseParam.getPhaseMaximumInitial());
                // 相位通过时间调整前时间
                actuatedPhase.setPhaseTimeBeforeReduction(actuatedPhaseParam.getPhaseTimeBeforeReduction());
                // 相位通过时间调整时相位通过时间
                actuatedPhase.setPhaseTimeToReduce(actuatedPhaseParam.getPhaseTimeToReduce());
                // 相位车辆间隔
                actuatedPhase.setPhaseReduceBy(actuatedPhaseParam.getPhaseReduceBy());
                // 相位车辆最小间隔
                actuatedPhase.setPhaseMinimumGap(actuatedPhaseParam.getPhaseMinimumGap());
                // 相位动态最大值
                actuatedPhase.setPhaseDynamicMaxLimit(actuatedPhaseParam.getPhaseDynamicMaxLimit());
                // 相位动态最大值步长
                actuatedPhase.setPhaseDynamicMaxStep(actuatedPhaseParam.getPhaseDynamicMaxStep());
                // 行人相位早起的时间参数
                actuatedPhase.setPhasePedAdvanceWalkTime(0);
                // 行人相位延迟时间参数
                actuatedPhase.setPhasePedDelayTime(0);
                // 相位启动参数
                actuatedPhase.setPhaseStartup(0);
                // 相位操作参数
                actuatedPhase.setPhaseOptions(0);

                actuatedPhaseList.add(actuatedPhase);
            }

        }

        //生成加载参数
        AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_ACTUATED_PHASE,
                actuatedPhaseList, signalInfoOp.get());
        areaMessages.add(areaMessage);


        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabAlarm extends TabOutBase {
    /**
     * 发生时间
     */
    private long alarmTime;
    
    /**
     * 类型
     */
    private int alarmType;
    
    /**
     * 故障详情
     */
    private Integer faultDetail;

    /**
     * 故障详情
     */
    private Integer faultDetail2;


    /**
     * 故障详情
     */
    private Integer faultDetail3;


    /**
     * 故障详情
     */
    private Integer faultDetail4;
    

} 
package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.Demand;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Extension;

import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 相位需求扩展参数
 */
@Data
public class PhaseDemandExtendParam implements SgpTransAble {
    public static final String MqObjectId = "10016";

    /**
     * 相位编号
     * 范围: [1,64]
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.phaseNo.notNull}")
    @Min(value = 1, message = "{basic.param.phaseDemandExtend.phaseNo.min}")
    @Max(value = 64, message = "{basic.param.phaseDemandExtend.phaseNo.max}")    
    private Integer phaseNo;

    /**
     * 需求列表
     * 范围: [8,8]
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.demands.notNull}")
    @Size(min = 8, max = 8, message = "{basic.param.phaseDemandExtend.demands.size}")  
    @Valid
    private List<Demand> demands;

    /**
     * 延长列表
     * 范围: [8,8]
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.extensions.notNull}")
    @Size(min = 8, max = 8, message = "{basic.param.phaseDemandExtend.extensions.size}")
    @Valid
    private List<Extension> extensions;

    /**
     * 结束手动运行期时插入相位需求
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.demandsInsertLeavingManualAndFixPhase.range}")
    private Integer demandsInsertLeavingManualAndFixPhase;

    /**
     * 启动时插入相位需求
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.demandsInsertStartUpPhase.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.demandsInsertStartUpPhase.range}")
    private Integer demandsInsertStartUpPhase;

    /**
     * 紧急调用结束后插入需求
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.demandsInsertLeavingHurryCall.range}")
    private Integer demandsInsertLeavingHurryCall;

    /**
     * 中心控制结末后插入需求
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.demandsInsertLeavingSystem.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.demandsInsertLeavingSystem.range}")
    private Integer demandsInsertLeavingSystem;

    /**
     * 无条件默认需求
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.unconditionalDemand.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.unconditionalDemand.range}")
    private Integer unconditionalDemand;

    /**
     * 非锁定需求最大相位最大绿
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.unlatchedDemandStartMaxGreenPhase.range}")
    private Integer unlatchedDemandStartMaxGreenPhase;

    /**
     * 最小绿需求
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.minGreenDemand.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.minGreenDemand.range}")
    private Integer minGreenDemand;

    /**
     * 最大绿需求
     * 0:否, 1:是
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.maxGreenDemand.notNull}")
    @Range(min = 0, max = 1, message = "{basic.param.phaseDemandExtend.maxGreenDemand.range}")
    private Integer maxGreenDemand;

    /**
     * 相位最大绿跟随相位需求
     * 0:无, 1-64相位编号
     */
    @NotNull(message = "{basic.param.phaseDemandExtend.revertivePhaseDemand.notNull}")
    @Range(min = 0, max = 64, message = "{basic.param.phaseDemandExtend.revertivePhaseDemand.range}")
    private Integer revertivePhaseDemand;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return phaseNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabSetStage;
import com.les.its.open.area.juncer.msg.cmd.sub.SetStageExt;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabSetStageProcess extends TabInnerBaseMsgProcess {

    @Override
    public MsgType msgType() {
        return MsgType.TAB_SET_STAGE;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabSetStage.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {

        if(tabInBase instanceof TabSetStage tabSetStage) {
            msgCode.set(msgType().getCode());
            boolean hasCharacter = tabSetStage.getCharacter() != null;
            byte[] body = new byte[5 + (hasCharacter ? 1 : 0) + 64 + 64];
            body[0] = (byte) (tabSetStage.getCrossingSeqNo() & 0xff);
            int offset  = 0;
            if(hasCharacter) {
                offset++;
                body[offset] = (byte) (tabSetStage.getCharacter() & 0xff);
            }
            offset++;
            body[offset] = (byte) (tabSetStage.getStageNo() & 0xff) ;
            offset++;
            body[offset] = (byte) (tabSetStage.getStageSeq() & 0xff) ;
            offset++;
            body[offset] = (byte) (tabSetStage.getDuration() & 0xff) ;
            offset++;
            body[offset] = (byte) ((tabSetStage.getDuration() >> 8) & 0xff) ;

            List<SetStageExt> extParams = tabSetStage.getExtParams();

            {
                AtomicInteger laggingTime = new AtomicInteger();
                for (int i = 0; i < 64; i++) {
                    laggingTime.set(0);
                    int finalI = i;
                    if(extParams != null) {
                        extParams.stream().filter(setStageExt -> setStageExt.getPhaseNo() == (finalI + 1)).findFirst().ifPresent(
                                setStageExt -> laggingTime.set(setStageExt.getLaggingTime())
                        );
                    }
                    body[5 + (hasCharacter ? 1 : 0) + i] = (byte) ((laggingTime.get()) & 0xff) ;
                }
            }

            {
                AtomicInteger delayCutOffTime = new AtomicInteger();
                for (int i = 0; i < 64; i++) {
                    delayCutOffTime.set(0);
                    int finalI = i;
                    if(extParams != null) {
                        extParams.stream().filter(setStageExt -> setStageExt.getPhaseNo() == (finalI + 1)).findFirst().ifPresent(
                                setStageExt -> delayCutOffTime.set(setStageExt.getDelayCutOffTime())
                        );
                    }
                    body[5 + (hasCharacter ? 1 : 0) + 64 + i] = (byte) (delayCutOffTime.get() & 0xff);
                }

            }

            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }

}

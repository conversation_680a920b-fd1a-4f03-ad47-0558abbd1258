package com.les.its.open.area.juncer.msg.param.lookload.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentAction {

    // 功能编号
    @NotNull(message = "{juncer.param.segmentAction.functionNo.notNull}")
    private Integer functionNo;
    
    // 参数1    
    @NotNull(message = "{juncer.param.segmentAction.param1.notNull}")
    private Integer param1;
    
    // 参数2
    @NotNull(message = "{juncer.param.segmentAction.param2.notNull}")
    private Integer param2;
} 
package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.sub.SetStageExt;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabSetStage extends TabInBase implements NeedAck {

    @Range(min = 1, max = 8, message = "子路口号范围[1,8]")
    private int crossingSeqNo;

    @Range(min = 1, max = 64, message = "阶段号范围[1,64]")
    private int stageNo;

    private int stageSeq;

    private int duration;

    @DigitsInSet(acceptedValues = {2,4}, message = "特征值必须是2或4")
    private Integer character;

    @Valid
    @Size(min = 0, max = 64, message = "迟起迟断数组有效范围[0,64]")
    private List<SetStageExt> extParams;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_SET_STAGE.getCode());
    }
}

package com.les.its.open.area.juncer.msg.param;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.NeedAck;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabLoadParam extends TabInBase implements NeedAck {

    private ParamMsgType paramMsgType;

    private List<Object> objects;

    public long getLoadId() {
        return (MsgType.TAB_LOAD_PARAM.getCode() & 0xffff0000) | ( getParamMsgType().getCode());
    }

    public String getParamDes() {
        return getParamMsgType().getDescription();
    }

    @Override
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", getLoadId());
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.ActuatedPhase;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ActuatedPhaseProc implements LookLoadBase {

    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_ACTUATED_PHASE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            ActuatedPhase actuatedPhase = new ActuatedPhase();
            objects.add(actuatedPhase);

            //相位编号
            actuatedPhase.setPhaseNo(buf.readUnsignedByte() & 0xff);
            //相位行人步行参数
            actuatedPhase.setPhaseWalk(buf.readUnsignedByte() & 0xff);
            //相位行人清除参数
            actuatedPhase.setPhasePedestrianClear(buf.readUnsignedByte() & 0xff);
            //相位通过时间参数
            actuatedPhase.setPhasePassage(buf.readUnsignedByte() & 0xff);
            //相位可支初始绿参数
            actuatedPhase.setPhaseAddedInitial(buf.readUnsignedByte() & 0xff);
            //相位最大初始绿参数
            actuatedPhase.setPhaseMaximumInitial(buf.readUnsignedByte() & 0xff);
            //相位通过时间递减前时间参数
            actuatedPhase.setPhaseTimeBeforeReduction(buf.readUnsignedByte() & 0xff);
            //相位通过时间递减的相位时间参数
            actuatedPhase.setPhaseTimeToReduce(buf.readUnsignedByte() & 0xff);
            //相位车辆间隔参数
            actuatedPhase.setPhaseReduceBy(buf.readUnsignedByte() & 0xff);
            //相位车辆最小间隔参数
            actuatedPhase.setPhaseMinimumGap(buf.readUnsignedByte() & 0xff);
            //相位动态最大值
            actuatedPhase.setPhaseDynamicMaxLimit(buf.readUnsignedByte() & 0xff);
            //相位动态最大值步长
            actuatedPhase.setPhaseDynamicMaxStep(buf.readUnsignedByte() & 0xff);
            //行人相位早起的时间参数
            actuatedPhase.setPhasePedAdvanceWalkTime(buf.readUnsignedByte() & 0xff);
            //行人相位延迟时间参数
            actuatedPhase.setPhasePedDelayTime(buf.readUnsignedByte() & 0xff);
            //相位启动参数
            actuatedPhase.setPhaseStartup(buf.readUnsignedByte() & 0xff);
            //相位操作参数
            actuatedPhase.setPhaseOptions(buf.readUnsignedShortLE());

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof ActuatedPhase)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            ActuatedPhase actuatedPhase
                    = (ActuatedPhase)objects.get(i);
            //相位编号
            buf.writeByte(actuatedPhase.getPhaseNo());
            //相位行人步行参数
            buf.writeByte(actuatedPhase.getPhaseWalk());
            //相位行人清除参数
            buf.writeByte(actuatedPhase.getPhasePedestrianClear());
            //相位通过时间参数
            buf.writeByte(actuatedPhase.getPhasePassage());
            //相位可支初始绿参数
            buf.writeByte(actuatedPhase.getPhaseAddedInitial());
            //相位最大初始绿参数
            buf.writeByte(actuatedPhase.getPhaseMaximumInitial());
            //相位通过时间递减前时间参数
            buf.writeByte(actuatedPhase.getPhaseTimeBeforeReduction());
            //相位通过时间递减的相位时间参数
            buf.writeByte(actuatedPhase.getPhaseTimeToReduce());
            //相位车辆间隔参数
            buf.writeByte(actuatedPhase.getPhaseReduceBy());
            //相位车辆最小间隔参数
            buf.writeByte(actuatedPhase.getPhaseMinimumGap());
            //相位动态最大值
            buf.writeByte(actuatedPhase.getPhaseDynamicMaxLimit());
            //相位动态最大值步长
            buf.writeByte(actuatedPhase.getPhaseDynamicMaxStep());
            //行人相位早起的时间参数
            buf.writeByte(actuatedPhase.getPhasePedAdvanceWalkTime());
            //行人相位延迟时间参数
            buf.writeByte(actuatedPhase.getPhasePedDelayTime());
            //相位启动参数
            buf.writeByte(actuatedPhase.getPhaseStartup());
            //相位操作参数
            buf.writeShortLE(actuatedPhase.getPhaseOptions());

        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 17;
    }

    @Override
    public Class dataClazz() {
        return ActuatedPhase.class;
    }
}
package com.les.its.open.area.message.api;

import com.les.its.open.area.message.service.distributelock.DistributedLock;
import com.les.its.open.area.message.service.distributelock.EnhancedDistributedLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/demo")
@Slf4j
public class DistributedLockController {

    @Autowired
    private EnhancedDistributedLockService lockService;

    /**
     * 使用服务方式
     */
    @PostMapping("/process/{id}/{exeTime}")
    public String processWithService(@PathVariable String id, @PathVariable int exeTime) {
        String lockKey = "process:lock:" + id;

        String result  = lockService.executeWithTimeout(lockKey, 5, 10, TimeUnit.SECONDS, () -> {
                // 业务逻辑
                log.info("Processing business logic for id: {}", id);
                Thread.sleep(exeTime); // 模拟业务处理
                return "Process completed for id: " + id;
            });

        return result;
    }

    /**
     * 使用注解方式
     */
    @PostMapping("/order/{orderId}")
    @DistributedLock(key = "order:lock:#{#orderId}", waitTime = 5, maxRunTime = 10)
    public String processOrder(@PathVariable String orderId) throws InterruptedException {
        log.info("Processing order: {}", orderId);
        Thread.sleep(15000); // 模拟订单处理
        return "Order processed: " + orderId;
    }
}

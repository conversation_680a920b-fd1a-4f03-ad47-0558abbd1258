package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 阶段软件需求
 */
@Data
public class TabStageCall extends TabInBase implements NeedAck {
    
    /**
     * 阶段软件需求
     */
    @NotNull(message = "阶段软件需求不能为空")
    @Size(min = 64, max = 64, message = "阶段软件需求固定长度64")
    @DiscreteValuesList(acceptedValues = {0, 1}, message = "阶段软件需求值必须是0或1")
    private List<Integer> systemCalls;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_STAGE_CALL.getCode());
    }
} 
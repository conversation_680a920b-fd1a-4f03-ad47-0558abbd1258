package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 灯电流电压状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabUIStatus extends TabOutBase {
    /**
     * 电流电压状态
     */
    private List<UIStatus> uiStatus;
    
    /**
     * 电流电压内部类
     */
    @Data
    public static class UIStatus {
        /**
         * 灯组编号
         */
        private int lightsGroupNo;
        
        /**
         * 红黄绿
         */
        private List<LightVoltageCurrent> lightVoltageCurrent;

    }
    
    /**
     * 灯电流电压内部类
     */
    @Data
    public static class LightVoltageCurrent {
        /**
         * 灯实时电压
         */
        private int voltage;

        /**
         * 灯实时电流
         */
        private int current;
    }
} 
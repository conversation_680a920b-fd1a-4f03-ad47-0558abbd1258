package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 自定义注解，验证多个整数是否都在指定的列表中或范围内
 */
@Documented
@Constraint(validatedBy = DiscreteValuesListValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DiscreteValuesList {
    String message() default "所有值必须在指定列表中或范围内";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    /**
     * 允许的离散值列表
     */
    int[] acceptedValues() default {};
    
    /**
     * 允许的最小值（包含）
     */
    int min() default Integer.MIN_VALUE;
    
    /**
     * 允许的最大值（包含）
     */
    int max() default Integer.MAX_VALUE;
    
    /**
     * 是否允许重复值
     */
    boolean allowDuplicates() default true;
    
    /**
     * 是否允许空值
     */
    boolean allowNull() default false;
} 
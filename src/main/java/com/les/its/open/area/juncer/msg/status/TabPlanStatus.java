package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 实时控制方式/方案/日计划/调度计划
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabPlanStatus extends TabOutBase {
    /**
     * 子路口号
     */
    private int crossingSeqNo;
    
    /**
     * 控制方式编号
     */
    private int controlModeNo;
    
    /**
     * 特征值
     */
    private int character;
    
    /**
     * 方案编号
     */
    private int planNo;
    
    /**
     * 日计划编号
     */
    private int dayPlanNo;
    
    /**
     * 调度表编号
     */
    private int scheduleNo;
    
    /**
     * 阶段编号
     */
    private int stageNo;
    
    /**
     * 阶段中相应灯组状态
     */
    private List<Integer> lightsGroupStatus;
    
    /**
     * 系统状态
     */
    private int systemStatus;
} 
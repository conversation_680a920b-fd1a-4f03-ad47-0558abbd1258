package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Schedule;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ScheduleProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_SCHEDULE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Schedule schedule = new Schedule();
            objects.add(schedule);

            // 调度表编号
            schedule.setScheduleNo(buf.readUnsignedByte() & 0xff);
            // 子路口号
            schedule.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            // 优先级 (0:基本优先级，优先级相同时调度表号最小最大)
            schedule.setPriority(buf.readUnsignedByte() & 0xff);
            // 星期值
            schedule.setWeek(buf.readUnsignedByte() & 0xff);
            // 月份
            schedule.setMonth(buf.readUnsignedShortLE() & 0xffff);
            // 日期
            schedule.setDay(buf.readUnsignedIntLE() & 0xffffffffL);
            // 日计划号
            schedule.setDayPlanNo(buf.readUnsignedByte() & 0xff);
            //相位名称
            schedule.setName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Schedule)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Schedule schedule
                    = (Schedule)objects.get(i);

            // 调度表编号
            buf.writeByte(schedule.getScheduleNo());
            // 子路口号
            buf.writeByte(schedule.getCrossingSeqNo());
            // 优先级 (0:基本优先级，优先级相同时调度表号最小最大)
            buf.writeByte(schedule.getPriority());
            // 星期值
            buf.writeByte(schedule.getWeek());
            // 月份
            buf.writeShortLE(schedule.getMonth());
            // 日期
            buf.writeIntLE(schedule.getDay().intValue());
            // 日计划号
            buf.writeByte(schedule.getDayPlanNo());

            //名称
            {
                byte[] name = new byte[64];
                byte[] bytes = schedule.getName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 4 + 2 + 4 + 1 + 64;
    }

    @Override
    public Class dataClazz() {
        return Schedule.class;
    }
}

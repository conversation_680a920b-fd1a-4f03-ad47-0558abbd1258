package com.les.its.open.area.juncer.msg.param;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.NeedAck;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabLook extends TabInBase implements NeedAck {

    @JsonIgnore
    @JSONField(serialize=false)
     private ParamMsgType paramMsgType;

     private int offset;
     private int count;

     public long getLookId() {
         return (MsgType.TAB_LOOK.getCode() & 0xffff0000) | ( getParamMsgType().getCode());
     }

     public String getParamDes() {
         return getParamMsgType().getDescription();
     }

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", getLookId());
    }
}

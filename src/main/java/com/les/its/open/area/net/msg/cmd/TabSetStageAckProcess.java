package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabSetStage;
import com.les.its.open.area.juncer.msg.cmd.TabSetStageAck;
import com.les.its.open.area.juncer.msg.cmd.sub.SetStageExt;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.les.its.open.area.net.utils.JuncerUtils.SIZE_CHANGE_ABLE;

@Component
public class TabSetStageAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_SET_STAGE_ACK;
    }

    @Override
    public int oneItemSize() {
        return SIZE_CHANGE_ABLE;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        final int length = 2 + 6 + 64 + 64;

        if((length != body.length) && (length - 1 != body.length)){
            return Optional.empty();
        }

        if(body.length == length) {
            TabSetStageAck tabSetStageAck = new TabSetStageAck();
            tabSetStageAck.setAck((body[0] & 0xff));
            tabSetStageAck.setReason(body[1] & 0xff);

            TabSetStage tabSetStage = new TabSetStage();
            tabSetStageAck.setTabSetStage(tabSetStage);
            tabSetStage.setCrossingSeqNo(body[2] & 0xff);
            tabSetStage.setCharacter(body[3] & 0xff);
            tabSetStage.setStageNo(body[4] & 0xff);
            tabSetStage.setStageSeq(body[5] & 0xff);
            tabSetStage.setDuration(body[6] & 0xff + ((body[7] & 0xff) << 8));

            List<SetStageExt> extParams = new ArrayList<>();
            for (int i = 0; i < 64; i++) {
                int laggingTime = ((body[8 + i] & 0xff));
                int delayCutOffTime = ((body[8 + 64 + i] & 0xff));
                if(laggingTime != 0 || delayCutOffTime != 0){
                    SetStageExt setStageExt = new SetStageExt();
                    setStageExt.setPhaseNo(i + 1);
                    setStageExt.setDelayCutOffTime(delayCutOffTime);
                    setStageExt.setLaggingTime(laggingTime);
                    extParams.add(setStageExt);
                }
            }
            tabSetStage.setExtParams(extParams);

            return Optional.of(tabSetStageAck);
        }else {
            TabSetStageAck tabSetStageAck = new TabSetStageAck();
            tabSetStageAck.setAck((body[0] & 0xff));
            tabSetStageAck.setReason(body[1] & 0xff);

            TabSetStage tabSetStage = new TabSetStage();
            tabSetStageAck.setTabSetStage(tabSetStage);
            tabSetStage.setCrossingSeqNo(body[2] & 0xff);
            tabSetStage.setCharacter(4);
            tabSetStage.setStageNo(body[3] & 0xff);
            tabSetStage.setStageSeq(body[4] & 0xff);
            tabSetStage.setDuration(body[5] & 0xff + ((body[6] & 0xff) << 8));

            List<SetStageExt> extParams = new ArrayList<>();
            for (int i = 0; i < 64; i++) {
                int laggingTime = ((body[7 + i] & 0xff));
                int delayCutOffTime = ((body[7 + 64 + i] & 0xff));
                if(laggingTime != 0 || delayCutOffTime != 0){
                    SetStageExt setStageExt = new SetStageExt();
                    setStageExt.setPhaseNo(i + 1);
                    setStageExt.setDelayCutOffTime(delayCutOffTime);
                    setStageExt.setLaggingTime(laggingTime);
                    extParams.add(setStageExt);
                }
            }
            tabSetStage.setExtParams(extParams);

            return Optional.of(tabSetStageAck);
        }

    }

}

package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabShield;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabShieldProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_SHIELD;
    }

    @Override
    public int oneItemSize() {
        return (1+ 8);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
           TabShield tabShield = new TabShield();
           ByteBuf buf = Unpooled.buffer(body.length);
           buf.writeBytes(body);
           // 类型
            tabShield.setType(buf.readUnsignedByte() & 0xff);
            // 屏蔽状态
            List<Integer> shields = new ArrayList<>();
            tabShield.setShields(shields);
            for (int i = 0; i < 8; i++) {
                int data = buf.readUnsignedByte() & 0xff;
                for (int t = 0; t < 8; t++) {
                    shields.add((data >> t) & 0x01);
                }
            }
           buf.release();
           return Optional.of(tabShield);
        }

        return Optional.empty();
    }
}

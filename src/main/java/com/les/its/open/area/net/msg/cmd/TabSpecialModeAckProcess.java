package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabSpecialMode;
import com.les.its.open.area.juncer.msg.cmd.TabSpecialModeAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabSpecialModeAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_SPECIAL_MODE_ACK;
    }

    @Override
    public int oneItemSize() {
        return 4;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabSpecialModeAck tabSpecialModeAck = new TabSpecialModeAck();
            tabSpecialModeAck.setAck(buf.readUnsignedByte() & 0xff);
            tabSpecialModeAck.setReason(buf.readUnsignedByte() & 0xff);

            TabSpecialMode tabSpecialMode = new TabSpecialMode();
            tabSpecialMode.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            tabSpecialMode.setControlMode(buf.readUnsignedByte() & 0xff);

            buf.release();

            return Optional.of(tabSpecialModeAck);
        }

        return Optional.empty();
    }

}

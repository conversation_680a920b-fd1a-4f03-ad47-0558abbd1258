package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;

public class IpAddressValidator implements ConstraintValidator<ValidIp, String> {
        private static final String IPv4_PATTERN =
                "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
                        "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
                        "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
                        "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";

        private static final String IPv6_PATTERN =
                "^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|" +
                        "([0-9a-fA-F]{1,4}:){1,7}:|" +
                        "([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|" +
                        "([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|" +
                        "([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|" +
                        "([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|" +
                        "([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|" +
                        "[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|" +
                        ":((:[0-9a-fA-F]{1,4}){1,7}|:)|" +
                        "fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|" +
                        "::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|" +
                        "([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$";

        private ValidIp.IpVersion[] versions;

        @Override
        public void initialize(ValidIp constraintAnnotation) {
            this.versions = constraintAnnotation.versions();
        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if (value == null || value.isEmpty()) {
                return true; // 空值由@NotNull或@NotEmpty处理
            }

            boolean isValid = false;
            for (ValidIp.IpVersion version : versions) {
                switch (version) {
                    case IPv4:
                        if (value.matches(IPv4_PATTERN)) {
                            isValid = true;
                        }
                        break;
                    case IPv6:
                        if (value.matches(IPv6_PATTERN)) {
                            isValid = true;
                        }
                        break;
                }

                if (isValid) {
                    break;
                }
            }

            if (!isValid) {
                // 自定义错误消息
                context.disableDefaultConstraintViolation();
                String expectedVersions = Arrays.toString(versions);
                context.buildConstraintViolationWithTemplate(
                                "IP地址必须是 " + expectedVersions + " 格式")
                        .addConstraintViolation();
            }

            return isValid;
        }
}
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhaseTableLimitGreenInfo {

    // 相位最小绿时间1
    @NotNull(message = "相位最小绿时间1不能为空")
    @Range(min = 0, max = 30, message = "相位最小绿时间1有效范围为[0,30]")
    private Integer minGreenTime1;
    
    // 相位最小绿时间2
    private Integer minGreenTime2;
    
    // 相位最小绿时间3
    private Integer minGreenTime3;
    
    // 相位最小绿时间4
    private Integer minGreenTime4;

    // 相位最大绿时间1
    @NotNull(message = "相位最大绿时间1不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间1有效范围为[0,255]")
    private Integer maxGreenTime1;
    
    // 相位最大绿时间2
    @NotNull(message = "相位最大绿时间2不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间2有效范围为[0,255]")
    private Integer maxGreenTime2;
    
    // 相位最大绿时间3
    @NotNull(message = "相位最大绿时间3不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间3有效范围为[0,255]")
    private Integer maxGreenTime3;
    
    // 相位最大绿时间4
    @NotNull(message = "相位最大绿时间4不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间4有效范围为[0,255]")
    private Integer maxGreenTime4;

    // 相位最大绿时间5
    @NotNull(message = "相位最大绿时间5不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间5有效范围为[0,255]")
    private Integer maxGreenTime5;

    // 相位最大绿时间6
    @NotNull(message = "相位最大绿时间6不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间6有效范围为[0,255]")
    private Integer maxGreenTime6;

    // 相位最大绿时间7
    @NotNull(message = "相位最大绿时间7不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间7有效范围为[0,255]")
    private Integer maxGreenTime7;

    // 相位最大绿时间8
    @NotNull(message = "相位最大绿时间8不能为空")
    @Range(min = 0, max = 255, message = "相位最大绿时间8有效范围为[0,255]")
    private Integer maxGreenTime8;



} 
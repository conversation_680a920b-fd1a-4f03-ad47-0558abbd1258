package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * 屏蔽控制
 */
@Data
public class TabShieldCmd  extends TabInBase implements NeedAck {
    
    /**
     * 属性
     * 1灯组/2相位/4阶段
     */
    @NotNull(message = "属性不能为空")
    @DigitsInSet(acceptedValues = {1, 2, 4}, message = "属性必须是1、2或4")
    private Integer cmdNo;
    
    /**
     * 屏蔽状态
     */
    @NotNull(message = "屏蔽状态不能为空")
    @Size(min = 64, max = 64, message = "屏蔽状态固定长度64")
    @DiscreteValuesList(acceptedValues = {0, 1}, message = "屏蔽状态值必须是0或1")
    private List<Integer> shields;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_SHIELD_CMD.getCode());
    }
} 
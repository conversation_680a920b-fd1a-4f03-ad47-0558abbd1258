package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 自定义验证器实现，用于验证多个整数是否都在指定的列表中或范围内
 */
public class DiscreteValuesListValidator implements ConstraintValidator<DiscreteValuesList, List<Integer>> {
    private Set<Integer> acceptedValues;
    private boolean allowDuplicates;
    private boolean allowNull;
    private int min;
    private int max;

    @Override
    public void initialize(DiscreteValuesList constraintAnnotation) {
        acceptedValues = new HashSet<>();
        for (int value : constraintAnnotation.acceptedValues()) {
            acceptedValues.add(value);
        }
        allowDuplicates = constraintAnnotation.allowDuplicates();
        allowNull = constraintAnnotation.allowNull();
        min = constraintAnnotation.min();
        max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(List<Integer> values, ConstraintValidatorContext context) {
        if (values == null) {
            return allowNull;
        }

        if (values.isEmpty()) {
            return true;
        }

        // 如果不允许重复值，检查是否有重复
        if (!allowDuplicates) {
            Set<Integer> uniqueValues = new HashSet<>(values);
            if (uniqueValues.size() != values.size()) {
                return false;
            }
        }

        // 检查所有值是否都在接受列表中或范围内
        for (Integer value : values) {
            if (value == null) {
                if (!allowNull) {
                    return false;
                }
                continue;
            }

            // 检查是否在范围内(当为空是)
            boolean minMaxFlag = false;
            boolean acceptedValuesFlag = false;
            if ((value >= min && value <= max)) {
                minMaxFlag =  true;
            }

            // 如果指定了离散值列表，则必须同时满足在列表中
            if (!acceptedValues.isEmpty() && acceptedValues.contains(value)) {
                acceptedValuesFlag = true;
            }

            //同时不满足时返回
            if(!minMaxFlag && !acceptedValuesFlag){
                return false;
            }
        }

        return true;
    }
} 
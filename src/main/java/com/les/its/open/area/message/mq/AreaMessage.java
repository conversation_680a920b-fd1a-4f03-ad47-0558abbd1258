package com.les.its.open.area.message.mq;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.param.TabLook;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:04
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AreaMessage {

    private int msg1;

    private int msg2;

    private int msg3;

    private int msg4;

    private int noArea;

    private int noJunc;

    private int source;

    private long timeStamp;

    private Object data;

    /**
     * 判定是否是模拟的数据报文·
     */
    private boolean simSend;

    /**
     * 在模拟发送的时候，是否直接应答
     */
    private boolean ackDirect;


    /**
     * 生成调看报文消息
     * @param paramMsgType
     * @param controllerBaseInfo
     * @return
     */
    public static AreaMessage genLoadMsg(ParamMsgType paramMsgType,
                                         Object data,
                                         ControllerBaseInfo controllerBaseInfo){

        return AreaMessage.builder()
                .msg1((MsgType.TAB_LOAD_PARAM.getCode() >> 24) & 0xff)
                .msg2((MsgType.TAB_LOAD_PARAM.getCode() >> 16) & 0xff)
                .msg3((paramMsgType.getCode() >> 8) & 0xff)
                .msg4((paramMsgType.getCode()) & 0xff)
                .noArea(controllerBaseInfo.getNoArea())
                .noJunc(controllerBaseInfo.getNoJunc())
                .source(0)
                .timeStamp(System.currentTimeMillis())
                .data(data)
                .build();
    }

    /**
     * 生成命令报文消息
     * @param msgType
     * @param controllerBaseInfo
     * @return
     */
    public static AreaMessage genCmdMsg(MsgType msgType,
                                         Object data,
                                         ControllerBaseInfo controllerBaseInfo){

        return AreaMessage.builder()
                .msg1((msgType.getCode() >> 24) & 0xff)
                .msg2((msgType.getCode() >> 16) & 0xff)
                .msg3((msgType.getCode() >> 8) & 0xff)
                .msg4((msgType.getCode()) & 0xff)
                .noArea(controllerBaseInfo.getNoArea())
                .noJunc(controllerBaseInfo.getNoJunc())
                .source(0)
                .timeStamp(System.currentTimeMillis())
                .data(data)
                .build();
    }


    /**
     * 生成命令报文消息
     * @param msgType
     * @param controllerBaseInfo
     * @return
     */
    public static AreaMessage genCmdMsg(MsgType msgType,
                                        Object data,
                                        ControllerBaseInfo controllerBaseInfo, boolean simSend, boolean ackDirect){

        return AreaMessage.builder()
                .msg1((msgType.getCode() >> 24) & 0xff)
                .msg2((msgType.getCode() >> 16) & 0xff)
                .msg3((msgType.getCode() >> 8) & 0xff)
                .msg4((msgType.getCode()) & 0xff)
                .noArea(controllerBaseInfo.getNoArea())
                .noJunc(controllerBaseInfo.getNoJunc())
                .source(0)
                .timeStamp(System.currentTimeMillis())
                .data(data)
                .simSend(simSend)
                .ackDirect(ackDirect)
                .build();
    }

    /**
     * 生成调看报文消息
      * @param paramMsgType
     * @param controllerBaseInfo
     * @return
     */
    public static AreaMessage genLookMsg(ParamMsgType paramMsgType,
                                         int offset,
                                         int count, ControllerBaseInfo controllerBaseInfo){

        TabLook tabLook = new TabLook();
        tabLook.setParamMsgType(paramMsgType);
        tabLook.setOffset(offset);
        tabLook.setCount(count);

        return AreaMessage.builder()
                .msg1((MsgType.TAB_LOOK.getCode() >> 24) & 0xff)
                .msg2((MsgType.TAB_LOOK.getCode() >> 16) & 0xff)
                .msg3((paramMsgType.getCode() >> 8) & 0xff)
                .msg4((paramMsgType.getCode()) & 0xff)
                .noArea(controllerBaseInfo.getNoArea())
                .noJunc(controllerBaseInfo.getNoJunc())
                .source(0)
                .timeStamp(System.currentTimeMillis())
                .data(tabLook)
                .build();
    }

    /**
     * 生成调看报文消息
     * @param paramMsgType
     * @param controllerBaseInfo
     * @return
     */
    public static AreaMessage genLookMsg(ParamMsgType paramMsgType, ControllerBaseInfo controllerBaseInfo){
        return genLookMsg(
                paramMsgType,
                1,
                1,
                controllerBaseInfo
              );
    }


    public static AreaMessage genMqMessage(TabOutBase tabOutBase, ControllerBaseInfo controllerBaseInfo) {

        return AreaMessage.builder()
                .msg1((tabOutBase.getMsgType().getCode() >> 24) & 0xff)
                .msg2((tabOutBase.getMsgType().getCode() >> 16) & 0xff)
                .msg3((tabOutBase.getMsgType().getCode() >> 8) & 0xff)
                .msg4((tabOutBase.getMsgType().getCode()) & 0xff)
                .noArea(controllerBaseInfo.getNoArea())
                .noJunc(controllerBaseInfo.getNoJunc())
                .source(0)
                .timeStamp(System.currentTimeMillis())
                .data(tabOutBase)
                .build();

    }

}

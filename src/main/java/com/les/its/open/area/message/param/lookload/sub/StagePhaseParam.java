package com.les.its.open.area.message.param.lookload.sub;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 阶段相位参数
 */
@Data
public class StagePhaseParam {
    /**
     * 相位编号
     * [1-64]
     */
    @NotNull(message = "{basic.param.stagePhase.phaseNo.notNull}")
    @Min(value = 1, message = "{basic.param.stagePhase.phaseNo.min}")
    @Max(value = 64, message = "{basic.param.stagePhase.phaseNo.max}")
    private Integer phaseNo;

    /**
     * 相位出现类型
     * 1: 固定出现, 2: 按需出现
     */
    @NotNull(message = "{basic.param.stagePhase.demand.notNull}")
    @DigitsInSet(acceptedValues = {1, 2}, message = "{basic.param.stagePhase.demand.discrete}")
    private Integer demand;

    /**
     * 迟后时长
     * 单位秒
     *  允许上级传递null，不传递
     */
    //@NotNull(message = "{basic.param.stagePhase.laggingTime.notNull}")
    @Min(value = 0, message = "{basic.param.stagePhase.laggingTime.min}")
    @Max(value = 255, message = "{basic.param.stagePhase.laggingTime.max}")
    private Integer laggingTime;

    /**
     * 早截时长
     * 单位秒
     *   允许上级传递null，不传递
     */
    //@NotNull(message = "{basic.param.stagePhase.delayCutOffTime.notNull}")
    @Min(value = 0, message = "{basic.param.stagePhase.delayCutOffTime.min}")
    @Max(value = 255, message = "{basic.param.stagePhase.delayCutOffTime.max}")
    private Integer delayCutOffTime;
} 
package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.List;

/**
 * 感应相位控制
 */
@Data
public class TabActuatedPhaseControl  extends TabInBase implements NeedAck {
    
    /**
     * 相位控制类型
     * 1-忽略控制
     * 2-保持控制
     * 4-强制关闭控制
     * 8-呼叫控制
     */
    @NotNull(message = "相位控制类型不能为空")
    @DigitsInSet(acceptedValues = {1, 2, 4, 8}, message = "相位控制类型必须是1、2、4或8")
    private Integer cmdNo;
    
    /**
     * 相位控制
     */
    @NotNull(message = "相位控制不能为空")
    @Size(min = 64, max = 64, message = "相位控制固定长度64")
    @DiscreteValuesList(acceptedValues = {0, 1}, message = "相位控制值必须是0或1")
    private List<Integer> phases;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###"
                + String.format("0x%08X", MsgType.TAB_ACTIVATED_PHASE_CONTROL.getCode());
    }
}
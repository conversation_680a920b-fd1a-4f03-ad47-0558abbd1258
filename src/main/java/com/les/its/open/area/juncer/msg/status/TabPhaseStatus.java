package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.sub.PhaseStatusDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 实时相位/灯组状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabPhaseStatus extends TabOutBase {

    private int crossingSeqNo;

    private long phaseFlag;

    /**
     * 路口状态
     */
    private List<TabPhaseStatusCrossStatus> crossStatuses;

    
    /**
     * 灯组状态
     */
    private List<Integer> lightsGroupStatuses;
    
    /**
     * 相位需运行时间
     */
    private List<Integer> needRunningTimes2;
    
    /**
     * 相位已运行时间
     */
    private List<Integer> runningTimes2;
    
    /**
     * 相位状态
     */
    private List<Integer> phaseStatuses2;

    /**
     * 整合的相位状态
     */
    private List<PhaseStatusDto> phaseStatuses;


    public void genPhaseStatuses(){

        phaseStatuses = new ArrayList<>();

        for (int i = 0; i < 64; i++) {

            if(((phaseFlag >> i) & (0x01)) == 0x0){
                continue;
            }

            PhaseStatusDto phaseStatusDto = new PhaseStatusDto();
            phaseStatusDto.setPhaseNo(i + 1);
            phaseStatusDto.setNeedRunningTimes(needRunningTimes2.get(i));
            phaseStatusDto.setRunningTimes(runningTimes2.get(i));
            phaseStatusDto.setPhaseStatuses(phaseStatuses2.get(i));
            phaseStatuses.add(phaseStatusDto);
        }

    }


    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        controllerAgent.setTabPhaseStatus(this);

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }
} 
package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.BasicParamUsed;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InformationBoard;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.ControllerParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class ControllerParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public ControllerParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_ControllerParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return ControllerParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();


        // 调看信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_DEVICE_INFO, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_BOARD_INFO, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_BASIC_CONFIG, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_LAMP_FAULT_DETECT, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_MODE_PRIORITY, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_CONSTRAINT, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_BASIC_PARAM_USED, signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            ControllerParam controllerParam = new ControllerParam();
            //设置信号机编号
            controllerParam.setSignalControllerID(requestMessage.getSignalControllerID());


            //设备信息
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.DeviceInfo> deviceInfoOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_DEVICE_INFO);
            if(deviceInfoOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_DEVICE_INFO);
                return Optional.empty();
            }

            //设备信息
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.BoardInfo> boardInfoOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_BOARD_INFO);
            if(boardInfoOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_BOARD_INFO);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), boardInfoOp.get());

            //基础配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.BasicConfig> basicConfigOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_BASIC_CONFIG);
            if(basicConfigOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_BASIC_CONFIG);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), basicConfigOp.get());

            //灯检测配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultDetect> lampFaultDetectOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_LAMP_FAULT_DETECT);
            if(lampFaultDetectOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_LAMP_FAULT_DETECT);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), lampFaultDetectOp.get());


            //控制模式优先级
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.ModePriority> modePriorityOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_MODE_PRIORITY);
            if(modePriorityOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_MODE_PRIORITY);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), modePriorityOp.get());


            //相位约束信息
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.Constraint> constraintOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_CONSTRAINT);
            if(constraintOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_CONSTRAINT);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), constraintOp.get());


            //使用信息
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.BasicParamUsed> basicParamUsedOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_BASIC_PARAM_USED);
            if(basicParamUsedOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_BASIC_PARAM_USED);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), basicParamUsedOp.get());

            {
                //设备编号
                controllerParam.setDeviceVersion(deviceInfoOp.get().getDeviceVersion());

                //板卡信息
                {
                    //主控板序列号
                    controllerParam.setControlBoardSerialNumber(boardInfoOp.get().getControlBoardSerialNumber());
                    //灯驱板数
                    controllerParam.setLampDriverBoardMaxNumber(boardInfoOp.get().getLampDriverBoardMaxNumber());
                    //灯驱板信息
                    controllerParam.setLampDriverBoards(boardInfoOp.get().getLampDriverBoards());
                    //检测板数
                    controllerParam.setDetectionBoardMaxNumber(boardInfoOp.get().getDetectionBoardMaxNumber());
                    //检测板信息
                    controllerParam.setDetectionBoards(boardInfoOp.get().getDetectionBoards());
                    //信息板数
                    controllerParam.setInformationBoardMaxNumber(boardInfoOp.get().getInformationBoardMaxNumber());
                    //信息板信息
                    List<InformationBoard> informationBoards = new ArrayList<>();
                    informationBoards.add(boardInfoOp.get().getInformationBoard());
                    controllerParam.setInformationBoards(informationBoards);
                    //灯组数
                    controllerParam.setLampGroupNumber(boardInfoOp.get().getLampGroupNumber());
                }

                //基础参数
                {
                    //可变标志数
                    controllerParam.setSwitchedSigns(basicConfigOp.get().getSwitchedSigns());
                    //协调参考点
                    controllerParam.setSystemCordRef(basicConfigOp.get().getSystemCordRef());
                    //看门狗最大绿
                    controllerParam.setMaxGreenWatchDog(basicConfigOp.get().getMaxGreenWatchDog());
                    //启动绿灯间隔
                    controllerParam.setStartInterGreen(basicConfigOp.get().getStartInterGreen());
                    //阶段跳转禁止
                    controllerParam.setStageSkipProhibited(basicConfigOp.get().getStageSkipProhibited());
                    //降级动作
                    controllerParam.setDegradeAction(basicConfigOp.get().getDegradeAction());
                    //紧急优先执行绿看门狗
                    controllerParam.setHurryCallExecuteWatchDog(basicConfigOp.get().getHurryCallExecuteWatchDog());
                    //黄灯行人灯态标志
                    controllerParam.setYellowAsWaitIndicator(basicConfigOp.get().getYellowAsWaitIndicator());
                    // 使用相位数
                    controllerParam.setUsedPhaseNum(basicParamUsedOp.get().getUsedPhaseNum());
                    // 使用阶段数
                    controllerParam.setUsedStageNum(basicParamUsedOp.get().getUsedStageNum());
                    // 手控最大时间
                    controllerParam.setMaxManualControlTime(basicConfigOp.get().getMaxManualControlTime());
                    // 闪灯亮灯时长
                    controllerParam.setFlashOnTime(basicConfigOp.get().getFlashOnTime());
                    // 闪灯灭灯时长
                    controllerParam.setFlashOffTime(basicConfigOp.get().getFlashOffTime());
                }

                //灯故障检测
                {
                    controllerParam.setLampFaultDetect(lampFaultDetectOp.get());
                }

                //约束信息
                {
                    controllerParam.setConstraint(constraintOp.get());
                }

                //控制模式优先级
                {
                    controllerParam.setModePriority(modePriorityOp.get().getModePrioritys());
                }

            }

            //应答参数
            datas.add(controllerParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        ControllerParam controllerParam = jsonObject.toJavaObject(ControllerParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(controllerParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, controllerParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //基础参数
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.BasicConfig basicConfig
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.BasicConfig();

            //系统控制最大时间
            basicConfig.setMaxSystemControlTime(0);
            //系统协调零点
            basicConfig.setSystemCordRef(controllerParam.getSystemCordRef());
            //看门狗最大绿
            basicConfig.setMaxGreenWatchDog(controllerParam.getMaxGreenWatchDog());
            //启动绿灯间隔
            basicConfig.setStartInterGreen(controllerParam.getStartInterGreen());
            //切换指示个数
            basicConfig.setSwitchedSigns(controllerParam.getSwitchedSigns());
            //阶段跳转禁止
            basicConfig.setStageSkipProhibited(controllerParam.getStageSkipProhibited());
            //降级动作
            basicConfig.setDegradeAction(controllerParam.getDegradeAction());
            //紧急优先执行绿看门狗
            basicConfig.setHurryCallExecuteWatchDog(controllerParam.getHurryCallExecuteWatchDog());
            //黄灯行人灯态标志
            basicConfig.setYellowAsWaitIndicator(controllerParam.getYellowAsWaitIndicator());
            //手控允许最大时间
            basicConfig.setMaxManualControlTime(controllerParam.getMaxManualControlTime());
            //闪灯亮灯时长
            basicConfig.setFlashOnTime(controllerParam.getFlashOnTime());
            //闪灯灭灯时长
            basicConfig.setFlashOffTime(controllerParam.getFlashOffTime());

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_BASIC_CONFIG, basicConfig, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //灯故障检测
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultDetect lampFaultDetect
                    = controllerParam.getLampFaultDetect();
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_LAMP_FAULT_DETECT, lampFaultDetect, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //约束信息
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.Constraint constraint
                    = controllerParam.getConstraint();
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_CONSTRAINT, constraint, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //控制模式优先级
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.ModePriority modePriority
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.ModePriority();
            modePriority.setModePrioritys(controllerParam.getModePriority());
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_MODE_PRIORITY, modePriority, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //使用参数
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.BasicParamUsed basicParamUsed
                    = new BasicParamUsed();
            basicParamUsed.setUsedPhaseNum(controllerParam.getUsedPhaseNum());
            basicParamUsed.setUsedStageNum(controllerParam.getUsedStageNum());
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_BASIC_PARAM_USED, basicParamUsed, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

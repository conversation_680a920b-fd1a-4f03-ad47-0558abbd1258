package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LightsGroup implements DataIndexAble {
    // 灯相编号
    @NotNull(message = "灯相编号不能为空")
    @Range(min = 1, max = 64, message = "灯相编号有效范围是[1,64]")
    private Integer lightsGroupNo;
    
    // 子路口号
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号有效范围是[1,8]")
    private Integer crossingSeqNo;
    
    // 灯相类型
    @NotNull(message = "灯相类型不能为空")
    private Integer type;
    
    // 方向
    @NotNull(message = "方向不能为空")
    private Integer direction;
    
    // 灯相名称
    private String name;

    @Override
    public int getDataNo() {
        return lightsGroupNo;
    }
}
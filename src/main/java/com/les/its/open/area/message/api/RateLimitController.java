package com.les.its.open.area.message.api;

import com.les.its.open.area.message.service.ratelimit.DistributedRateLimitService;
import com.les.its.open.area.message.service.ratelimit.RateLimit;
import com.les.its.open.area.message.service.ratelimit.RateLimitConfig;
import com.les.its.open.area.message.service.ratelimit.RateLimitType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/demo")
@Slf4j
public class RateLimitController {

    @Autowired
    private DistributedRateLimitService rateLimitService;

    /**
     * 用户级别限流 - 每分钟100次
     */
    @GetMapping("/user/profile")
    @RateLimit(type = RateLimitType.USER, capacity = 2, tokens = 2, period = 1, timeUnit = TimeUnit.SECONDS)
    public ResponseEntity<String> getUserProfile(@RequestParam String userId) {
        return ResponseEntity.ok("User profile for: " + userId);
    }

    /**
     * IP级别限流 - 每秒10次
     */
    @PostMapping("/public/search/{id}")
    @RateLimit(type = RateLimitType.IP, capacity = 2, tokens = 2, period = 1, timeUnit = TimeUnit.SECONDS)
    public ResponseEntity<String> search(@PathVariable String id) {
        return ResponseEntity.ok("Search results " + id);
    }

    /**
     * 自定义key限流 - 基于商户ID
     */
    @PostMapping("/merchant/{merchantId}/order")
    @RateLimit(type = RateLimitType.CUSTOM, key = "#{#merchantId}", capacity = 50, tokens = 50)
    public ResponseEntity<String> createOrder(@PathVariable String merchantId) {
        return ResponseEntity.ok("Order created for merchant: " + merchantId);
    }

    /**
     * 手动流控示例
     */
    @GetMapping("/manual-limit/{userId}")
    public ResponseEntity<String> manualLimit(@PathVariable String userId) {
        String key = "manual_limit:" + userId;
        RateLimitConfig config = RateLimitConfig.PER_SECOND_10;

        if (rateLimitService.tryConsume(key, config)) {
            return ResponseEntity.ok("Request processed");
        } else {
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .body("Rate limit exceeded");
        }
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class LampDriverBoard {
    // 是否启用
    @Range(min = 0, max = 1, message = "{juncer.param.lampDriverBoard.enabled.range}")
    private int enabled;

    // 灯控板序列号
    @NotNull(message = "{juncer.param.lampDriverBoard.serialNumber.notNull}")
    private String serialNumber;


}

package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabPhaseStatus;
import com.les.its.open.area.juncer.msg.status.TabPhaseStatusCrossStatus;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabPhaseStatusProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_PHASE_STATUS;
    }

    @Override
    public int oneItemSize() {
        return ( 1 + 8  + 64 + 64 * 2 + 64 * 2 + 64);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabPhaseStatus tabPhaseStatus = new TabPhaseStatus();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            tabPhaseStatus.setCrossingSeqNo(buf.readUnsignedByte());

            tabPhaseStatus.setPhaseFlag(buf.readLongLE());

            List<TabPhaseStatusCrossStatus> crossStatus = new ArrayList<>();
            tabPhaseStatus.setCrossStatuses(crossStatus);

            // 灯组状态
            List<Integer> lightsGroupStatus = new ArrayList<>();
            tabPhaseStatus.setLightsGroupStatuses(lightsGroupStatus);
            for (int i = 0; i < 64; i++) {
                lightsGroupStatus.add(buf.readUnsignedByte() & 0xff);
            }

            // 相位需运行时间
            List<Integer> needRunningTime = new ArrayList<>();
            tabPhaseStatus.setNeedRunningTimes2(needRunningTime);
            for (int i = 0; i < 64; i++) {
                needRunningTime.add(buf.readUnsignedShortLE() & 0xffff);
            }

            // 相位已运行时间
            List<Integer> runningTime = new ArrayList<>();
            tabPhaseStatus.setRunningTimes2(runningTime);
            for (int i = 0; i < 64; i++) {
                runningTime.add(buf.readUnsignedShortLE() & 0xffff);
            }

            // 相位状态
            List<Integer> phaseStatus = new ArrayList<>();
            tabPhaseStatus.setPhaseStatuses2(phaseStatus);
            for (int i = 0; i < 64; i++) {
                phaseStatus.add(buf.readUnsignedByte() & 0xff);
            }

            //生成与4.0系统参数
            tabPhaseStatus.genPhaseStatuses();

            buf.release();
            return Optional.of(tabPhaseStatus);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.net.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.MDC;

public class IpBasedLoggingFilter extends Filter<ILoggingEvent> {

    @Override
    public FilterReply decide(ILoggingEvent event) {
        String currentIp = MDC.get("clientIP");
        if (currentIp == null || currentIp.isEmpty() || "unknown".equals(currentIp)) {
            return FilterReply.DENY;
        }
        return FilterReply.ACCEPT;
    }
}

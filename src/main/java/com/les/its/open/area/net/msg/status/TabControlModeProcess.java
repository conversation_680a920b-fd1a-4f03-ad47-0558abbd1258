package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabControlMode;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabControlModeProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_CONTROL_MODE;
    }

    @Override
    public int oneItemSize() {
        return 3;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabControlMode tabControlMode = new TabControlMode();
            tabControlMode.setCrossingSeqNo((body[0] & 0xff));
            tabControlMode.setControlModeNo((body[1] & 0xff));
            tabControlMode.setCharacter((body[2] & 0xff));
            return Optional.of(tabControlMode);
        }

        return Optional.empty();
    }
}

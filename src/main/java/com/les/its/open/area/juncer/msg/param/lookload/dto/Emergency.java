package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Emergency implements DataIndexAble {
    // 紧急编号
    @NotNull(message = "{juncer.param.emergency.emergencyNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.emergency.emergencyNo.range}")
    private Integer emergencyNo;
    
    // 紧急申请相位阶段
    @NotNull(message = "{juncer.param.emergency.stageNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.emergency.stageNo.range}")
    private Integer stageNo;
    
    // 紧急申请优先级
    @NotNull(message = "{juncer.param.emergency.priority.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.emergency.priority.range}")
    private Integer priority;

    // 延迟时间
    @NotNull(message = "{juncer.param.emergency.delayTime.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.emergency.delayTime.range}")
    private Integer delayTime;
    
    // 持续时间
    @NotNull(message = "{juncer.param.emergency.durationTime.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.emergency.durationTime.range}")
    private Integer durationTime;
    
    // 呼叫间隔时间
    @NotNull(message = "{juncer.param.emergency.callIntervalTime.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.emergency.callIntervalTime.range}")
    private Integer callIntervalTime;

    // 需求变量类型
    @NotNull(message = "{juncer.param.emergency.paramTypeCall.notNull}")
    private Integer paramTypeCall;

    /**
     * 需求变量编号
     */
    @NotNull(message = "{juncer.param.emergency.paramNoCall.notNull}")
    private Integer paramNoCall;

    /**
     * 删除变量类型
     */
    @NotNull(message = "{juncer.param.emergency.paramTypeCancel.notNull}")
    private Integer paramTypeCancel;

    /**
     * 删除变量编号
     */
    @NotNull(message = "{juncer.param.emergency.paramNoCancel.notNull}")
    private Integer paramNoCancel;

    /**
     * 确认变量类型
     */
    @NotNull(message = "{juncer.param.emergency.paramTypeConfirm.notNull}")
    private Integer paramTypeConfirm;

    /**
     * 确认变量编号
     */
    @NotNull(message = "{juncer.param.emergency.paramNoConfirm.notNull}")
    private Integer paramNoConfirm;

    /**
     * 紧急优先呼叫看门狗
     */
    @NotNull(message = "{juncer.param.emergency.callWatchDog.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.emergency.callWatchDog.range}")
    private Integer callWatchDog;

    @Override
    public int getDataNo() {
        return emergencyNo;
    }
}
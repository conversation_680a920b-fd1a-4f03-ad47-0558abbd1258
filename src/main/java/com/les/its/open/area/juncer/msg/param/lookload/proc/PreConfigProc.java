package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.PreConfig;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PreConfigProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PRE_CONFIG;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {

        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        PreConfig preConfig = new PreConfig();

        //6位mac地址
        {
            List<Short> datas = new ArrayList<>();
            for (int i = 0; i < 6; i++) {
                datas.add(buf.readUnsignedByte());
            }
            String macAddress = datas.stream().map(
                    data -> String.format("%02x", data)
            ).collect(Collectors.joining("-"));
            preConfig.setMacAddress(macAddress);
        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(preConfig);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof PreConfig)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        PreConfig preConfig = (PreConfig)objects.get(0);

        //6位mac地址
        {
            String[] dataStrs = preConfig.getMacAddress().split("-");
            List<Integer> datas = Arrays.stream(dataStrs).map(
                    dataStr -> Integer.valueOf(dataStr, 16)
            ).toList();
            for (int i = 0; i < 6 && i < datas.size(); i++) {
                buf.writeByte(datas.get(i));
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return   6;
    }

    @Override
    public Class dataClazz() {
        return PreConfig.class;
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.TscBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class TscBaseInfoProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_BASE_INFO;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        TscBaseInfo tscBaseInfo = new TscBaseInfo();
        //安装路口
        {
            byte[] manufacturer = new byte[128];
            buf.readBytes(manufacturer);
            tscBaseInfo.setInstallIntersection(new String(manufacturer, StandardCharsets.UTF_8).trim());
        }

        //信号机ID
        {
            tscBaseInfo.setTscId(buf.readUnsignedIntLE());
        }

        //信号机控制路口
        {
            tscBaseInfo.setControlledJunctionNum(buf.readUnsignedByte());
        }

        //经度
        {
            long longitude = buf.readIntLE();
            tscBaseInfo.setLongitude(longitude * 1.0 / 10000000);
        }

        //纬度
        {
            long latitude = buf.readIntLE();
            tscBaseInfo.setLatitude(latitude * 1.0 / 10000000);
        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(tscBaseInfo);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {

        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof TscBaseInfo)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        TscBaseInfo tscBaseInfo = (TscBaseInfo)objects.get(0);
        //安装路口
        {
            byte[] installIntersection = new byte[128];
            byte[] bytes = tscBaseInfo.getInstallIntersection().getBytes(StandardCharsets.UTF_8);
            System.arraycopy(bytes, 0, installIntersection, 0, Math.min(bytes.length, 128));
            buf.writeBytes(installIntersection);
        }

        //信号机ID
        {
            buf.writeIntLE((int) (tscBaseInfo.getTscId() & 0xffffffffL));
        }

        //信号机控制路口
        {
            buf.writeByte(tscBaseInfo.getControlledJunctionNum());
        }

        //经度
        {
            double longitude = tscBaseInfo.getLongitude() * 10000000;
            long longitudeInt = (long) (longitude);
            buf.writeIntLE((int) (longitudeInt & 0xffffffffL));
        }

        //纬度
        {
            double latitude = tscBaseInfo.getLatitude() * 10000000;
            long latitudeInt = (long) (latitude);
            buf.writeIntLE((int) (latitudeInt & 0xffffffffL));
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 128 + 4 + 1 + 8;
    }

    @Override
    public Class dataClazz() {
        return TscBaseInfo.class;
    }


}

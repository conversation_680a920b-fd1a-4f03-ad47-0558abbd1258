package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.area.message.param.lookload.sub.StageParamInfo;
import com.les.its.open.area.message.param.lookload.sub.StartupStage;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 阶段参数信息
 */
@Data
public class StageParam implements SgpTransAble {
    public static final String MqObjectId = "10007";

    /**
     * 阶段相位参数列表
     */
    @NotNull(message = "{basic.param.stage.stageParams.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.stage.stageParams.size}")
    @Valid
    private List<StageParamInfo> stageParams;

    /**
     * 子路口启动参数
     */
    @NotNull(message = "{basic.param.stage.startupStages.notNull}")
    @Size(min = 1, max = 8, message = "{basic.param.stage.startupStages.size}")
    @Valid
    private List<StartupStage> startupStages;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }

} 
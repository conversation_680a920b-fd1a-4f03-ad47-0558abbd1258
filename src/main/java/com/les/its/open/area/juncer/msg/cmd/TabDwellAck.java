package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 指定相位控制应答
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabDwellAck extends TabOutBase implements AckAble {

    private int ack;
    private int reason;

    private TabDwell tabDwell;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_DWELL.getCode());
    }

}
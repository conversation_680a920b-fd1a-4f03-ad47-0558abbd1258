package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv4;
import com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv6;
import com.les.its.open.area.juncer.msg.param.lookload.dto.HostNetwork;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class HostNetworkProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_HOST_NETWORK;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        HostNetwork hostNetwork = new HostNetwork();

        List<HostIpv4> hostIpv4s = new ArrayList<>();
        hostNetwork.setHostIpv4s(hostIpv4s);
        List<HostIpv6> hostIpv6s = new ArrayList<>();
        hostNetwork.setHostIpv6s(hostIpv6s);

        //ipv4数据参数
        for (int index = 0; index < 4; index++) {

            HostIpv4 hostIpv4 = new HostIpv4();
            hostIpv4s.add(hostIpv4);

            //启用状态
            {
                int ipEnabled = buf.readUnsignedByte();
                hostIpv4.setEnabled(ipEnabled);
            }

            //4位ip地址
            {
                List<Short> datas = new ArrayList<>();
                for (int i = 0; i < 4; i++) {
                    datas.add(buf.readUnsignedByte());
                }
                String ip = datas.stream().map(
                        data -> Integer.toString(data & 0xFF)
                ).collect(Collectors.joining("."));
                hostIpv4.setIp(ip);
            }

            //端口数据
            {
                int port = buf.readUnsignedShortLE();
                hostIpv4.setPort(port);
            }

            //通信类型
            {
                int commType = buf.readUnsignedByte();
                hostIpv4.setCommType(commType);
            }

            //协议类型
            {
                int protoType = buf.readUnsignedByte();
                hostIpv4.setProtoType(protoType);
            }
        }

        //ipv6数据参数
        for (int index = 0; index < 4; index++) {

            HostIpv6 hostIpv6 = new HostIpv6();
            hostIpv6s.add(hostIpv6);

            //启用状态
            {
                int ipEnabled = buf.readUnsignedByte();
                hostIpv6.setEnabled(ipEnabled);
            }

            //ipv6地址
            {
                List<Integer> datas = new ArrayList<>();
                for (int i = 0; i < 8; i++) {
                    datas.add(buf.readUnsignedShort());
                }
                String ip = datas.stream().map( data -> String.format("%04x", data))
                        .collect(Collectors.joining(":"));
                hostIpv6.setIp(ip);
            }

            //端口数据
            {
                int port = buf.readUnsignedShortLE();
                hostIpv6.setPort(port);
            }

            //通信类型
            {
                int commType = buf.readUnsignedByte();
                hostIpv6.setCommType(commType);
            }

            //协议类型
            {
                int protoType = buf.readUnsignedByte();
                hostIpv6.setProtoType(protoType);
            }
        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(hostNetwork);
        return Optional.of(objects);

    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof HostNetwork)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        HostNetwork hostNetwork = (HostNetwork)objects.get(0);

        //补全数据项
        if(hostNetwork.getHostIpv4s() == null){
            hostNetwork.setHostIpv4s(new ArrayList<>());
        }
        if(hostNetwork.getHostIpv4s().size() < 4){
            for (int i = hostNetwork.getHostIpv4s().size(); i < 4; i++) {
                hostNetwork.getHostIpv4s().add(HostIpv4.genDefaultHostIpv4());
            }
        }

        if(hostNetwork.getHostIpv6s() == null){
            hostNetwork.setHostIpv6s(new ArrayList<>());
        }
        if(hostNetwork.getHostIpv6s().size() < 4){
            for (int i = hostNetwork.getHostIpv6s().size(); i < 4; i++) {
                hostNetwork.getHostIpv6s().add(HostIpv6.genDefaultHostIpv6());
            }
        }

        //ipv4数据参数
        for (int index = 0; (index < 4) && (index < hostNetwork.getHostIpv4s().size()); index++) {

            HostIpv4 hostIpv4 = hostNetwork.getHostIpv4s().get(index);
            //启用状态
            {
                buf.writeByte(hostIpv4.getEnabled());
            }

            //4位ip地址
            {
                String[] dataStrs = hostIpv4.getIp().split("\\.");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 10)
                ).toList();
                for (int i = 0; i < 4 && i < datas.size(); i++) {
                    buf.writeByte(datas.get(i));
                }
            }

            //端口数据
            {
                buf.writeShortLE(hostIpv4.getPort());
            }

            //通信类型
            {
                buf.writeByte(hostIpv4.getCommType());
            }

            //协议类型
            {
                buf.writeByte(hostIpv4.getProtoType());
            }
        }

        //ipv6数据参数
        for (int index = 0; (index < 4) && (index < hostNetwork.getHostIpv6s().size()); index++) {

            HostIpv6 hostIpv6 = hostNetwork.getHostIpv6s().get(index);

            //启用状态
            {
                buf.writeByte(hostIpv6.getEnabled());
            }

            //ipv6地址
            {
                String[] dataStrs = hostIpv6.getIp().split(":");
                List<Integer> datas = Arrays.stream(dataStrs).map(
                        dataStr -> Integer.valueOf(dataStr, 16)
                ).toList();
                for (int i = 0; i < 8 && i < datas.size(); i++) {
                    buf.writeShort(datas.get(i));
                }
            }

            //端口数据
            {
                buf.writeShortLE(hostIpv6.getPort());
            }

            //通信类型
            {
                buf.writeByte(hostIpv6.getCommType());
            }

            //协议类型
            {
                buf.writeByte(hostIpv6.getProtoType());
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);

    }

    @Override
    public int getOneDataSize() {
        return 4 * (9) + 4 * (21);
    }

    @Override
    public Class dataClazz() {
        return HostNetwork.class;
    }
}

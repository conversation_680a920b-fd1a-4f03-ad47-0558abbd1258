package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabAlarm;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabAlarmProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_ALARM;
    }

    @Override
    public int oneItemSize() {
        return 8 + 1 + 4;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabAlarm tabAlarm = new TabAlarm();

            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            tabAlarm.setAlarmTime(buf.readLongLE());
            tabAlarm.setAlarmType(buf.readUnsignedByte() & 0xff);
            tabAlarm.setFaultDetail(buf.readUnsignedByte() & 0xff);
            tabAlarm.setFaultDetail2(buf.readUnsignedByte() & 0xff);
            tabAlarm.setFaultDetail3(buf.readUnsignedByte() & 0xff);
            tabAlarm.setFaultDetail4(buf.readUnsignedByte() & 0xff);

            buf.release();
            return Optional.of(tabAlarm);
        }

        return Optional.empty();
    }
}

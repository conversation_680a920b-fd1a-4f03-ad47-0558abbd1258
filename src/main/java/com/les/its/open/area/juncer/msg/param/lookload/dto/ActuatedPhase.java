package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActuatedPhase  implements DataIndexAble {
    // 相位编号
    @NotNull(message = "相位编号不能为空")
    @Range(min = 1, max = 64, message = "相位编号有效范围为[1,64]")
    private Integer phaseNo;
    
    // 相位行人步行参数
    private Integer phaseWalk;
    
    // 相位行人清除参数
    private Integer phasePedestrianClear;
    
    // 相位通过时间参数
    @NotNull(message = "相位通过时间参数不能为空")
    @Range(min = 0, max = 255, message = "相位通过时间参数有效范围为[0,255]")
    private Integer phasePassage;
    
    // 相位可支初始绿参数
    @NotNull(message = "相位可支初始绿参数不能为空")
    @Range(min = 0, max = 255, message = "相位可支初始绿参数有效范围为[0,255]")
    private Integer phaseAddedInitial;
    
    // 相位最大初始绿参数
    @NotNull(message = "相位最大初始绿参数不能为空")
    @Range(min = 0, max = 255, message = "相位最大初始绿参数有效范围为[0,255]")
    private Integer phaseMaximumInitial;
    
    // 相位通过时间递减前时间参数
    @NotNull(message = "相位通过时间递减前时间参数不能为空")
    @Range(min = 0, max = 255, message = "相位通过时间递减前时间参数有效范围为[0,255]")
    private Integer phaseTimeBeforeReduction;
    
    // 相位通过时间递减的相位时间参数
    @NotNull(message = "相位通过时间递减的相位时间参数不能为空")
    @Range(min = 0, max = 255, message = "相位通过时间递减的相位时间参数有效范围为[0,255]")
    private Integer phaseTimeToReduce;
    
    // 相位车辆间隔参数
    @NotNull(message = "相位车辆间隔参数不能为空")
    @Range(min = 0, max = 255, message = "相位车辆间隔参数有效范围为[0,255]")
    private Integer phaseReduceBy;
    
    // 相位车辆最小间隔参数
    @NotNull(message = "相位车辆最小间隔参数不能为空")
    @Range(min = 0, max = 255, message = "相位车辆最小间隔参数有效范围为[0,255]")
    private Integer phaseMinimumGap;
    
    // 相位动态最大值
    @NotNull(message = "相位动态最大值不能为空")
    @Range(min = 0, max = 255, message = "相位动态最大值有效范围为[0,255]")
    private Integer phaseDynamicMaxLimit;
    
    // 相位动态最大值步长
    @NotNull(message = "相位动态最大值步长不能为空")
    @Range(min = 0, max = 255, message = "相位动态最大值步长有效范围为[0,255]")
    private Integer phaseDynamicMaxStep;
    
    // 行人相位早起的时间参数
    private Integer phasePedAdvanceWalkTime;
    
    // 行人相位延迟时间参数
    private Integer phasePedDelayTime;
    
    // 相位启动参数
    private Integer phaseStartup;
    
    // 相位操作参数
    private Integer phaseOptions;

    @Override
    public int getDataNo() {
        return phaseNo;
    }
}
package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DigitsInSet;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 紧急优先控制
 */
@Data
public class TabEmergencyPriorityCmd extends TabInBase implements NeedAck {
    
    /**
     * 类型
     * 1紧急/2优先
     */
    @NotNull(message = "类型不能为空")
    @DigitsInSet(acceptedValues = {1, 2}, message = "类型必须是1或2")
    private Integer cmdNo;
    
    /**
     * 紧急优先编号
     */
    @NotNull(message = "紧急优先编号不能为空")
    @Min(value = 1, message = "紧急优先编号不能小于1")
    @Max(value = 64, message = "紧急优先编号不能大于64")
    private Integer emergencyPriorityNo;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_EMERGENCY_PRIORITY_CMD.getCode());
    }
} 
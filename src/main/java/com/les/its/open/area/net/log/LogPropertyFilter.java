package com.les.its.open.area.net.log;


import com.alibaba.fastjson2.filter.PropertyFilter;
import com.les.its.open.config.JuncerConfigure;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * log日志打印时去除一些不必要的字段打印
 */
@Service
@Slf4j
public class LogPropertyFilter {

    @Getter
    private final PropertyFilter propertyFilter;

    @Autowired
    private JuncerConfigure juncerConfigure;

    public LogPropertyFilter(){
        propertyFilter = new PropertyFilter() {
            @Override
            public boolean apply(Object object, String name, Object value) {

                if(juncerConfigure.getDisableFields().contains(name)){
                    return false;
                }
                return true;
            }
        };
    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@NoArgsConstructor
public class ControllerTime implements DataIndexAble {

    @Range(min = -43200, max = 43200, message = "时区偏移有效范围[-43200,43200]")
    private int timeZone;

    /**
     * 校时方式
     * 0:系统
     * 1:GPS
     * 2:NTP
     * 3:PTP
     */
    @Range(min = 0, max = 3, message = "校时方式有效范围[0,3]")
    private int timeSyncType;

    /**
     *校时服务器地址类型 0 ipv4启用 1ipv6启用
     */
    @Range(min = 0, max = 1, message = "校时服务器有效范围[0,1]")
    private int timeServerType;

    @ValidIp(
            versions = {ValidIp.IpVersion.IPv4},
            message = "必须是有效的IPv4地址"
    )
    @NotNull(message = "校时服务器IPv4地址不能为空")
    private String timeServerIpv4;

    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "必须是有效的IPv6地址"
    )
    @NotNull(message = "校时服务器IPv6地址不能为空")
    private String timeServerIpv6;

    @Override
    public int getDataNo() {
        return 1;
    }
}

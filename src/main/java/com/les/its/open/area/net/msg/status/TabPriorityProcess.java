package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabPriority;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabPriorityProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_PRIORITY;
    }

    @Override
    public int oneItemSize() {
        return 3;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
           TabPriority tabPriority = new TabPriority();
           ByteBuf buf = Unpooled.buffer(body.length);
           buf.writeBytes(body);
           tabPriority.setPriorityNo(buf.readUnsignedByte() & 0xff);
           tabPriority.setApplyStatus(buf.readUnsignedByte() & 0xff);
           tabPriority.setExecuteStatus(buf.readUnsignedByte() & 0xff);
           buf.release();
           return Optional.of(tabPriority);
        }

        return Optional.empty();
    }
}

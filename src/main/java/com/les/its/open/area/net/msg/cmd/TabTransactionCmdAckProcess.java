package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabTransactionCmd;
import com.les.its.open.area.juncer.msg.cmd.TabTransactionCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Component
public class TabTransactionCmdAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_TRANSACTION_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return (2 + 2 + 64);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize() ) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabTransactionCmdAck tabTransactionCmdAck = new TabTransactionCmdAck();
            tabTransactionCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabTransactionCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabTransactionCmd tabTransactionCmd = new TabTransactionCmd();
            tabTransactionCmdAck.setTabTransactionCmd(tabTransactionCmd);
            tabTransactionCmd.setTransactionCreate(buf.readUnsignedByte() & 0xff);
            tabTransactionCmd.setTransactionTimeout(buf.readUnsignedByte() & 0xff);

            //交易事务说明
            {
                byte[] transactionNote = new byte[64];
                buf.readBytes(transactionNote);
                tabTransactionCmd.setTransactionNote(new String(transactionNote, StandardCharsets.UTF_8).trim());
            }

            buf.release();

            return Optional.of(tabTransactionCmdAck);
        }

        return Optional.empty();
    }

}

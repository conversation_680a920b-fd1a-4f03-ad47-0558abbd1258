package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabDayPlan;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabDayPlanProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_DAY_PLAN;
    }

    @Override
    public int oneItemSize() {
        return 3;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabDayPlan tabDayPlan = new TabDayPlan();
            tabDayPlan.setCrossingSeqNo((body[0] & 0xff));
            tabDayPlan.setDayPlanNo(body[1] & 0xff);
            tabDayPlan.setSegmentNo(body[2] & 0xff);
            return Optional.of(tabDayPlan);
        }

        return Optional.empty();
    }
}

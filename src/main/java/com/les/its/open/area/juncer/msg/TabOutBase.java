package com.les.its.open.area.juncer.msg;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.net.msg.MsgType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
public  class TabOutBase {

    protected String controllerId;

    protected LocalDateTime localDateTime;

    protected MsgType msgType;

    /**
     * 返回实际code，用于调试
     * @return
     */
    public int getMsgTypeCode(){
        return msgType.getCode();
    }

    /**
     * 返回实际code，用于调试
     * @return
     */
    public String getMsgTypeCodeHex(){
        return String.format("0x%08x", msgType.getCode());
    }


    //默认处理函数
    public List<TabInBase> proc(ControllerAgent controllerAgent){
        List<TabInBase> inBases = new ArrayList<>();
        return  inBases;
    }
}

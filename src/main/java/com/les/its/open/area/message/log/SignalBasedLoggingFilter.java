package com.les.its.open.area.message.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.MDC;

public class SignalBasedLoggingFilter extends Filter<ILoggingEvent> {

    @Override
    public FilterReply decide(ILoggingEvent event) {
        String signalId = MDC.get("signalId");
        if (signalId == null || signalId.isEmpty() || "unknown".equals(signalId)) {
            return FilterReply.DENY;
        }
        return FilterReply.ACCEPT;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.IncompatiblePhase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.IncompatiblePhases;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IncompatiblePhasesProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_INCOMPATIBLE_PHASE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        final int size = 2 + getOneDataSize();

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        int offset = buf.readUnsignedByte();
        int count = buf.readUnsignedByte();

        IncompatiblePhases incompatiblePhases= new IncompatiblePhases();
        List<IncompatiblePhase> incompatiblePhaseList = new ArrayList<>() ;
        incompatiblePhases.setIncompatiblePhases(incompatiblePhaseList);

        for (int i = 0; i < 64; i++) {
            IncompatiblePhase incompatiblePhase = new IncompatiblePhase();
            incompatiblePhaseList.add(incompatiblePhase);
            //相位编号
            incompatiblePhase.setPhaseNo(buf.readUnsignedByte() & 0xff);
            //冲突对立相位序列
            List<Integer> incompatiblePhaseSeq = new ArrayList<>();
            incompatiblePhase.setIncompatiblePhaseSeq(incompatiblePhaseSeq);

            long data0 =  buf.readLongLE() ;
            long data1 =  buf.readLongLE() ;

            for (int m = 0; m < 64; m++) {
                incompatiblePhaseSeq.add((int) ((( data0 >> m ) & 0x01) + (( (data1 >> m) & 0x01) << 1)));
            }

        }

        buf.release();
        List<Object> objects = new ArrayList<>();
        objects.add(incompatiblePhases);
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        //只能有一份数据，且数据类型为 TscBaseInfo
        if(objects == null || objects.size() != 1 || !(objects.get(0) instanceof IncompatiblePhases incompatiblePhases)){
            return Optional.empty();
        }

        final int size = 1 + getOneDataSize();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(0x01);

        List<IncompatiblePhase> incompatiblePhaseList = incompatiblePhases.getIncompatiblePhases();
        for (int i = 0; i < 64; i++) {
            IncompatiblePhase incompatiblePhase = incompatiblePhaseList.get(i);
            //相位编号
            buf.writeByte(incompatiblePhase.getPhaseNo());
            //冲突对立相位序列
            List<Integer> incompatiblePhaseSeq = incompatiblePhase.getIncompatiblePhaseSeq();
            if(incompatiblePhaseSeq.size() == 64) {
                long data0 = 0x00;
                long data1 = 0x00;
                for (int m = 0; m < 64; m++) {
                    data0 |=  ( (long)( incompatiblePhaseSeq.get(m) & 0x01)  << m);
                    data1 |=  ( (long)(( incompatiblePhaseSeq.get(m) >> 1) & 0x01) << m) ;
                }
                buf.writeLongLE(data0);
                buf.writeLongLE(data1);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 64 * (1 + 16);
    }

    @Override
    public Class dataClazz() {
        return IncompatiblePhases.class;
    }
}

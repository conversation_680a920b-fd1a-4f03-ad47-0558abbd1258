package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Ipv6Info {
    // IPv6地址
    @NotNull(message = "IPv6地址不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "必须是有效的IPv6地址"
    )
    private String ip;

    // 子网掩码
    @NotNull(message = "子网掩码不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "必须是有效的IPv6子网掩码"
    )
    private String mask;

    // 网关
    @NotNull(message = "网关不能为空")
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "必须是有效的IPv6网关"
    )
    private String gateway;

    /**
     * 生成默认参数数据,用于数据补全
     * @return
     */
    public static Ipv6Info genDefault(){
        Ipv6Info ipv6 = new Ipv6Info();
        ipv6.setIp("fe80:1122:3352:96ae:0000:0000:3352:96ae");
        ipv6.setMask(" fe80:1122:3352:96ae:0000:0000:3352:0000");
        ipv6.setGateway("fe80:1122:3352:96ae:0000:0000:3352:0001");
        return ipv6;
    }
}

package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabStage;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabStagProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_STAGE;
    }

    @Override
    public int oneItemSize() {
        return 7;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
           TabStage tabStage = new TabStage();
           ByteBuf buf = Unpooled.buffer(body.length);
           buf.writeBytes(body);
           tabStage.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
           tabStage.setStageNoPre(buf.readUnsignedByte() & 0xff);
           tabStage.setStageNo(buf.readUnsignedByte() & 0xff);
           tabStage.setLengthPre(buf.readUnsignedShortLE() & 0xffff);
           tabStage.setLength(buf.readUnsignedShortLE() & 0xffff);
           buf.release();
           return Optional.of(tabStage);
        }

        return Optional.empty();
    }
}

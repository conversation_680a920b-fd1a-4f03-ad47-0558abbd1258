package com.les.its.open.area.juncer.proc;


import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.bussiness.bean.ControllerNotifyMsg;
import com.les.its.open.config.GlobalConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class ControllerAgentService {

    private final Map<String, ControllerAgent> controllerAgentMap = new ConcurrentHashMap<>();

    private final ControllerService controllerService;

    public ControllerAgentService(ControllerService controllerService) {
        this.controllerService = controllerService;
    }

    /**
     * 控制内存初始化
     */
    public void init(){
        log.error("控制初始化");
        controllerService.getSignalBaseInfoMap().values().forEach(
            signalBaseInfo -> {
                //信号控制数据初始化
                ControllerAgent controllerAgent = new ControllerAgent(signalBaseInfo.getSignalId());
                controllerAgentMap.put(signalBaseInfo.getSignalId(), controllerAgent);
            }
        );
    }

    /**
     * 获取处理agent数据
     * @param controllerId
     * @return
     */
    public Optional<ControllerAgent> getControllerAgent(String controllerId){
        return Optional.ofNullable(controllerAgentMap.get(controllerId));
    }


    /**
     * 获取所有的信号机id
     * @return
     */
    public Set<String> getControllerAgentIds(){
        return controllerAgentMap.keySet();
    }


    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    @EventListener
    public void revControllerNotifyMsg(ControllerNotifyMsg controllerNotifyMsg){
        log.error("收到信号机新增通知-{}", controllerNotifyMsg);

        ControllerAgent controllerAgent = new ControllerAgent(controllerNotifyMsg.getControllerId());
        controllerAgentMap.put(controllerNotifyMsg.getControllerId(), controllerAgent);

    }

}

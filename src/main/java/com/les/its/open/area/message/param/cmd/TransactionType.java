package com.les.its.open.area.message.param.cmd;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 交易事务类型枚举
 * 定义交易事务的不同状态
 */
@Getter
public enum TransactionType {
    
    NORMAL(1, "普通模式"),
    TRANSACTION(2, "事务模式"),
    VERIFYING(3, "验证中"),
    DONE(6, "已完成");
    
    private final int code;
    private final String description;
    
    TransactionType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据代码获取对应的交易类型
     * 
     * @param code 类型代码
     * @return 交易类型枚举
     */
    public static Optional<TransactionType> fromCode(int code) {
        return Arrays.stream(TransactionType.values())
                .filter(type -> type.getCode() == code)
                .findFirst();
    }
    
    /**
     * 判断代码是否为有效的交易类型
     * 
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return fromCode(code).isPresent();
    }
    
    @Override
    public String toString() {
        return String.format("%s(%d) - %s", name(), code, description);
    }
}
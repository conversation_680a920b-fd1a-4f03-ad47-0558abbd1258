package com.les.its.open.area.message.handler.status;

import com.les.ads.ds.enums.ControlModeType;
import com.les.its.open.area.juncer.msg.status.TabControlMode;
import com.les.its.open.area.message.param.status.StatusMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.config.GlobalConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class TabControlModeHandler {

    private final ControllerService controllerService;

    private final CrossingService crossingService;

    private final StatusHandler statusHandler;

    public TabControlModeHandler(ControllerService controllerService, CrossingService crossingService,
                                StatusHandler statusHandler) {
        this.controllerService = controllerService;
        this.crossingService = crossingService;
        this.statusHandler = statusHandler;
    }

    @EventListener
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void processTabControlMode(TabControlMode tabControlMode) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabControlMode.getControllerId());

        if (signalInfoOp.isEmpty()) {
            return;
        }

    }


    /**
     * 初始化通知所有信号机离线
      */
    public void notifyAllLinkOff(){
        controllerService.getSignalBaseInfoMap().values().forEach(
            controllerBaseInfo -> {
                sendLinkOff(controllerBaseInfo.getSignalId());
            }
        );
    }


    /**
     * 信号机离线发送
     */
    @Async(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR)
    public void sendLinkOff(String controllerId){
        crossingService.getCrossingInfos(controllerId).ifPresent(crossingBaseInfos -> {
            crossingBaseInfos.forEach(crossingBaseInfo -> {
                sendTabControlMode(controllerId, crossingBaseInfo.getSubJuncNo(), ControlModeType.OFFLINE, 0);
            });
        });
    }

    /**
     * 发送路口控制方式
     * @param controllerId
     */
    public void sendTabControlMode(String controllerId, int crossingSeqNo, ControlModeType controlModeType,
                                   int character){
        //构建控制方式报文
        TabControlMode tabControlMode = new TabControlMode();
        tabControlMode.setControllerId(controllerId);
        tabControlMode.setMsgType(MsgType.TAB_CONTROL_MODE);
        tabControlMode.setLocalDateTime(LocalDateTime.now());

        //路口控制方式
        tabControlMode.setCrossingSeqNo(crossingSeqNo);
        tabControlMode.setControlModeNo(controlModeType.getCode());
        tabControlMode.setCharacter(character);

        statusHandler.sendStatus2Nats(tabControlMode.getControllerId(), StatusMqObject.TAB_CONTROL_MODE, tabControlMode);

    }


}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountdownDisplayParam implements DataIndexAble {

    //倒计时屏编号
    @NotNull(message = "{juncer.param.countdownDisplayParam.countdownDisplayNo.notNull}")
    @Range(min = 1, max = 32, message = "{juncer.param.countdownDisplayParam.countdownDisplayNo.range}")
    private Integer countdownDisplayNo;

    // 相位1
    @NotNull(message = "{juncer.param.countdownDisplayParam.phase1.notNull}")
    @Range(min = 0, max = 64, message = "{juncer.param.countdownDisplayParam.phase1.range}")
    private Integer phase1;

    // 相位2
    @NotNull(message = "{juncer.param.countdownDisplayParam.phase2.notNull}")
    @Range(min = 0, max = 64, message = "{juncer.param.countdownDisplayParam.phase2.range}")
    private Integer phase2;

    @Override
    public int getDataNo() {
        return countdownDisplayNo;
    }
}
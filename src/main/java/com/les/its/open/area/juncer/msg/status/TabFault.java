package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 故障-其他
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabFault extends TabOutBase {

    /**
     * 故障事件发生时间
     */
    private long faultTime;

    /**
     * 故障状态
     */
    private int faultStatus;
    
    /**
     * 故障动作
     */
    private int faultAction;
    
    /**
     * 故障类型
     */
    private int faultType;
    
    /**
     * 故障详情
     */
    private Integer faultDetail;

    /**
     * 故障详情
     */
    private Integer faultDetail2;


    /**
     * 故障详情
     */
    private Integer faultDetail3;

    /**
     * 故障详情
     */
    private Integer faultDetail4;

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        controllerAgent.getTabFaults().add(this);

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }

} 
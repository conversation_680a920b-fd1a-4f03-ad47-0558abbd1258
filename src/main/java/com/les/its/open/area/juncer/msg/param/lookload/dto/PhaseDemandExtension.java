package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhaseDemandExtension implements DataIndexAble {
    // 相位编号
    @NotNull(message = "相位编号不能为空")
    @Range(min = 1, max = 64, message = "相位编号有效范围为[1,64]")
    private Integer phaseNo;
    
    // 需求配置列表 (最多8个)
    @NotNull(message = "需求配置列表不能为空")
    @Size(min = 8, max = 8, message = "需求配置列表数量必须为8")
    @Valid
    private List<Demand> demands;
    
    // 延长配置列表 (最多8个)
    @NotNull(message = "延长配置列表不能为空")
    @Size(min = 8, max = 8, message = "延长配置列表数量必须为8")
    @Valid
    private List<Extension> extensions;

    /**
     * 结束手动运行期时插入相位需求
     * 0:否, 1:是
     */
    @NotNull(message = "结束手动运行期时插入相位需求不能为空")
    @Range(min = 0, max = 1, message = "结束手动运行期时插入相位需求有效范围为[0,1]")
    private Integer demandsInsertLeavingManualAndFixPhase;

    /**
     * 启动时插入相位需求
     * 0:否, 1:是
     */
    @NotNull(message = "启动时插入相位需求不能为空")
    @Range(min = 0, max = 1, message = "启动时插入相位需求有效范围为[0,1]")
    private Integer demandsInsertStartUpPhase;

    /**
     * 紧急调用结束后插入需求
     */
    @NotNull(message = "紧急调用结束后插入需求不能为空")
    @Range(min = 0, max = 1, message = "紧急调用结束后插入需求有效范围为[0,1]")
    private Integer demandsInsertLeavingHurryCall;

    /**
     * 中心控制结末后插入需求
     */
    @NotNull(message = "中心控制结末后插入需求不能为空")
    @Range(min = 0, max = 1, message = "中心控制结末后插入需求有效范围为[0,1]")
    private Integer demandsInsertLeavingSystem;

    /**
     * 无条件默认需求
     */
    @NotNull(message = "无条件默认需求不能为空")
    @Range(min = 0, max = 1, message = "无条件默认需求有效范围为[0,1]")
    private Integer unconditionalDemand;

    /**
     * 非锁定需求最大相位最大绿
     * 0:否, 1:是
     */
    @NotNull(message = "非锁定需求最大相位最大绿不能为空")
    @Range(min = 0, max = 1, message = "非锁定需求最大相位最大绿有效范围为[0,1]")
    private Integer unlatchedDemandStartMaxGreenPhase;

    /**
     * 最小绿需求
     * 0:否, 1:是
     */
    @NotNull(message = "最小绿需求不能为空")
    @Range(min = 0, max = 1, message = "最小绿需求有效范围为[0,1]")
    private Integer minGreenDemand;

    /**
     * 最大绿需求
     * 0:否, 1:是
     */
    @NotNull(message = "最大绿需求不能为空")
    @Range(min = 0, max = 1, message = "最大绿需求有效范围为[0,1]")
    private Integer maxGreenDemand;

    /**
     * 相位最大绿跟随相位需求
     * 0:无, 1-64相位编号
     */
    @NotNull(message = "相位最大绿跟随相位需求不能为空")
    @Range(min = 0, max = 64, message = "相位最大绿跟随相位需求有效范围为[0,64]")
    private Integer revertivePhaseDemand;

    @Override
    public int getDataNo() {
        return phaseNo;
    }
}
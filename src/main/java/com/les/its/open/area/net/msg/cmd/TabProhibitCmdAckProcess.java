package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabProhibitCmd;
import com.les.its.open.area.juncer.msg.cmd.TabProhibitCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabProhibitCmdAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_PROHIBIT_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 10;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabProhibitCmdAck tabProhibitCmdAck = new TabProhibitCmdAck();
            tabProhibitCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabProhibitCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabProhibitCmd tabProhibitCmd = new TabProhibitCmd();
            tabProhibitCmdAck.setTabProhibitCmd(tabProhibitCmd);
            List<Integer> prohibits = new ArrayList<>();
            tabProhibitCmd.setProhibits(prohibits);

            // 属性
            tabProhibitCmd.setCmdNo(buf.readUnsignedByte() & 0xff);
            // 命令标记
            tabProhibitCmd.setCmdTag(buf.readUnsignedByte() & 0xff);
            //标记
            long longValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                prohibits.add((int) ((longValue >> i) & 0x01));
            }
            buf.release();

            return Optional.of(tabProhibitCmdAck);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Action implements DataIndexAble {
    
    // 条件动作编号
    @NotNull(message = "{juncer.param.action.actionNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.action.actionNo.range}")
    private Integer actionNo;
    
    // 变   量类型
    @NotNull(message = "{juncer.param.action.paramType.notNull}")
    private Integer paramType;
    
    // 变量编号
    @NotNull(message = "{juncer.param.action.paramNo.notNull}")
    private Integer paramNo;
    
    // 功能编号
    @NotNull(message = "{juncer.param.action.functionNo.notNull}")
    private Integer functionNo;
    
    // 参数1
    @NotNull(message = "{juncer.param.action.param1.notNull}")
    private Integer param1;
    
    // 参数2
    @NotNull(message = "{juncer.param.action.param2.notNull}")
    private Integer param2;

    //动作名称
    @NotNull(message = "{juncer.param.action.actionName.notNull}")
    private String actionName;

    @Override
    public int getDataNo() {
        return actionNo;
    }
}
package com.les.its.open.area.net.msg;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum MsgType {

    /*链路状态类*/
    TAB_REQUEST(0x01010000, "联机请求"),
    TAB_RESPONSE(0x01010100, "联机应答", MsgSource.MSG_SRC_SYS),
    TAB_CONFIRM(0x01010200, "联机确认"),
    TAB_INQUIRE_SYSTEM(0x01020000, "系统心跳", MsgSource.MSG_SRC_SYS),
    TAB_ANSWER_SIGNAL(0x01020100, "信号机心跳应答"),
    TAB_INQUIRE_SIGNAL(0x01030000, "信号机心跳"),
    TAB_ANSWER_SYSTEM(0x01030100, "系统心跳应答", MsgSource.MSG_SRC_SYS),

    /*配置参数类*/
    TAB_LOOK(0x0201FFFF, "参数调看", MsgSource.MSG_SRC_SYS),
    TAB_LOOK_PARAM(0x0202FFFF, "参数调看应答"),
    TAB_LOOK_PARAM_ERR(0x203FFFF, "参数调看失败应答"),

    TAB_LOAD_PARAM(0x0211FFFF, "参数加载", MsgSource.MSG_SRC_SYS),
    TAB_LOAD(0x0212FFFF, "参数加载应答"),

    /*状态类*/
    //切换类
    TAB_CONTROL_MODE(0x03010100, "控制方式切换"),
    TAB_PLAN(0x03010200, "方案切换"),
    TAB_DAY_PLAN(0x03010300, "日计划切换"),
    TAB_SCHEDULE(0x03010400, "调度计划切换"),
    TAB_STAGE(0x03010500, "阶段切换"),
    TAB_EMERGENCY(0x03010600, "紧急状态切换"),
    TAB_PRIORITY(0x03010700, "优先状态切换"),
    TAB_SHIELD(0x03010800, "屏蔽状态切换"),
    TAB_PROHIBIT(0x03010900, "禁止状态切换"),
    TAB_EMERGENCY_PRIORITY_SHIELD(0x03010A00, "紧急优先屏蔽状态切换"),
    //周期状态
    TAB_CYCLE(0x03020000, "周期切换"),
    //实时类
    TAB_PHASE_STATUS(0x03030100, "实时相位/灯组状态"),
    TAB_STAGE_STATUS(0x03030200, "实时阶段状态"),
    TAB_PLAN_STATUS(0x03030300, "实时控制方式/方案/日计划/调度计划"),
    TAB_ACTUATED_PHASE_STATUS(0x03030400, "感应相位状态"),
    TAB_UI_STATUS(0x03030500, "灯电流电压状态"),
    //报警类
    TAB_ALARM(0x03040100, "报警"),
    //故障类
    TAB_FAULT(0x03050100, "故障"),
    //设备状态
    TAB_DEVICE_STATUS(0x03060100, "设备状态(电流/电压/温度)"),
    TAB_ENV(0x03060200, "环境状态"),
    TAB_DETECTOR_REALTIME(0x03070100, "交通数据-存在数据"),
    TAB_DETECTOR_STATISTICS(0x03070200, "交通数据-统计数据"),
    TAB_NOTIFY(0x03080100, "数据变更"),
    TAB_TRANSACTION(0x03090100, "事务交易控制"),

    /*控制命令*/
    TAB_CANCEL_CMD(0x04010000, "恢复本地控制", MsgSource.MSG_SRC_SYS),
    TAB_CANCEL_CMD_ACK(0x04010100, "恢复本地控制应答"),
    TAB_CMD(0x04020000, "特殊控制命令", MsgSource.MSG_SRC_SYS),
    TAB_CMD_ACK(0x04020100, "特殊控制命令应答"),
    TAB_INQUIRE(0x04030000, "状态查询", MsgSource.MSG_SRC_SYS),
    TAB_INQUIRE_ACK(0x04030100, "状态查询应答"),
    TAB_DWELL(0x04040000, "相位/灯组驻留", MsgSource.MSG_SRC_SYS),
    TAB_DWELL_ACK(0x04040100, "相位/灯组驻留应答"),
    TAB_SET_STAGE(0x04050000, "指定相位阶段控制", MsgSource.MSG_SRC_SYS),
    TAB_SET_STAGE_ACK(0x04050100, "指定相位阶段控制应答"),
    TAB_SPECIAL_MODE(0x04060000, "特殊控制方式", MsgSource.MSG_SRC_SYS),
    TAB_SPECIAL_MODE_ACK(0x04060100, "特殊控制方式应答"),
    TAB_REAL_TIME_OPTIMIZE(0x04070000, "实时优化控制", MsgSource.MSG_SRC_SYS),
    TAB_REAL_TIME_OPTIMIZE_ACK(0x04070100, "实时优化控制应答"),
    TAB_PLAN_CMD(0x04080000, "方案控制", MsgSource.MSG_SRC_SYS),
    TAB_PLAN_CMD_ACK(0x04080100, "方案控制应答"),
    TAB_DETECTOR_CONTROL_GROUP_ACTUATION(0x04090000, "检测器控制组", MsgSource.MSG_SRC_SYS),
    TAB_DETECTOR_CONTROL_GROUP_ACTUATION_ACK(0x04090100, "检测器控制组应答"),
    TAB_DETECTOR_RESET(0x040A0000, "检测器重置控制", MsgSource.MSG_SRC_SYS),
    TAB_DETECTOR_RESET_ACK(0x040A0100, "检测器重置控制组应答"),
    TAB_ACTIVATED_PHASE_CONTROL(0x040B0000, "感应需求相位控制", MsgSource.MSG_SRC_SYS),
    TAB_ACTIVATED_PHASE_CONTROL_ACK(0x040B0100, "感应需求相位控制应答"),
    TAB_ACTIVATED_PHASE_CONTROL2(0x040C0000, "感应需求相位控制", MsgSource.MSG_SRC_SYS),
    TAB_ACTIVATED_PHASE_CONTROL2_ACK(0x040C0100, "感应需求相位控制应答"),
    TAB_STAGE_CALL(0x040D0000, "阶段软件需求", MsgSource.MSG_SRC_SYS),
    TAB_STAGE_CALL_ACK(0x040D0100, "阶段软件需求应答"),
    TAB_SHIELD_CMD(0x040E0000, "屏蔽控制", MsgSource.MSG_SRC_SYS),
    TAB_SHIELD_CMD_ACK(0x040E0100, "屏蔽控制应答"),
    TAB_PROHIBIT_CMD(0x040F0000, "禁止控制", MsgSource.MSG_SRC_SYS),
    TAB_PROHIBIT_CMD_ACK(0x040F0100, "禁止控制应答"),
    TAB_EMERGENCY_PRIORITY_CMD(0x04100000, "紧急优先控制", MsgSource.MSG_SRC_SYS),
    TAB_EMERGENCY_PRIORITY_CMD_ACK(0x04100100, "紧急优先控制应答"),
    TAB_EMERGENCY_PRIORITY_DISABLE(0x04110000, "紧急优先屏蔽控制", MsgSource.MSG_SRC_SYS),
    TAB_EMERGENCY_PRIORITY_DISABLE_ACK(0x04110100, "紧急优先屏蔽控制应答"),
    TAB_TRANSACTION_CMD(0x04120000, "事务交易控制", MsgSource.MSG_SRC_SYS),
    TAB_TRANSACTION_CMD_ACK(0x04120100, "事务交易控制应答"),

    //这是模拟等待交易事务的命令,不真实下发给信号机，但是等待信号机应答
    TAB_TRANSACTION_SIM_CMD(0x04FF0100, "事务交易控制校验(模拟)", MsgSource.MSG_SRC_SYS),

    ;

    private final int code;
    private final String description;
    private final MsgSource msgSource;


    MsgType(int code, String description) {
        this.code = code;
        this.description = description;
        this.msgSource = MsgSource.MSG_SRC_SIGNAL;
    }

    MsgType(int code, String description, MsgSource msgSource) {
        this.code = code;
        this.description = description;
        this.msgSource = msgSource;
    }

    @Override
    public String toString() {
        return String.format("%s(0x%08X) - %s", name(), code, description);
    }


    /**
     * 上级系统下发时，匹配数据类型
     * @param msg1
     * @param msg2
     * @param msg3
     * @param msg4
     * @return
     */
    public static Optional<MsgType> parseMsgType(int msg1, int msg2, int msg3, int msg4){

        int msgId = ((msg1 & 0xff) << 24) + ((msg2 & 0xff) << 16) + ((msg3 & 0xff) << 8) + ( msg4 & 0xff);

        //加载调看应答特殊处理
        if((MsgType.TAB_LOOK.getCode() & 0xffff0000) == ( msgId & 0xffff0000)){
            msgId = MsgType.TAB_LOOK.getCode();
        } else if ((MsgType.TAB_LOAD_PARAM.getCode() & 0xffff0000) == ( msgId & 0xffff0000)){
            msgId = MsgType.TAB_LOAD_PARAM.getCode();
        }

        final int checkId = msgId;
        return Arrays.stream(MsgType.values()).filter(
                msgType -> msgType.getCode() == checkId
        ).findAny();
    }

}
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DefaultColor {

    /**
     * 有通行权灯色
     */
    @NotNull(message = "有通行权灯色不能为空")
    private Integer row;

    /**
     * 无通行权灯色
     */
    @NotNull(message = "无通行权灯色不能为空")
    private Integer notAtRow;

    /**
     * 待机灯色
     */
    @NotNull(message = "待机灯色不能为空")
    private Integer partTime;

}

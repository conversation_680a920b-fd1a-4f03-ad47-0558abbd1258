package com.les.its.open.area.juncer.msg.param;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabLookParam extends TabOutBase implements AckAble {

    @JsonIgnore
    @JSONField(serialize=false)
    private ParamMsgType paramMsgType;

    private int offset;
    private int count;

    private List<Object> params;


    @JsonIgnore
    @JSONField(serialize=false)
    private Class aClass;

    public long getLookId() {
        return (MsgType.TAB_LOOK.getCode() & 0xffff0000) | ( getParamMsgType().getCode());
    }

    /**
     * 用于前端数据过滤，勿要删除
     * @return
     */
    public long getOrgId() {
        return (MsgType.TAB_LOOK_PARAM.getCode() & 0xffff0000) | ( getParamMsgType().getCode());
    }

    /**
     * 用于前端数据过滤，勿要删除
     * @return
     */
    public String getOrgIdHex() {
        return String.format("0x%08x", getOrgId());
    }

    public String getParamDes() {
        return getParamMsgType().getDescription();
    }

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", getLookId());
    }
}

package com.les.its.open.area.message.dbsave;


import com.les.its.open.area.message.param.RealTimeMsgInterface;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/21 11:16
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DataSavePushBean {
    private RealTimeMsgInterface realTimeMsgInterface;
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicParam implements DataIndexAble {
    // 最大灯组数
    @NotNull(message = "最大灯组数不能为空")
    private Integer maxLightsGroupNum;
    
    // 最大相位数
    @NotNull(message = "最大相位数不能为空")
    private Integer maxPhaseNum;
    
    // 最大相位表数
    @NotNull(message = "最大相位表数不能为空")
    private Integer maxPhaseTableNum;
    
    // 最大阶段数
    @NotNull(message = "最大阶段数不能为空")
    private Integer maxStageNum;
    
    // 最大方案数
    @NotNull(message = "最大方案数不能为空")
    private Integer maxPlanNum;
    
    // 最大日计划数
    @NotNull(message = "最大日计划数不能为空")
    private Integer maxDayPlanNum;
    
    // 最大调度计划数
    @NotNull(message = "最大调度计划数不能为空")
    private Integer maxScheduleNum;
    
    // 最大检测器数
    @NotNull(message = "最大检测器数不能为空")
    private Integer maxDetectorNum;
    
    // 最大紧急数
    @NotNull(message = "最大紧急数不能为空")    
    private Integer maxEmergencyNum;
    
    // 最大优先数
    @NotNull(message = "最大优先数不能为空")
    private Integer maxPriorityNum;

    // 最大逻辑输出数
    @NotNull(message = "最大逻辑输出数不能为空")
    private Integer maxLogicOutNum;

    // 最大定时器数
    @NotNull(message = "最大定时器数不能为空")
    private Integer maxTimerNum;

    // 最大用户变量数
    @NotNull(message = "最大用户变量数不能为空")
    private Integer maxUserFlagNum;

    // 最大条件变量数
    @NotNull(message = "最大条件变量数不能为空")
    private Integer maxConditionNum;

    // 最大动作数
    @NotNull(message = "最大动作数不能为空")
    private Integer maxActionNum;

    //最大可变标记数
    @NotNull(message = "最大可变标记数不能为空")
    private Integer maxSwitchedSigns;

    @Override
    public int getDataNo() {
        return 1;
    }
}
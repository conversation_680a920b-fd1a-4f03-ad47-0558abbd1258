package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;


import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = MacAddressValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidMac {
    String message() default "无效的MAC地址格式";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    // 可选：允许指定需要验证的MAC地址格式
    MacAddressFormat[] formats() default {
            MacAddressFormat.COLON_SEPARATED,
            MacAddressFormat.HYPHEN_SEPARATED,
            MacAddressFormat.NO_SEPARATOR,
            MacAddressFormat.DOT_SEPARATED
    };

    // 可选：是否忽略大小写
    boolean ignoreCase() default true;

    // 可选：是否允许广播地址(FF:FF:FF:FF:FF:FF)
    boolean allowBroadcast() default true;

    // 可选：是否允许零地址(00:00:00:00:00:00)
    boolean allowZero() default false;

    // MAC地址格式枚举
    enum MacAddressFormat {
        COLON_SEPARATED,  // XX:XX:XX:XX:XX:XX
        HYPHEN_SEPARATED, // XX-XX-XX-XX-XX-XX
        NO_SEPARATOR,     // XXXXXXXXXXXX
        DOT_SEPARATED     // XXXX.XXXX.XXXX
    }
}
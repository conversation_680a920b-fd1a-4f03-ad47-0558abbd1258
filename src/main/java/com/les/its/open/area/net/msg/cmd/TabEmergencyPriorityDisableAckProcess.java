package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabEmergencyPriorityDisable;
import com.les.its.open.area.juncer.msg.cmd.TabEmergencyPriorityDisableAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabEmergencyPriorityDisableAckProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_EMERGENCY_PRIORITY_DISABLE_ACK;
    }

    @Override
    public int oneItemSize() {
        return 2 + 9;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabEmergencyPriorityDisableAck emergencyPriorityDisableAck = new TabEmergencyPriorityDisableAck();
            emergencyPriorityDisableAck.setAck(buf.readUnsignedByte() & 0xff);
            emergencyPriorityDisableAck.setReason(buf.readUnsignedByte() & 0xff);

            TabEmergencyPriorityDisable tabEmergencyPriorityDisable = new TabEmergencyPriorityDisable();
            emergencyPriorityDisableAck.setTabEmergencyPriorityDisable(tabEmergencyPriorityDisable);
            List<Integer> emergencyPriorities = new ArrayList<>();
            tabEmergencyPriorityDisable.setEmergencyPriorities(emergencyPriorities);

            //命令类型
            tabEmergencyPriorityDisable.setCmdNo(buf.readUnsignedByte() & 0xff);
            //相位标记
            long longValue = buf.readLongLE();
            //按bit解析数据
            for (int i = 0; i < 64; i++) {
                emergencyPriorities.add((int) ((longValue >> i) & 0x01));
            }
            buf.release();

            return Optional.of(emergencyPriorityDisableAck);
        }

        return Optional.empty();
    }
}

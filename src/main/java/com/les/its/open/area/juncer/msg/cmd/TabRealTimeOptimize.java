package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * 实时优化控制
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabRealTimeOptimize extends TabInBase implements NeedAck {
    
    /**
     * 子路口号
     */
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号范围[1,8]")
    private Integer crossingSeqNo;
    
    /**
     * 阶段号
     */
    @NotNull(message = "阶段号不能为空")
    @Range(min = 1, max = 64, message = "阶段号范围[1,64]")
    private Integer stageNo;
    
    /**
     * 阶段时长
     */
    @NotNull(message = "阶段时长不能为空")
    private Integer stageTime;

    @Override
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_REAL_TIME_OPTIMIZE.getCode());
    }
}
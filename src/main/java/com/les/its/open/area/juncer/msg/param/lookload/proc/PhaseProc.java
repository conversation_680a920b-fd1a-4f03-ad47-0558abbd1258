package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Phase;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PhaseProc  implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PHASE;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Phase phase = new Phase();
            objects.add(phase);
            //相位号
            phase.setPhaseNo(buf.readUnsignedByte() & 0xff);
            // 相位灯序编号
            phase.setLampSequenceNo(buf.readUnsignedByte() & 0xff);

            //相位关联灯组
            List<Integer> phaseLightsGroups = new ArrayList<>();
            phase.setPhaseLightsGroups(phaseLightsGroups);
            for (int m = 0; m < 8; m++) {
                int data =  buf.readUnsignedByte() & 0xff ;
                for (int t = 0; t < 8; t++) {
                    if(((data >> t) & 0x01) == 0x01){
                        phaseLightsGroups.add(0x01);
                    }else{
                        phaseLightsGroups.add(0x00);
                    }
                }
            }
            //绿灯开始方式
            phase.setAppearance(buf.readUnsignedByte() & 0xff);
            //绿灯结束方式
            phase.setTermination(buf.readUnsignedByte() & 0xff);
            // 关联相位
            phase.setAssocPhase(buf.readUnsignedByte() & 0xff);
            //相位名称
            {
                byte[] name = new byte[64];
                buf.readBytes(name);
                phase.setPhaseName(new String(name, StandardCharsets.UTF_8).trim());
            }
        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Phase)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Phase phase
                    = (Phase)objects.get(i);

            //相位号
            buf.writeByte(phase.getPhaseNo());
            // 灯序类型
            buf.writeByte(phase.getLampSequenceNo());
            //相位关联灯组
            List<Integer> phaseLightsGroups = phase.getPhaseLightsGroups();
            if(phaseLightsGroups.size() == 64) {
                for (int m = 0; m < 8; m++) {
                    int data = 0x00;
                    for (int t = 0; t < 8; t++) {
                        if ((phaseLightsGroups.get(m*8 + t)) == 0x01) {
                            data = (data | ( 0x01 << t));
                        } else {
                            data = (data | ( 0x00 << t));
                        }
                    }
                    buf.writeByte(data & 0xff);
                }
            }
            //绿灯开始方式
            buf.writeByte(phase.getAppearance());
            //绿灯结束方式
            buf.writeByte(phase.getTermination());
            //关联相位
            buf.writeByte(phase.getAssocPhase());
            //相位名称
            {
                byte[] name = new byte[64];
                byte[] bytes = phase.getPhaseName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 5 + 8 + 64;
    }

    @Override
    public Class dataClazz() {
        return Phase.class;
    }
}

package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.Emergency;
import com.les.its.open.area.juncer.msg.param.lookload.dto.ManualPanel;
import com.les.its.open.area.message.param.SgpTransAble;
import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 紧急参数
 */
@Data
public class EmergencyParam implements SgpTransAble {
    public static final String MqObjectId = "10019";

    /**
     * 紧急列表
     */
    @NotNull(message = "{basic.param.emergency.emergencies.notNull}")
    @Size(min = 1, max = 64, message = "{basic.param.emergency.emergencies.size}")
    @Valid
    private List<Emergency> emergencies;

    /**
     * 手控控制列表
    */
    @NotNull(message = "{basic.param.emergency.manualPanels.notNull}")
    @Size(min = 8, max = 8, message = "{basic.param.emergency.manualPanels.size}")
    @Valid
    private List<ManualPanel> manualPanels;

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return 1;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
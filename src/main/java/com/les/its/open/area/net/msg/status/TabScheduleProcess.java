package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabSchedule;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabScheduleProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_SCHEDULE;
    }

    @Override
    public int oneItemSize() {
        return 2;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabSchedule tabSchedule = new TabSchedule();
            tabSchedule.setCrossingSeqNo((body[0] & 0xff));
            tabSchedule.setScheduleNo(body[1] & 0xff);
            return Optional.of(tabSchedule);
        }

        return Optional.empty();
    }
}

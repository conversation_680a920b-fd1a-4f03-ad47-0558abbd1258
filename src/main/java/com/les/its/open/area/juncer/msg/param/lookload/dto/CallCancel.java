package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 手控控制
 */
@Data
public class CallCancel implements DataIndexAble {

    // 调用删除编号
    @NotNull(message = "调用删除编号不能为空")
    @Range(min = 1, max = 64, message = "调用删除编号有效范围为[1,128]")
    private Integer callCancelNo;

    //变量类型
    @NotNull(message = "变量类型不能为空")
    private Integer  paramType;

    //变量编号
    @NotNull(message = "变量编号不能为空")
    private Integer  paramNo;

    //调用延迟
    @NotNull(message = "调用延迟不能为空")
    private Integer callDelay;

    //删除延迟
    @NotNull(message = "删除延迟不能为空")
    private Integer cancelDelay;

    //需求相位
    @NotNull(message = "需求相位不能为空")
    private Integer phaseDemand;

    //调用删除名称
    @NotNull(message = "调用删除名称不能为空")
    private String callCancelName;

    @Override
    public int getDataNo() {
        return callCancelNo;
    }
}
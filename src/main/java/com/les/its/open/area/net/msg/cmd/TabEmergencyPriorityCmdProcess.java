package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.cmd.TabEmergencyPriorityCmd;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class TabEmergencyPriorityCmdProcess extends TabInnerBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_EMERGENCY_PRIORITY_CMD;
    }

    @Override
    public Optional<Class> dataClazz(int msg3, int msg4) {
        return Optional.of(TabEmergencyPriorityCmd.class);
    }

    @Override
    public Optional<byte[]> toOuter(TabInBase tabInBase, AtomicLong msgCode) {
        if(tabInBase instanceof TabEmergencyPriorityCmd tabEmergencyPriorityCmd) {
            msgCode.set(msgType().getCode());
            ByteBuf buf = Unpooled.buffer( 9);

            //控制类型
            buf.writeByte(tabEmergencyPriorityCmd.getCmdNo());
            //紧急优先编号
            buf.writeByte(tabEmergencyPriorityCmd.getEmergencyPriorityNo());

            byte[] body = new byte[buf.readableBytes()];
            buf.readBytes(body);
            buf.release();
            return Optional.of(body);
        }else {
            return Optional.empty();
        }
    }
}

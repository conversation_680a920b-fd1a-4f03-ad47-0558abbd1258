package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanStageParam {
    // 阶段编号
    @NotNull(message = "阶段编号不能为空")
    @Range(min = 1, max = 64, message = "阶段编号有效范围为[1,64]")
    private Integer stageNo;
    
    // 阶段时长
    @NotNull(message = "阶段时长不能为空")
    @Range(min = 0, max = 65535, message = "阶段时长有效范围为[0,65535]")
    private Integer stageTime;
    
    // 阶段出现类型 (1固定 2按需Hold 3按需Skip)
    @NotNull(message = "阶段出现类型不能为空")
    @Range(min = 1, max = 3, message = "阶段出现类型有效范围为[1,3]")
    private Integer stageActivationType;
    
    // 协调强制关闭 1固定(默认) 2浮动
    @NotNull(message = "协调强制关闭不能为空")
    @Range(min = 1, max = 2, message = "协调强制关闭有效范围为[1,2]")
    private Integer coordinatedForceOff;

    // 迟启早断参数
    @NotNull(message = "迟启早断参数不能为空")
    @Size(min = 64, max = 64, message = "迟启早断参数数量必须为64")
    @Valid
    private List<PlanStageParamExt> extParams;
} 
package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;

import com.les.its.open.config.LocaleProperties;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;
import java.util.Set;

/**
 * 数值约束
 *
 * @Min：验证数值是否大于或等于指定的最小值
 * @Max：验证数值是否小于或等于指定的最大值
 * @Positive：验证数值是否为正数
 * @PositiveOrZero：验证数值是否为正数或零
 * @Negative：验证数值是否为负数
 * @NegativeOrZero：验证数值是否为负数或零
 * @DecimalMin：验证数值是否大于或等于指定的字符串值
 * @DecimalMax：验证数值是否小于或等于指定的字符串值
 * @Digits：验证数值的整数位数和小数位数是否不超过指定值
 *
 * 字符串约束
 *
 * @NotBlank：验证字符串是否不为null且去除两端空格后长度大于0
 * @Email：验证字符串是否是有效的电子邮件地址
 * @Pattern：验证字符串是否匹配指定的正则表达式
 * @Size：验证字符串长度是否在指定范围内
 *
 * 集合约束
 *
 * @Size：验证集合的元素数量是否在指定范围内
 * @NotEmpty：验证集合是否不为null且包含至少一个元素
 *
 * 时间约束
 *
 * @Past：验证日期是否在当前日期之前
 * @PastOrPresent：验证日期是否在当前日期之前或等于当前日期
 * @Future：验证日期是否在当前日期之后
 * @FutureOrPresent：验证日期是否在当前日期之后或等于当前日期
 *
 * 通用约束
 *
 * @NotNull：验证对象是否不为null
 * @Null：验证对象是否为null
 * @Valid：递归验证关联对象
 */
@Slf4j
@Service
public class DataValidatorFactory {

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private LocaleProperties localeProperties;

    private final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    
    private final Validator validator = factory.getValidator();

    /**
     * 对数据项进行校验
     * @param object 待验证的对象
     * @param logStr 用于存储验证错误信息的StringBuilder
     * @return 验证是否通过
     * @param <T> 对象类型
     */
    public <T> boolean validateData(T object, StringBuilder logStr) {
        // 使用配置的默认Locale
        Locale locale = localeProperties.getDefaultLocale();
        
        // 验证对象
        Set<ConstraintViolation<T>> violations = validator.validate(object);

        // 输出所有验证错误
        for (ConstraintViolation<T> violation : violations) {
            String message = violation.getMessage();
            // 如果消息是国际化消息键（以{开头，以}结尾），则获取对应的国际化消息
            if (message.startsWith("{") && message.endsWith("}")) {
                String messageKey = message.substring(1, message.length() - 1);
                try {
                    message = messageSource.getMessage(messageKey, null, locale);
                } catch (Exception e) {
                    log.warn("Failed to resolve message for key: {}, using default message", messageKey);
                }
            }
            
            String dataStr = String.format("%s:%s", violation.getPropertyPath(), message);
            logStr.append(dataStr).append(";");
        }
        return violations.isEmpty();
    }
}

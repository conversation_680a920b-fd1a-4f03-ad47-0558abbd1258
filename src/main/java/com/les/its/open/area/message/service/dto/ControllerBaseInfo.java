package com.les.its.open.area.message.service.dto;

import lombok.Data;

import java.util.concurrent.atomic.AtomicInteger;

@Data
public class ControllerBaseInfo {
    private String signalId;
    private int noArea;
    private int noJunc;
    private String ip;
    private int port;

    private String name;
    private AtomicInteger atomicInteger = new AtomicInteger(0);

    /**
     * 前端界面显示
     */
    private boolean link;
    /**
     * 是否是模拟信号机
     */
    private boolean simu;

}

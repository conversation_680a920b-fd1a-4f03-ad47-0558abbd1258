package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LampFaultThreshold implements DataIndexAble {
    // 灯组编号
    @NotNull(message = "灯组编号不能为空")
    @Range(min = 1, max = 64, message = "灯组编号有效范围是[1,64]")
    private Integer lampNo;
    
    // 绿灯电压电流门限值
    @NotNull(message = "绿灯电压电流门限值不能为空")
    @Valid  
    private Threshold greenThreshold;
    
    // 黄灯电压电流门限值
    @NotNull(message = "黄灯电压电流门限值不能为空")
    @Valid
    private Threshold yellowThreshold;
    
    // 红灯电压电流门限值
    @NotNull(message = "红灯电压电流门限值不能为空")
    @Valid
    private Threshold redThreshold;

    @Override
    public int getDataNo() {
        return lampNo;
    }
}
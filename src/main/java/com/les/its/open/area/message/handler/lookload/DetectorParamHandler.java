package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.DetectorParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class DetectorParamHandler implements MqMsgBaseHandler {


    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public DetectorParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_DetectorParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return DetectorParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        if(datas.size() == 1){
            int detectorNo = datas.get(0);
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_DETECTOR, detectorNo,
                    1, signalInfoOp.get());
            areaMessages.add(msg);
        }else {
             {
                AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_DETECTOR, 1,
                 ParamMsgType.PARAM_DETECTOR.getMax(), signalInfoOp.get());
                areaMessages.add(msg);
            }
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());


        List<Integer> dataNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dataNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        if(dataNos.isEmpty()){
            IntStream.rangeClosed(1, ParamMsgType.PARAM_DETECTOR.getMax()).forEach(dataNos::add);
        }

        Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.Detector>> detectorsOp
                = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_DETECTOR);
        if(detectorsOp.isEmpty()){
            log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_DETECTOR);
            return Optional.empty();
        }
        log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), detectorsOp.get());


        //构建返回数据项
        dataNos.forEach(dataNo -> {
            //根据编号查找数据项
            detectorsOp.get().stream().filter(
                    detector -> detector.getDetectorNo().intValue() == dataNo
            ).findAny().ifPresent( detector ->

            //构建返回数据项
            {
                DetectorParam detectorParam = new DetectorParam();
                //设置信号机编号
                detectorParam.setSignalControllerID(requestMessage.getSignalControllerID());

                //逻辑输入编号
                detectorParam.setLogicInputNo(detector.getDetectorNo());
                //逻辑输入名称
                detectorParam.setLogicInputName(detector.getLogicInputName());
                // 安装位置
                detectorParam.setInstallLocation(detector.getInstallLocation());
                // 状态保持
                detectorParam.setState(detector.getDetectorState());
                // 需求记忆
                detectorParam.setHold(detector.getDetectorHold());
                // 持续时长
                detectorParam.setDuration(detector.getPedestrianButtonPushTime());
                // 取反记忆
                detectorParam.setInvert(detector.getDetectorInvert());
                // 配对检测器
                detectorParam.setPairedDetector(detector.getDetectorPairedDetector());
                // 配对检测器间距
                detectorParam.setPairedDetectorSpacing(detector.getDetectorPairedDetectorSpacing());
                // 行人检测
                detectorParam.setPed((detector.getDetectorOptions2() >> 4) & 0x01);
                // 流量检测
                detectorParam.setVolume(detector.getDetectorOptions() & 0x01);
                // 占有率检测
                detectorParam.setOccupancy((detector.getDetectorOptions() >> 1) & 0x01);
                // 速度检测
                detectorParam.setSpeed((detector.getDetectorOptions2()) & 0x01);
                // 排队检测
                detectorParam.setQueue((detector.getDetectorOptions() >> 6) & 0x01);
                // 数量检测
                detectorParam.setCount((detector.getDetectorOptions2() >> 5) & 0x01);
                // 身份检测
                detectorParam.setIdentity((detector.getDetectorOptions2() >> 6) & 0x01);
                // 有效时间
                detectorParam.setMaxPresence(detector.getDetectorMaxPresence());
                // 无效时间
                detectorParam.setNoActivity(detector.getDetectorNoActivity());
                // 延长时间
                detectorParam.setExtend(detector.getDetectorExtend());
                // 延迟时间
                detectorParam.setDelay(detector.getDetectorDelay());
                // 故障动作
                detectorParam.setFailOperation(detector.getDetectorFailOperation());

                datas.add(detectorParam);

            });
        });

        if(datas.isEmpty()){
            errorMsgRet.append("数据返回异常");
            return Optional.empty();
        }
        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }


        List<com.les.its.open.area.juncer.msg.param.lookload.dto.Detector> detectors
                = new ArrayList<>();

        for (int i = 0; i < datas.size(); i++) {
            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            DetectorParam detectorParam = jsonObject.toJavaObject(DetectorParam.class);
            log.error("设置参数-{}", detectorParam);


            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(detectorParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, detectorParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }


            //逻辑输入
             {
                com.les.its.open.area.juncer.msg.param.lookload.dto.Detector detector
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.Detector();
                // 检测器编号
                detector.setDetectorNo(detectorParam.getLogicInputNo());
                // 检测器类型
                detector.setDetectorType(0);
                // 流量采集周期
                detector.setVolumeCollectCycle(0);
                // 占有率采集周期
                detector.setOccupancyCollectCycle(0);
                // 安装位置
                detector.setInstallLocation(detectorParam.getInstallLocation());
                // 逻辑输入名称
                detector.setLogicInputName(detectorParam.getLogicInputName());
                // 检测器延迟参数
                detector.setDetectorDelay(detectorParam.getDelay());
                // 检测器延长参数
                detector.setDetectorExtend(detectorParam.getExtend());
                // 检测器排队限制
                detector.setDetectorQueueLimit(0);
                // 检测器输入取反标记
                detector.setDetectorInvert(detectorParam.getInvert());
                // 检测器无活动参数
                detector.setDetectorNoActivity(detectorParam.getNoActivity());
                // 检测器最长存在时间参数
                detector.setDetectorMaxPresence(detectorParam.getMaxPresence());
                // 检测器异常计数参数
                detector.setDetectorErraticCounts(0);
                // 检测器故障动作
                detector.setDetectorFailOperation(detectorParam.getFailOperation());
                // 行人按钮按下时间
                detector.setPedestrianButtonPushTime(detectorParam.getDuration());
                // 检测器配对检测器
                detector.setDetectorPairedDetector(detectorParam.getPairedDetector());
                // 检测器配对检测器间距
                detector.setDetectorPairedDetectorSpacing(detectorParam.getPairedDetectorSpacing());
                // 检测器平均车辆长度
                detector.setDetectorAvgVehicleLength(0);
                // 检测器长度参数
                detector.setDetectorLength(0);
                // 状态保持
                detector.setDetectorState(detectorParam.getState());
                //  需求记忆
                detector.setDetectorHold(detectorParam.getHold());
                // 持续时长
                detector.setDetectorDuration(detectorParam.getDuration());
                // 检测器选项参数
                {
                    int options = 0;
                    //流量检测
                    if (detectorParam.getVolume() != 0) {
                        options |= 0x01;
                    }
                    //占有率检测
                    if (detectorParam.getOccupancy() != 0) {
                        options |= 0x02;
                    }
                    //排队检测
                    if (detectorParam.getQueue() != 0) {
                        options |= (0x01 << 6);
                    }
                    detector.setDetectorOptions(options);
                }
                // 检测器选项参数2
                {
                    int options = 0;
                    //速度检测
                    if (detectorParam.getSpeed() != 0) {
                        options |= 0x01;
                    }
                    //行人检测
                    if (detectorParam.getPed() != 0) {
                        options |= (0x01 << 4);
                    }
                    //数量检测
                    if (detectorParam.getCount() != 0) {
                        options |= (0x01 << 5);
                    }
                    //身份检测
                    if (detectorParam.getIdentity() != 0) {
                        options |= (0x01 << 6);
                    }
                    detector.setDetectorOptions2(options);
                }

                detectors.add(detector);
            }

        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //生成加载参数
        AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_DETECTOR, detectors, signalInfoOp.get());
        areaMessages.add(areaMessage);

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage,
                                                    List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return false;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.message.param.cmd.TransactionType;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 事务交易状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabTransaction extends TabOutBase implements AckAble {
    /**
     * 交易事务创建
     */
    private int transactionCreate;
    
    /**
     * 交易超时时间
     */
    private int transactionTimeout;
    
    /**
     * 交易验证状态
     */
    private int transactionStatus;
    
    /**
     * 交易验证错误码
     */
    private int transactionErrorCode;

    /**
     * 交易备注
     */
    private String transactionNote;

    /**
     * 版本时间
     */
    private long versionTag;

    @Override
    public String getAckBackKey() {

        if(transactionCreate == TransactionType.DONE.getCode()){
            String orgAck = getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_TRANSACTION_CMD.getCode())
                    + "###" + transactionCreate;
            return orgAck;
        }else{
            String orgAck = getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_TRANSACTION_CMD.getCode());
            return orgAck;
        }
    }
}
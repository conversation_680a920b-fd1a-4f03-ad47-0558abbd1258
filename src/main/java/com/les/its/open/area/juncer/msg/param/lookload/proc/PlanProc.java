package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Plan;
import com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParam;
import com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParamExt;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PlanProc   implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_PLAN;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            Plan plan = new Plan();
            objects.add(plan);

            //方案编号
            plan.setPlanNo(buf.readUnsignedByte() & 0xff);
            //子路口号
            plan.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
            //相位表
            int phaseTableNo = (buf.readUnsignedByte() & 0xff);
            //周期
            plan.setCycle(buf.readUnsignedShortLE() & 0xffff);
            //协调序号
            plan.setCoordinatedStageSeq(buf.readUnsignedByte() & 0xff);
            //协调参考点 (绿灯初，黄灯初，红灯初)
            plan.setCoordinatedRef(buf.readUnsignedByte() & 0xff);
            //相位差
            plan.setOffset(buf.readUnsignedShortLE() & 0xffff);
            //阶段个数
            plan.setStageNumber(buf.readUnsignedByte() & 0xff);
            //阶段信息 [阶段序列也要检查阶段过渡规约]
            List<PlanStageParam> planStageParams = new ArrayList<>();
            plan.setPlanStageParams(planStageParams);
            for (int i = 0; i < 16 ; i++) {
                PlanStageParam planStageParam = new PlanStageParam();
                planStageParams.add(planStageParam);
                planStageParam.setStageNo(buf.readUnsignedByte() & 0xff);
                planStageParam.setStageTime(buf.readUnsignedShortLE() & 0xffff);
                planStageParam.setStageActivationType(buf.readUnsignedByte() & 0xff);
                planStageParam.setCoordinatedForceOff(buf.readUnsignedByte() & 0xff);

                List<PlanStageParamExt> extParams = new ArrayList<>();
                planStageParam.setExtParams(extParams);
                for (int j = 0; j < 64; j++) {
                    PlanStageParamExt extParam = new PlanStageParamExt();
                    extParam.setLaggingTime(buf.readUnsignedByte() & 0xff);
                    extParam.setDelayCutOffTime(buf.readUnsignedByte() & 0xff);
                    extParams.add(extParam);
                }
            }
            // 方案名称
            {
                plan.setPlanName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());
            }

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof Plan)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            Plan plan
                    = (Plan)objects.get(i);
            //方案编号
            buf.writeByte(plan.getPlanNo());
            //子路口号
            buf.writeByte(plan.getCrossingSeqNo());
            //相位表
            buf.writeByte(0x00);
            // 周期
            buf.writeShortLE(plan.getCycle());
            // 协调序号
            buf.writeByte(plan.getCoordinatedStageSeq());
            // 协调参考点 (绿灯初，黄灯初，红灯初)
            buf.writeByte(plan.getCoordinatedRef());
            // 相位差
            buf.writeShortLE(plan.getOffset());
            // 阶段个数
            buf.writeByte(plan.getStageNumber());
            List<PlanStageParam> planStageParams = plan.getPlanStageParams();
            for (int k = 0; k < 16; k++) {
                if ( k < planStageParams.size() ) {
                    PlanStageParam planStageParam = planStageParams.get(k);
                    buf.writeByte(planStageParam.getStageNo());
                    buf.writeShortLE(planStageParam.getStageTime());
                    buf.writeByte(planStageParam.getStageActivationType());
                    buf.writeByte(planStageParam.getCoordinatedForceOff());
                    List<PlanStageParamExt> extParams = planStageParam.getExtParams();
                    for (int j = 0; j < 64; j++) {
                        if(j < extParams.size()) {
                            PlanStageParamExt extParam = extParams.get(j);
                            buf.writeByte(extParam.getLaggingTime());
                            buf.writeByte(extParam.getDelayCutOffTime());
                        }else {
                            buf.writeByte(0x00);
                            buf.writeByte(0x00);
                        }
                    }
                }else {
                    buf.writeByte(0x00);
                    buf.writeShortLE(0x00);
                    buf.writeByte(0x00);
                    buf.writeByte(0x00);
                    for (int j = 0; j < 64; j++) {
                        buf.writeByte(0x00);
                        buf.writeByte(0x00);
                    }
                }
            }
            //方案名称
            {
                byte[] name = new byte[64];
                byte[] bytes = plan.getPlanName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 3 + 2 + 2 +  2 + 1 + 16 * ( 5 + 64 * 2) + 64 ;
    }

    @Override
    public Class dataClazz() {
        return Plan.class;
    }
}

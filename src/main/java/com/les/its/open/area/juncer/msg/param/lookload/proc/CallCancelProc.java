package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.CallCancel;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class CallCancelProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_CALL_CANCEL;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            CallCancel callCancel = new CallCancel();
            objects.add(callCancel);

            // 调用删除编号
            callCancel.setCallCancelNo(buf.readUnsignedByte() & 0xff);
            // 变量类型
            callCancel.setParamType(buf.readUnsignedByte() & 0xff);
            //变量编号
            callCancel.setParamNo(buf.readUnsignedByte() & 0xff);
            // 调用延迟
            callCancel.setCallDelay(buf.readUnsignedByte() & 0xff);
            // 删除延迟
            callCancel.setCancelDelay(buf.readUnsignedByte() & 0xff);
            // 需求相位
            callCancel.setPhaseDemand(buf.readUnsignedByte() & 0xff);
            // 调用删除名称
            callCancel.setCallCancelName(buf.readCharSequence(64, StandardCharsets.UTF_8).toString().trim());

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof CallCancel)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            CallCancel callCancel
                    = (CallCancel)objects.get(i);

            // 调用删除编号
            buf.writeByte(callCancel.getCallCancelNo());
            // 变量类型
            buf.writeByte(callCancel.getParamType());
            // 变量编号
            buf.writeByte(callCancel.getParamNo());
            // 调用延迟
            buf.writeByte(callCancel.getCallDelay());
            // 删除延迟
            buf.writeByte(callCancel.getCancelDelay());
            // 需求相位
            buf.writeByte(callCancel.getPhaseDemand());
            // 名称
            {
                byte[] name = new byte[64];
                byte[] bytes = callCancel.getCallCancelName().getBytes(StandardCharsets.UTF_8);
                System.arraycopy(bytes, 0, name, 0, Math.min(bytes.length, 64));
                buf.writeBytes(name);
            }
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 6 + 64;
    }

    @Override
    public Class dataClazz() {
        return CallCancel.class;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidMac;
import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NetworkConfig implements DataIndexAble {
    // 0不启用 1ipv4启用 2ipv6启用
    @Range(min = 0, max = 2, message = "是否启用的有效范围是[0,2]")
    @NotNull(message = "是否启用不能为空")
    private Integer ipEnabled;
    

    
    // IPv4信息
    @NotNull(message = "IPv4信息不能为空")
    @Valid
    private Ipv4Info ipv4;
    
    // IPv6信息
    @NotNull(message = "IPv6信息不能为空")
    @Valid
    private Ipv6Info ipv6;

    @Override
    public int getDataNo() {
        return 1;
    }
}
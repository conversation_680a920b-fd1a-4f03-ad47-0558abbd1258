package com.les.its.open.area.message.utils;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLookParam;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.mq.MqMsgOperator;
import com.les.its.open.area.message.mq.MqMsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/11 13:31
 */
@Slf4j
public class OpenLesMqUtils {

    private static AtomicLong mqSeq = new AtomicLong(0);

    private OpenLesMqUtils(){
    }

    /**
     * 构建模拟的请求数据项
     *
     * @param signalControlId
     * @param objectId
     * @return
     */
    public static MqMessage buildSimMqMsg(String signalControlId, String objectId) {
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Request.value())
                .operator(MqMsgOperator.MqMsgOperator_Query.value())
                .sequenceCode(mqSeq.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(new ArrayList<>())
                .ackMsg(false)
                .timeStampRev(System.currentTimeMillis())
                .build();
        return mqMessageResponse;
    }

    /**
     * 构建模拟的请求数据项
     *
     * @param signalControlId
     * @param objectId
     * @param objects
     * @return
     */
    public static MqMessage buildSimQueryMqMsg(String signalControlId, String objectId, List<Object> objects) {

        //将object原始对象，转换为jsonobject对象
        List<Object> objectsJson = objects.stream().map(object -> JSONObject.toJSON(object)).collect(Collectors.toList());

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Request.value())
                .operator(MqMsgOperator.MqMsgOperator_Query.value())
                .sequenceCode(mqSeq.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(objectsJson)
                .ackMsg(false)
                .build();
        return mqMessageResponse;
    }

    /**
     * 构建模拟的请求数据项
     *
     * @param signalControlId
     * @param objectId
     * @param objects
     * @return
     */
    public static MqMessage buildSimSetMqMsgWithObjects(String signalControlId, String objectId, List<Object> objects) {

        //将object原始对象，转换为jsonobject对象
        List<Object> objectsJson = objects.stream().map(object -> JSONObject.toJSON(object)).collect(Collectors.toList());

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Request.value())
                .operator(MqMsgOperator.MqMsgOperator_Set.value())
                .sequenceCode(mqSeq.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(objectsJson)
                .ackMsg(false)
                .build();
        return mqMessageResponse;
    }

    /**
     * 构建模拟的请求数据项
     *
     * @param signalControlId
     * @param objectId
     * @param objects
     * @return
     */
    public static MqMessage buildSimSetMqMsg(String signalControlId, String objectId, List<JSONObject> objects) {

        //将object原始对象，转换为jsonobject对象
        List<Object> objectsJson = new ArrayList<>(objects);

        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Request.value())
                .operator(MqMsgOperator.MqMsgOperator_Set.value())
                .sequenceCode(mqSeq.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(objectsJson)
                .ackMsg(false)
                .build();
        return mqMessageResponse;
    }


    /**
     * 构建推送报文
     *
     * @param data
     * @return
     */
    public static MqMessage buildPushMqMsg(String signalControlId, String objectId, Object data) {
        //构建数据项
        List<Object> datas = new ArrayList<>();
        datas.add(data);
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Push.value())
                .operator(MqMsgOperator.MqMsgOperator_Broadcast.value())
                .sequenceCode(mqSeq.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(datas)
                .source(0)
                .ackMsg(false)
                .timeStamp(System.currentTimeMillis())
                .build();
        return mqMessageResponse;
    }


    /**
     * 构建推送报文
     *
     * @param data
     * @return
     */
    public static MqMessage buildPushMqMsg(String signalControlId, String objectId, List<Object> data) {
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version("1.0")
                .token("1111")
                .type(MqMsgType.MqMsgType_Push.value())
                .operator(MqMsgOperator.MqMsgOperator_Broadcast.value())
                .sequenceCode(mqSeq.getAndIncrement())
                .objectId(objectId)
                .signalControllerID(signalControlId)
                .errorCode("0")
                .errorInfo("")
                .objectList(data)
                .source(0)
                .ackMsg(false)
                .timeStamp(System.currentTimeMillis())
                .build();
        return mqMessageResponse;
    }

    /**
     * 根据原始报文构建成功应答报文
     * @param requestMessage
     * @return
     */
    public static MqMessage buildSuccessMqResponseMsg(MqMessage requestMessage, List<Object> data){
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version(requestMessage.getVersion())
                .token(requestMessage.getToken())
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(requestMessage.getOperator())
                .sequenceCode(requestMessage.getSequenceCode())
                .objectId(requestMessage.getObjectId())
                .signalControllerID(requestMessage.getSignalControllerID())
                .errorCode("0")
                .errorInfo("")
                .objectList(data)
                .source(0)
                .ackMsg(true)
                .timeStamp(System.currentTimeMillis())
                .timeStampRev(requestMessage.getTimeStampRev())
                .build();
        return mqMessageResponse;
    }


    /**
     * 根据原始报文构建错误应答报文
     * @param requestMessage
     * @return
     */
    public static MqMessage buildErrorMqResponseMsg(MqMessage requestMessage, String erroCode, String errorString){
        //构建数据项
        MqMessage mqMessageResponse = MqMessage.builder()
                .version(requestMessage.getVersion())
                .token(requestMessage.getToken())
                .type(MqMsgType.MqMsgType_Response.value())
                .operator(requestMessage.getOperator())
                .sequenceCode(requestMessage.getSequenceCode())
                .objectId(requestMessage.getObjectId())
                .signalControllerID(requestMessage.getSignalControllerID())
                .errorCode(erroCode)
                .errorInfo(errorString)
                .objectList(requestMessage.getObjectList())
                .source(0)
                .ackMsg(true)
                .timeStampRev(requestMessage.getTimeStampRev())
                .build();
        return mqMessageResponse;
    }

    /**
     * 获取指定参数数据项
     * @param datas
     * @param paramMsgType
     * @return
     */
    public static <T> Optional<T> getOneData(List<JsonResult<?>> datas, ParamMsgType paramMsgType){
        Optional<JsonResult<?>> jsonResultOP = datas.stream().filter(
                jsonResult -> {
                    if (jsonResult.isSuccess()) {
                        TabLookParam tabLookParam = (TabLookParam) (jsonResult.getData());
                        if (tabLookParam.getParamMsgType() == paramMsgType) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                    return false;
                }
        ).findFirst();

        if(jsonResultOP.isPresent()) {
            TabLookParam tabLookParam = (TabLookParam) (jsonResultOP.get().getData());
            if( tabLookParam.getParams() != null && !tabLookParam.getParams().isEmpty()){
               T t = (T)(tabLookParam.getParams().get(0));
               return Optional.of(t);
            }
        }

        log.error("在返回的数据中{}没有找到参数[{}]", datas, paramMsgType.getDescription());

        return Optional.empty();
    }


    /**
     * 获取指定参数数据项
     * @param datas
     * @param paramMsgType
     * @return
     */
    public static <T> Optional<List<T>> getDatas(List<JsonResult<?>> datas, ParamMsgType paramMsgType){
        List<JsonResult<?>> jsonResults = datas.stream().filter(
                jsonResult -> {
                    if (jsonResult.isSuccess() && jsonResult.getData() instanceof TabLookParam) {
                        TabLookParam tabLookParam = (TabLookParam) (jsonResult.getData());
                        if (tabLookParam.getParamMsgType() == paramMsgType) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                    return false;
                }
        ).toList();

        if(!jsonResults.isEmpty()){
            List<T> ts = new ArrayList<>();
            jsonResults.forEach(
                    jsonResult -> {
                        TabLookParam tabLookParam = (TabLookParam) (jsonResult.getData());
                        if( tabLookParam.getParams() != null && !tabLookParam.getParams().isEmpty()){
                            List<T> t = (List<T>)(tabLookParam.getParams());
                            ts.addAll(t);
                        }
                    }
            );
            return Optional.of(ts);
        }

        log.error("在返回的数据中{}没有找到参数[{}]", datas, paramMsgType.getDescription());

        return Optional.empty();
    }

}

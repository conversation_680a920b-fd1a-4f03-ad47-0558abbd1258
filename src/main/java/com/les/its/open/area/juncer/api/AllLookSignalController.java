package com.les.its.open.area.juncer.api;

import com.les.its.open.area.juncer.msg.param.TabLookParamErr;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabLookService;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@RequestMapping("/openles/lookAll")
@Slf4j
@RestController
public class AllLookSignalController {


    @Autowired
    private TabLookService tabLookService;

    @Autowired
    private ControllerService controllerService;

    /**
     * 同步一键调看信号机参数
     * @param controllerId
     * @return
     */
    @PostMapping("/{controllerId}")
    public JsonResult<?> lookSignalController(@PathVariable String controllerId, @RequestBody List<String> exportTypes) {

        //信号机参数
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            log.error("设备{}不存在", controllerId);
            return new JsonResult<>(false, String.valueOf(ResultCode.FAILED_NO_CONTROLLER.code()),
                    ResultCode.FAILED_NO_CONTROLLER.message(), null);
        }

        //遍历信号机所有的参数,根据前端过滤条件进行参数过滤
        List<ParamMsgType> paramMsgTypes = Arrays.stream(ParamMsgType.values())
                .filter(
                        paramMsgType -> {
                            if( paramMsgType == ParamMsgType.PARAM_UNKNOWN){
                                return false;
                            }
                            return exportTypes.contains(String.format("0x%04x", paramMsgType.getCode()));
                        }

                ).sorted(
                        Comparator.comparingInt(ParamMsgType::getCode)
                ).toList();

        List<Map<String, List<Object>>> allParams = new ArrayList<>();
        AtomicInteger dataIndex  = new AtomicInteger(1);
        paramMsgTypes.forEach(
            paramMsgType -> {
                Map<String, List<Object>> eachParam = new HashMap<>();
                allParams.add(eachParam);
                List<Object> datas = new ArrayList<>();
                eachParam.put(String.format("%02d-0x%04x",dataIndex.get(), paramMsgType.getCode()) + "-" + paramMsgType.getDescription(), datas);
                dataIndex.getAndIncrement();

                final int EACH_COUNT = paramMsgType.getLookMax();
                for (int i = 0; i < paramMsgType.getMax() / EACH_COUNT; i++) {
                    log.error("开始一键调看-{}-{}-offset-{}-count-{}", controllerId, paramMsgType.getDescription(), 1 + (EACH_COUNT)*i, EACH_COUNT);
                    Optional<Object> lookedOp = tabLookService.look(controllerId, paramMsgType, 1 + (EACH_COUNT)*i, EACH_COUNT);
                    if(lookedOp.isPresent() && lookedOp.get() instanceof List<?>){
                        datas.addAll((List<Object>) lookedOp.get());
                    }else  if(lookedOp.isPresent() && lookedOp.get() instanceof TabLookParamErr){
                        datas.add(lookedOp.get());
                    } else{
                        log.error("一键调看信号机{}-参数{}失败", controllerId, paramMsgType.getDescription());
                        return;
                    }
                }
            }
        );

        return new JsonResult<>(true, "20000", "获取信号参数正常", allParams);

    }

}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Lane {
    // 车道编号
    @Range(min = 0, max = 127)
    private Integer laneNo;
    
    // 车道方向
    private Integer direction;
    
    // 车道特征
    private Integer feature;
    
    // 车道属性
    private Integer attribute;
    
    // 车道流向
    private Integer movement;
    
    // 车道方位角
    private Integer azimuth;
} 
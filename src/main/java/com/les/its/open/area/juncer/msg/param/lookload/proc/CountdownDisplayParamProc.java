package com.les.its.open.area.juncer.msg.param.lookload.proc;

import com.les.its.open.area.juncer.msg.param.lookload.LookLoadBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.CountdownDisplayParam;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.ParamMsgType;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class CountdownDisplayParamProc implements LookLoadBase {
    @Override
    public ParamMsgType msgType() {
        return ParamMsgType.PARAM_COUNT_DOWN_DISPLAY_PARAM;
    }

    @Override
    public Optional<List<Object>> procLook(JuncerMsg juncerMsg) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length < 2){
            log.error("收到{}-{}数据长度小于2", juncerMsg.getIp(), msgType().getDescription());
            return Optional.empty();
        }

        int offset = body[0] & 0xff;
        int count = body[1] & 0xff;

        final int size = 2 + getOneDataSize() * count;

        if(body.length != size){
            log.error("收到{}-{}数据长度异常,应该是-{},实际是-{}", juncerMsg.getIp(), msgType().getDescription(),
                    size, body.length);
            return Optional.empty();
        }

        ByteBuf buf = Unpooled.buffer(size);
        buf.writeBytes(body);

        //跳过offset\count
        buf.skipBytes(2);

        List<Object> objects = new ArrayList<>();

        for (int k = 0; k < count; k++) {
            CountdownDisplayParam countdownDisplayParam = new CountdownDisplayParam();
            objects.add(countdownDisplayParam);

            countdownDisplayParam.setCountdownDisplayNo(buf.readUnsignedByte() & 0xff);
            countdownDisplayParam.setPhase1(buf.readUnsignedByte() & 0xff);
            countdownDisplayParam.setPhase2(buf.readUnsignedByte() & 0xff);

        }

        buf.release();
        return Optional.of(objects);
    }

    @Override
    public Optional<byte[]> procLoad(List<Object> objects) {
        if(objects == null || objects.isEmpty()){
            return Optional.empty();
        }

        for(Object object : objects){
            if( !(object instanceof CountdownDisplayParam)){
                return Optional.empty();
            }
        }

        final int size = 1 + getOneDataSize() * objects.size();

        ByteBuf buf = Unpooled.buffer(size);
        //count数据项个数
        buf.writeByte(objects.size());

        for (int i = 0; i < objects.size(); i++) {
            CountdownDisplayParam countdownDisplayParam
                    = (CountdownDisplayParam)objects.get(i);
            buf.writeByte(countdownDisplayParam.getCountdownDisplayNo());
            buf.writeByte(countdownDisplayParam.getPhase1());
            buf.writeByte(countdownDisplayParam.getPhase2());
        }

        byte[]  data = new byte[size];
        buf.readBytes(data);
        buf.release();
        return Optional.of(data);
    }

    @Override
    public int getOneDataSize() {
        return 3;
    }

    @Override
    public Class dataClazz() {
        return CountdownDisplayParam.class;
    }
}

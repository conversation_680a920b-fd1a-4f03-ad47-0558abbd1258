package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Priority implements DataIndexAble {
    // 优先编号   
    @NotNull(message = "优先编号不能为空")
    @Range(min = 1, max = 64, message = "优先编号有效范围为[1,64]")
    private Integer priorityNo;
    
    // 优先申请相位阶段
    @NotNull(message = "优先申请相位阶段不能为空")
    @Range(min = 1, max = 64, message = "优先申请相位阶段有效范围为[1,64]")
    private Integer stageNo;
    
    // 优先申请优先级
    @NotNull(message = "优先申请优先级不能为空")
    @Range(min = 0, max = 255, message = "优先申请优先级有效范围为[0,255]")
    private Integer priority;

    
    // 延迟时间
    @NotNull(message = "延迟时间不能为空")
    @Range(min = 0, max = 255, message = "延迟时间有效范围为[0,255]")
    private Integer delayTime;
    
    // 持续时间
    @NotNull(message = "持续时间不能为空")
    @Range(min = 0, max = 255, message = "持续时间有效范围为[0,255]")
    private Integer durationTime;
    
    // 呼叫间隔时间
    @NotNull(message = "呼叫间隔时间不能为空")
    @Range(min = 0, max = 255, message = "呼叫间隔时间有效范围为[0,255]")
    private Integer callIntervalTime;

    // 请求类型
    @NotNull(message = "请求类型不能为空")
    private Integer paramTypeCall;

    // 请求编号
    @NotNull(message = "请求编号不能为空")
    private Integer paramNoCall;

    @Override
    public int getDataNo() {
        return priorityNo;
    }
}
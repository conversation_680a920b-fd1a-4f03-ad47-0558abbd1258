package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabInquire;
import com.les.its.open.area.juncer.msg.cmd.TabInquireAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabInquireAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_INQUIRE_ACK;
    }

    @Override
    public int oneItemSize() {
        return  (2 + 8);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            TabInquireAck tabCmdAck = new TabInquireAck();
            tabCmdAck.setAck(buf.readUnsignedByte() & 0xff);
            tabCmdAck.setReason(buf.readUnsignedByte() & 0xff);

            TabInquire tabInquire = new TabInquire();
            tabCmdAck.setTabInquire(tabInquire);
            tabInquire.setType(buf.readLongLE());

            buf.release();

            return Optional.of(tabCmdAck);
        }

        return Optional.empty();
    }

}

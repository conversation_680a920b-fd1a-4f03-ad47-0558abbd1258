package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhaseTableLimitGreens implements DataIndexAble {
    // 相位表编号
    @NotNull(message = "相位表编号不能为空")
    @Range(min = 1, max = 10, message = "相位表编号有效范围为[1,10]")
    private Integer phaseTableNo;

    // 相位表限制绿时列表
    @NotNull(message = "相位表限制绿时列表不能为空")
    @Size(min = 64, max = 64, message = "相位表限制绿时列表长度必须为64")
    @Valid
    private List<PhaseTableLimitGreenInfo> phaseTableLimitGreenInfoInfos;

    @Override
    public int getDataNo() {
        return phaseTableNo;
    }
}
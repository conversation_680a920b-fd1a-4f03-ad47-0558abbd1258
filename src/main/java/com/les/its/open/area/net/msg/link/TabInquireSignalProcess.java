package com.les.its.open.area.net.msg.link;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.link.TabInquireSignal;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabInquireSignalProcess extends TabOuterBaseMsgProcess {

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        TabInquireSignal tabInquireSignal = new TabInquireSignal();
       return Optional.of(tabInquireSignal);
    }

    @Override
    public MsgType msgType() {
        return MsgType.TAB_INQUIRE_SIGNAL;
    }

    @Override
    public int oneItemSize() {
        return 0;
    }
}

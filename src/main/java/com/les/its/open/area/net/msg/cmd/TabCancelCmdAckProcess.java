package com.les.its.open.area.net.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.cmd.TabCancelCmd;
import com.les.its.open.area.juncer.msg.cmd.TabCancelCmdAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class TabCancelCmdAckProcess extends TabOuterBaseMsgProcess {


    @Override
    public MsgType msgType() {
        return MsgType.TAB_CANCEL_CMD_ACK;
    }

    @Override
    public int oneItemSize() {
        return 3;
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {

        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();

        if(body.length == oneItemSize()) {
            TabCancelCmdAck tabCancelCmdAck = new TabCancelCmdAck();
            tabCancelCmdAck.setAck((body[0] & 0xff));
            tabCancelCmdAck.setReason(body[1] & 0xff);

            TabCancelCmd tabCancelCmd = new TabCancelCmd();
            tabCancelCmdAck.setTabCancelCmd(tabCancelCmd);
            tabCancelCmd.setCrossingSeqNo(body[2] & 0xff);

            return Optional.of(tabCancelCmdAck);
        }

        return Optional.empty();
    }

}

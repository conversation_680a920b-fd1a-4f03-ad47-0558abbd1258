package com.les.its.open.area.juncer.msg.link;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabInquireSignal extends TabOutBase {

    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}心跳请求", controllerAgent.getControllerId());

        //信号机当前逻辑链路状态异常，但是收到了信号机心跳数据
        if (!controllerAgent.isLogicLink()) {
            log.warn("信号机{}逻辑链路状态异常，但是收到了信号机心跳数据,设置逻辑链路上线", controllerAgent.getControllerId());
            controllerAgent.setLogicLink(true);
        }

        List<TabInBase> inBases = new ArrayList<>();
        {
            TabAnswerSystem tabAnswerSystem = new TabAnswerSystem();
            tabAnswerSystem.setControllerId(controllerAgent.getControllerId());
            tabAnswerSystem.setLocalDateTime(LocalDateTime.now());
            tabAnswerSystem.setMsgType(MsgType.TAB_ANSWER_SYSTEM);
            inBases.add(tabAnswerSystem);
        }
        return inBases;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

import org.hibernate.validator.constraints.Range;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InterGreenTime {
    // 相位编号
    @NotNull(message = "{juncer.param.interGreenTime.phaseNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.interGreenTime.phaseNo.range}")
    private Integer phaseNo;
    
    // 绿间隔时间序列 [存在中英的相位之间配置绿间隔]
    @NotNull(message = "{juncer.param.interGreenTime.interGreenTimeSeq.notNull}")
    @Size(min = 64, max = 64, message = "{juncer.param.interGreenTime.interGreenTimeSeq.size}")
    @DiscreteValuesList(min = 0, max = 30, message = "{juncer.param.interGreenTime.interGreenTimeSeq.range}")
    private List<Integer> interGreenTimeSeq;
} 
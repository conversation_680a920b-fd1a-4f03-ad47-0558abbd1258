package com.les.its.open.area.message.service.distributelock;

import com.les.its.open.task.RejectedExecutionHandlerImpl;
import io.netty.util.NettyRuntime;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

import static com.les.its.open.task.AsyncTaskConfig.corePoolSize;

@Slf4j
@Service
public class EnhancedDistributedLockService {

    private final RedissonClient redissonClient;
    private final ThreadPoolExecutor taskExecutor;

    public EnhancedDistributedLockService(RedissonClient redissonClient) {
       this.redissonClient = redissonClient;
       this.taskExecutor = (ThreadPoolExecutor) (Executors.newScheduledThreadPool(
                NettyRuntime.availableProcessors() ));
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaximumPoolSize(corePoolSize * 2);
        taskExecutor.setRejectedExecutionHandler(new RejectedExecutionHandlerImpl("DistributeLock"));
    }


    /**
     * 方案3：超时检测和处理
     *  leaseTime 保障程序异常退出后 退出
     */
    public <T> T executeWithTimeout(String lockKey, long waitTime,
                                    long maxRunTime, TimeUnit timeUnit, LockTask<T> task) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            //设置默认为任务超时时间+1s
            boolean acquired = lock.tryLock(waitTime, maxRunTime + 1, timeUnit);

            if (acquired) {
                log.debug("Successfully acquired lock: {}", lockKey);

                // 使用CompletableFuture执行任务，设置超时
                CompletableFuture<T> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return task.execute();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, taskExecutor);

                try {
                    return future.get(maxRunTime, timeUnit);
                } catch (TimeoutException e) {
                    future.cancel(true);
                    log.error("Task execution timeout for lock: {}", lockKey);
                    throw new RuntimeException("Task execution timeout for lock: "  + lockKey, e);
                }
            } else {
                log.error("Failed to acquire lock: {}", lockKey);
                throw new RuntimeException("Unable to acquire lock: " + lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for lock {}", lockKey);
            throw new RuntimeException("Thread interrupted while waiting for lock " + lockKey, e);
        } catch (ExecutionException e) {
            throw new RuntimeException("Task execution failed for lock " + lockKey, e.getCause());
        } catch (RuntimeException e) {
            throw new RuntimeException("Task execution exception for lock " + lockKey, e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("Released lock: {}", lockKey);
            }
        }
    }

    public void shutdown() {
        if (taskExecutor != null && !taskExecutor.isShutdown()) {
            taskExecutor.shutdown();
        }
    }
}
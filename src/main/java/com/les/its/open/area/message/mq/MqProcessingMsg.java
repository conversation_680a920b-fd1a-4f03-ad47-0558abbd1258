package com.les.its.open.area.message.mq;


import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.event.AckManager.InvokeFuture;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/20 10:40
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MqProcessingMsg {
    /**
     * mq请求包
     */
    private MqMessage mqMessage;

    /**
     * mq处理对象类
     */
    private MqMsgBaseHandler mqMsgBaseHandler;

    /**
     * 请求等待应答的数据项
     */
    List<InvokeFuture> invokeFutures;

    /**
     * 报文处理时间
     */
    private LocalDateTime localDateTime;

    /**
     * 判定是否已经有线程对数据进行处理
     */
    private AtomicBoolean processed;

    /**
     * 待发送的数据项
     */
    private List<AreaMessage> requestMsgs;

    /**
     * 当前数据发送的索引
     */
    private AtomicInteger currentSendIndex;

    /**
     * 用户请求状态的数据项
     */
    private long statusIndex;

    /**
     * 获取是否已经准备完毕
     * @return
     */
    public boolean isReady() {
        Optional<InvokeFuture> invokeFuture = invokeFutures.stream().filter(
                future -> !future.isDone()
        ).findAny();
        if (invokeFuture.isPresent()) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 获取所有的子数据项个数
     *
     * @return
     */
    public int getAllMsgNumber() {
        return requestMsgs.size();
    }
}

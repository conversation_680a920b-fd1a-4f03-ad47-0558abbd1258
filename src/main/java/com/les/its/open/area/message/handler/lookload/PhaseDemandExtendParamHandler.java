package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.PhaseDemandExtendParam;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class PhaseDemandExtendParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public PhaseDemandExtendParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }


    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_PhaseDemandExtendParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return PhaseDemandExtendParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看相位需求与扩展
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION, 1,
                    ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        List<Integer> dataNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dataNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        if(dataNos.isEmpty()){
            IntStream.rangeClosed(1, ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION.getMax()).forEach(dataNos::add);
        }

        //设备信息
        Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseDemandExtension>> phaseDemandExtensionsOp
                = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION);
        if(phaseDemandExtensionsOp.isEmpty()){
            log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION);
            return Optional.empty();
        }
        log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), phaseDemandExtensionsOp.get());


        //构建返回数据项
        dataNos.forEach(dataNo ->{
            //根据编号查找数据项
            phaseDemandExtensionsOp.get().stream().filter(
                    phaseTableLimitGreens -> phaseTableLimitGreens.getPhaseNo().intValue() == dataNo
            ).findAny().ifPresent(phaseDemandExtension -> {

                //转换MQ数据项
                PhaseDemandExtendParam phaseDemandExtendParam = new PhaseDemandExtendParam();
                //设置信号机编号
                phaseDemandExtendParam.setSignalControllerID(requestMessage.getSignalControllerID());

                //相位号
                phaseDemandExtendParam.setPhaseNo(phaseDemandExtension.getPhaseNo());
                //需求
                phaseDemandExtendParam.setDemands(phaseDemandExtension.getDemands());
                //延长
                phaseDemandExtendParam.setExtensions(phaseDemandExtension.getExtensions());
                // 结束手动运行期时插入相位需求
                phaseDemandExtendParam.setDemandsInsertLeavingManualAndFixPhase(
                        phaseDemandExtension.getDemandsInsertLeavingManualAndFixPhase());
                // 启动时插入相位需求
                phaseDemandExtendParam.setDemandsInsertStartUpPhase(
                        phaseDemandExtension.getDemandsInsertStartUpPhase());
                // 紧急调用结束
                phaseDemandExtendParam.setDemandsInsertLeavingHurryCall(
                        phaseDemandExtension.getDemandsInsertLeavingHurryCall());
                // 中心控制结束
                phaseDemandExtendParam.setDemandsInsertLeavingSystem(
                        phaseDemandExtension.getDemandsInsertLeavingSystem());
                // 无条件
                phaseDemandExtendParam.setUnconditionalDemand(
                        phaseDemandExtension.getUnconditionalDemand());
                // 非锁定需求启动相位最大绿
                phaseDemandExtendParam.setUnlatchedDemandStartMaxGreenPhase(
                        phaseDemandExtension.getUnlatchedDemandStartMaxGreenPhase());
                // 最小绿需求
                phaseDemandExtendParam.setMinGreenDemand(
                        phaseDemandExtension.getMinGreenDemand());
                // 最大绿需求
                phaseDemandExtendParam.setMaxGreenDemand(
                        phaseDemandExtension.getMaxGreenDemand());
                // 相位最大绿跟随相位需求
                phaseDemandExtendParam.setRevertivePhaseDemand(
                        phaseDemandExtension.getRevertivePhaseDemand());

                //应答参数
                datas.add(phaseDemandExtendParam);
            });
        });

        if(datas.isEmpty()){
            errorMsgRet.append("数据返回异常");
            return Optional.empty();
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //转换格式

        List<com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseDemandExtension> phaseDemandExtensionList
                = new ArrayList<>();

        for (int i = 0; i < datas.size(); i++) {
            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            PhaseDemandExtendParam phaseDemandExtendParam = jsonObject.toJavaObject(PhaseDemandExtendParam.class);
            log.error("设置参数-{}", phaseDemandExtendParam);

            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(phaseDemandExtendParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, phaseDemandExtendParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }

            {
                com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseDemandExtension phaseDemandExtension = new
                        com.les.its.open.area.juncer.msg.param.lookload.dto.PhaseDemandExtension();
                phaseDemandExtensionList.add(phaseDemandExtension);
                //相位号
                phaseDemandExtension.setPhaseNo(phaseDemandExtendParam.getPhaseNo());
                //需求
                phaseDemandExtension.setDemands(phaseDemandExtendParam.getDemands());
                //扩展
                phaseDemandExtension.setExtensions(phaseDemandExtendParam.getExtensions());
                // 结束手动运行期时插入相位需求
                phaseDemandExtension.setDemandsInsertLeavingManualAndFixPhase(
                        phaseDemandExtendParam.getDemandsInsertLeavingManualAndFixPhase());
                // 启动时插入相位需求
                phaseDemandExtension.setDemandsInsertStartUpPhase(
                        phaseDemandExtendParam.getDemandsInsertStartUpPhase());
                // 紧急调用结束
                phaseDemandExtension.setDemandsInsertLeavingHurryCall(
                        phaseDemandExtendParam.getDemandsInsertLeavingHurryCall());
                // 中心控制结束
                phaseDemandExtension.setDemandsInsertLeavingSystem(
                        phaseDemandExtendParam.getDemandsInsertLeavingSystem());
                // 无条件
                phaseDemandExtension.setUnconditionalDemand(
                        phaseDemandExtendParam.getUnconditionalDemand());
                // 非锁定需求启动相位最大绿
                phaseDemandExtension.setUnlatchedDemandStartMaxGreenPhase(
                        phaseDemandExtendParam.getUnlatchedDemandStartMaxGreenPhase());
                // 最小绿需求
                phaseDemandExtension.setMinGreenDemand(
                        phaseDemandExtendParam.getMinGreenDemand());
                // 最大绿需求
                phaseDemandExtension.setMaxGreenDemand(
                        phaseDemandExtendParam.getMaxGreenDemand());
                // 相位最大绿跟随相位需求
                phaseDemandExtension.setRevertivePhaseDemand(
                        phaseDemandExtendParam.getRevertivePhaseDemand());

            }

        }

        //生成加载参数
        AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_PHASE_DEMAND_EXTENSION,
                phaseDemandExtensionList, signalInfoOp.get());
        areaMessages.add(areaMessage);


        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModeTransitionConstraint implements DataIndexAble {

    // 过渡约束值数组(uint8(255))，包含多个过渡约束值
    @NotNull(message = "过渡约束值数组不能为空")
    @Size(min = 1, max = 64, message = "过渡约束值数组长度有效范围是[1,64]")
    @DiscreteValuesList(min = 1, max = 4, message = "过渡约束值数组有效范围是[1,4]")
    private List<Integer> transitionConstraintTableNos;

    @Override
    public int getDataNo() {
        return 1;
    }
}
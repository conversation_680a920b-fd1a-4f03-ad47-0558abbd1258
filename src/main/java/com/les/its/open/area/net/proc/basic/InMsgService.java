package com.les.its.open.area.net.proc.basic;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabInBases;
import com.les.its.open.area.juncer.msg.param.TabLoadParam;
import com.les.its.open.area.juncer.msg.param.TabLook;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.MsgTypeCat;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.msg.TabInnerBaseMsgProcess;
import com.les.its.open.area.net.utils.JuncerUtils;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.les.its.open.front.websocket.service.WsMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 处理从信号机获得的报文，未转化为具体报文参数
 */
@Slf4j
@Service
public class InMsgService implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private final Map<Integer, TabInnerBaseMsgProcess> tabBaseMsgMap = new ConcurrentHashMap<>();

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private WsMessageService wsMessageService;

    @Autowired
    private JuncerUtils juncerUtils;

    public void init(){
        Map<String, TabInnerBaseMsgProcess> beansOfType = applicationContext.getBeansOfType(TabInnerBaseMsgProcess.class);
        beansOfType.keySet().stream().forEach(key ->
        {
            tabBaseMsgMap.put(beansOfType.get(key).msgType().getCode(), beansOfType.get(key));
        });

        log.error("==================支持内部报文数据项==================begin");
        tabBaseMsgMap.values().stream().sorted(Comparator.comparingInt(a -> a.msgType().getCode())).forEach(
                tabInnerBaseMsgProcess -> {
                log.error("支持内部数据项-id-{},des-{}", String.format("0x%08X", tabInnerBaseMsgProcess.msgType().getCode()),
                        tabInnerBaseMsgProcess.msgType().getDescription());
            }
        );
        log.error("==================支持内部报文数据项==================end");
    }


    /**
     * 转换成外部参数数据项
     * @param tabInBase
     * @return
     */
    public boolean chg2Outer(TabInBase tabInBase){

        if(tabInBase == null){
            return false;
        }

        TabInnerBaseMsgProcess tabInnerBaseMsgProcess = tabBaseMsgMap.get(tabInBase.getMsgType().getCode());
        if(tabInnerBaseMsgProcess == null){
            log.error("内部{}数据项不支持", tabInBase.getMsgType());
            return false;
        }else {
            AtomicLong msgCode = new AtomicLong(0);

            Optional<byte[]> bodyOp = tabInnerBaseMsgProcess.toOuter(tabInBase, msgCode);
            if (bodyOp.isEmpty()) {
                log.error("转换外部报文转换失败{}", tabInBase);
                return false;
            } else {
                //设置基础参数
                Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(tabInBase.getControllerId());
                if (signalInfoOp.isEmpty()) {
                    log.error("设备{}不存在", tabInBase.getControllerId());
                    return false;
                }

                //构建外部数据项
                String ip = signalInfoOp.get().getIp();
                tabInBase.setIp(ip);
                tabInBase.setMsgTypeCode(msgCode.get());
                tabInBase.setNoArea(signalInfoOp.get().getNoArea());
                tabInBase.setNoJunc(signalInfoOp.get().getNoJunc());
                tabInBase.setData(bodyOp.get());

                //发送数据项
                wsMessageService.sendMsg(tabInBase.getControllerId(), tabInBase);

                return true;
            }
        }
    }


    @EventListener
    @Async(GlobalConfigure.JUNCER_ASYC_EXECUTOR)
    public void processJuncerMsg(TabInBases tabInBases) {

        if(tabInBases== null
                || tabInBases.getTabInBases() == null
                ||  tabInBases.getTabInBases().isEmpty()){
            return;
        }

        tabInBases.getTabInBases().forEach(tabInBase ->
        {
            //是否正常转换到外部数据项
            if(chg2Outer(tabInBase)) {
                //发送数据项
                juncerUtils.send2Controller(tabInBase.getIp(), tabInBase, false, false);
            }
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 处理mq参数数据项
     * @param areaMessage
     */
    public Optional<InvokeFuture> procMqMessage(AreaMessage areaMessage){

        Optional<MsgType> msgTypeOp =
                MsgType.parseMsgType(areaMessage.getMsg1(), areaMessage.getMsg2(), areaMessage.getMsg3(), areaMessage.getMsg4());

        if(msgTypeOp.isEmpty()){
            log.error("不支持的MQ消息-{}", areaMessage);
            return Optional.empty();
        }

        if(areaMessage.getData() == null){
            log.error("MQ数据项{}内部为空", areaMessage);
            return Optional.empty();
        }

        TabInnerBaseMsgProcess tabInnerBaseMsgProcess = tabBaseMsgMap.get(msgTypeOp.get().getCode());
        if(tabInnerBaseMsgProcess == null){
            log.error("MQ收到内部{}数据项不支持1", areaMessage);
            return Optional.empty();
        }

        Optional<Class> aClassOp = tabInnerBaseMsgProcess.dataClazz(areaMessage.getMsg3(), areaMessage.getMsg4());
        if(aClassOp.isEmpty()){
            log.error("MQ收到内部{}数据项不支持2", areaMessage);
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(areaMessage.getNoArea(), areaMessage.getNoJunc());
        if(signalInfoOp.isEmpty()) {
            log.error("没有找到信号机2-{}", areaMessage);
            return Optional.empty();
        }

        //反序列化内部参数数据项
        Optional<TabInBase> toSend = Optional.empty();
        {

            //控制命令类
            if (MsgTypeCat.CAT_CMD.getCode() == areaMessage.getMsg1()) {

                Object object = JSONObject.parseObject(JSONObject.toJSONString(areaMessage.getData()), aClassOp.get());
                if (object instanceof TabInBase tabInBase) {
                    //设置基础参数
                    tabInBase.setControllerId(signalInfoOp.get().getSignalId());
                    tabInBase.setMsgType(msgTypeOp.get());

                    toSend = Optional.of(tabInBase);
                } else {
                    log.error("MQ数据项序列化失败1{}", areaMessage);
                }
            } else if (MsgTypeCat.CAT_PARAM.getCode() == areaMessage.getMsg1()) {
                int paraId = (areaMessage.getMsg3() << 8) + areaMessage.getMsg4();
                ParamMsgType paramMsgType = ParamMsgType.fromCode(paraId);

                if (ParamMsgType.PARAM_UNKNOWN != paramMsgType) {
                    //加载参数时可能是列表数据对象
                    if(areaMessage.getData() instanceof List<?>)
                    {
                        List datas = JSONArray.parseArray(JSONObject.toJSONString(areaMessage.getData()), aClassOp.get());
                        //加载参数
                        TabLoadParam tabLoadParam = new TabLoadParam();
                        tabLoadParam.setControllerId(signalInfoOp.get().getSignalId());
                        tabLoadParam.setMsgType(MsgType.TAB_LOAD_PARAM);
                        List<Object> objects = new ArrayList<>();
                        {
                            objects.addAll(datas);
                        }
                        tabLoadParam.setObjects(objects);
                        tabLoadParam.setParamMsgType(paramMsgType);
                        toSend = Optional.of(tabLoadParam);

                    }else {
                        Object object = JSONObject.parseObject(JSONObject.toJSONString(areaMessage.getData()), aClassOp.get());
                        //调看参数
                        if (object instanceof TabLook tabLook) {
                            tabLook.setControllerId(signalInfoOp.get().getSignalId());
                            tabLook.setMsgType(MsgType.TAB_LOOK);
                            tabLook.setParamMsgType(paramMsgType);
                            toSend = Optional.of(tabLook);
                        } else {
                            //加载参数
                            TabLoadParam tabLoadParam = new TabLoadParam();
                            tabLoadParam.setControllerId(signalInfoOp.get().getSignalId());
                            tabLoadParam.setMsgType(MsgType.TAB_LOAD_PARAM);
                            List<Object> objects = new ArrayList<>();
                            {
                                objects.add(object);
                            }
                            tabLoadParam.setObjects(objects);
                            tabLoadParam.setParamMsgType(paramMsgType);
                            toSend = Optional.of(tabLoadParam);
                        }
                    }
                } else {
                    log.error("MQ配置参数子类型异常{}", areaMessage);
                }
            }
        }

        if(toSend.isPresent()){
            //是否正常转换到外部数据项
            if(chg2Outer(toSend.get())) {
                //发送数据项
              return  juncerUtils.send2Controller(toSend.get().getIp(), toSend.get(),
                      areaMessage.isSimSend(), areaMessage.isAckDirect());
            }
        }

        return Optional.empty();
    }

}

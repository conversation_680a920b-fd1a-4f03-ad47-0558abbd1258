package com.les.its.open.area.message.service.ratelimit;

import com.les.its.open.utils.IPUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.Duration;

/**
 * 流控切面
 */
@Aspect
@Component
@Slf4j
public class RateLimitAspect {

    @Autowired
    private DistributedRateLimitService rateLimitService;

    @Autowired
    private SpelExpressionParser spelParser;

    /**
     * 针对 限流 注解的切面处理
     * @param joinPoint
     * @param rateLimit
     * @return
     * @throws Throwable
     */
    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        String key = buildKey(joinPoint, rateLimit);
        RateLimitConfig config = new RateLimitConfig(
                rateLimit.capacity(),
                rateLimit.tokens(),
                Duration.of(rateLimit.period(), rateLimit.timeUnit().toChronoUnit())
        );

        if (rateLimitService.tryConsume(key, config)) {
            log.debug("Rate limit passed for key: {}", key);
            return joinPoint.proceed();
        } else {
            log.warn("Rate limit exceeded for key: {}", key);
            throw new RateLimitExceededException(rateLimit.message());
        }
    }

    /**
     * 构建key
     * @param joinPoint
     * @param rateLimit
     * @return
     */
    private String buildKey(ProceedingJoinPoint joinPoint, RateLimit rateLimit) {
        String prefix = buildPrefix(rateLimit.type());

        if (rateLimit.type() == RateLimitType.CUSTOM && !rateLimit.key().isEmpty()) {
            return prefix + ":" + parseSpEL(rateLimit.key(), joinPoint);
        }

        HttpServletRequest request = getCurrentRequest();
        return switch (rateLimit.type()) {
            case USER -> prefix + ":" + getCurrentUserId(request);
            case IP -> prefix + ":" + getClientIp(request);
            case API -> prefix + ":" + joinPoint.getSignature().toShortString();
            case CUSTOM -> prefix + ":" + parseSpEL(rateLimit.key(), joinPoint);
        };
    }

    /**
     * 构建key 前缀数据项
     * @param type
     * @return
     */
    private String buildPrefix(RateLimitType type) {
        return "rate_limit:" + type.name().toLowerCase();
    }

    /**
     * spEL 构建参数
     * @param expression
     * @param joinPoint
     * @return
     */
    private String parseSpEL(String expression, ProceedingJoinPoint joinPoint) {
        if (!expression.startsWith("#{")) {
            return expression;
        }

        Expression exp = spelParser.parseExpression(expression);
        EvaluationContext context = new StandardEvaluationContext();

        // 设置方法参数
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();

        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        return exp.getValue(context, String.class);
    }

    private HttpServletRequest getCurrentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
    }

    private String getCurrentUserId(HttpServletRequest request) {
        // 从JWT或Session中获取用户ID
        String token = request.getHeader("Authorization");
        return extractUserIdFromToken(token);
    }

    private String getClientIp(HttpServletRequest request) {
        return IPUtils.getHostIp(request);
    }

    private String extractUserIdFromToken(String token) {
        // JWT解析逻辑
        if (token != null && token.startsWith("Bearer ")) {
            // 解析JWT获取用户ID
            return "user_from_jwt";
        }
        return "anonymous";
    }
}

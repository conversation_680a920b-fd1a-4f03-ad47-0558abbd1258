package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Stage implements DataIndexAble {
    // 相位阶段编号
    @NotNull(message = "相位阶段编号不能为空")
    @Range(min = 1, max = 64, message = "相位阶段编号有效范围为[1,64]")
    private Integer stageNo;
    
    // 阶段相位参数 (最多64个相位参数)
    @NotNull(message = "阶段相位参数不能为空")
    @Size(min = 64, max = 64, message = "阶段相位参数数量必须为64")
    @Valid
    private List<StagePhase> stagePhases;

    // 阶段名称
    @NotNull(message = "阶段名称不能为空")
    private String stageName;

    @Override
    public int getDataNo() {
        return stageNo;
    }
}
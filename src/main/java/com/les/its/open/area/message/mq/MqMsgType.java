package com.les.its.open.area.message.mq;

import java.util.Arrays;
import java.util.Optional;

public enum MqMsgType {
    MqMsgType_Request(0x01, "请求"),
    MqMsgType_Response(0x02, "应答"),
    MqMsgType_Push(0x03, "推送");

    private int value;
    private String des;

    MqMsgType(int value, String des){
        this.value = value;
        this.des = des;
    }

    public int value(){
        return this.value;
    }

    public String des(){
        return this.des;
    }

    public static Optional<MqMsgType> getType(int orgValue) {
        Optional<MqMsgType> messageTypeOptional = Arrays.asList(MqMsgType.values()).stream()
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();

        return messageTypeOptional;
    }
}

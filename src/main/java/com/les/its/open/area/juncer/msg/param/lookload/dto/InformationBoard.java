package com.les.its.open.area.juncer.msg.param.lookload.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class InformationBoard {
    // 是否启用
    @Range(min = 0, max = 1, message = "{juncer.param.informationBoard.enabled.range}")
    private int enabled;

    // 信息板序列号
    @NotNull(message = "{juncer.param.informationBoard.serialNumber.notNull}")
    private String serialNumber;
}

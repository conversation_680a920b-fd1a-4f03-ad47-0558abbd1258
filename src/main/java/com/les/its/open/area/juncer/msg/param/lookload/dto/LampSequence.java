package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LampSequence implements DataIndexAble {

    // 相位灯序编号
    @NotNull(message = "相位灯序编号不能为空")
    @Range(min = 1, max = 64, message = "相位灯序编号有效范围为[1,64]")
    private Integer lampSequenceNo;

    /**
     *  相位灯序类型
     *  0:机动车
     *  1:行人
     *  2:非机动车
     *  3:绿箭头
     */
    @NotNull(message = "相位灯序类型不能为空")
    @Range(min = 0, max = 3, message = "相位灯序类型有效范围为[0,3]")
    private Integer sequenceType;

    /**
     * 相位灯序名称
     */
    @NotNull(message = "相位灯序名称不能为空")
    private String sequenceName;

    /**
     * 常规灯色
     */
    @NotNull(message = "常规灯色不能为空")
    @Valid
    private DefaultColor defaultColor;

    // 失去路权配置
    @NotNull(message = "失去路权配置不能为空")
    @Size(min = 4, max = 4, message = "失去路权配置数量必须为4")
    @Valid
    private List<LightTransition> loseLightTransitions;

    // 获得路权配置
    @NotNull(message = "获得路权配置不能为空")
    @Size(min = 4, max = 4, message = "获得路权配置数量必须为4")
    @Valid
    private List<LightTransition> obtainLightTransitions;


    // 开机失去路权配置
    @NotNull(message = "开机失去路权配置不能为空")
    @Size(min = 4, max = 4, message = "开机失去路权配置数量必须为4")
    @Valid
    private List<LightTransition> loseLightTransitionStartups;
    
    // 开机获得路权配置 
    @NotNull(message = "开机获得路权配置不能为空")
    @Size(min = 4, max = 4, message = "开机获得路权配置数量必须为4")
    @Valid
    private List<LightTransition> obtainLightTransitionStartups;
    
    // 正常到待机失去路权配置
    @NotNull(message = "正常到待机失去路权配置不能为空")
    @Size(min = 4, max = 4, message = "正常到待机失去路权配置数量必须为4")
    @Valid
    private List<LightTransition> loseLightTransitionYellowFlashs;
    
    // 正常到待机获得路权配置
    @NotNull(message = "正常到待机获得路权配置不能为空")
    @Size(min = 4, max = 4, message = "正常到待机获得路权配置数量必须为4")
    @Valid  
    private List<LightTransition> obtainLightTransitionYellowFlashs;
    
    // 待机到正常失去路权配置
    @NotNull(message = "待机到正常失去路权配置不能为空")
    @Size(min = 4, max = 4, message = "待机到正常失去路权配置数量必须为4")
    @Valid  
    private List<LightTransition> loseLightTransitionYellowFlashToNormals;
    
    // 待机到正常获得路权配置
    @NotNull(message = "待机到正常获得路权配置不能为空")
    @Size(min = 4, max = 4, message = "待机到正常获得路权配置数量必须为4")
    @Valid    
    private List<LightTransition> obtainLightTransitionYellowFlashToNormals;

    @Override
    public int getDataNo() {
        return lampSequenceNo;
    }
}
package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class DeviceInfo implements DataIndexAble {
    // 制造厂家
    @NotNull(message = "制造厂家不能为空")
    private String manufacturer;

    // 设备版本
    @NotNull(message = "设备版本不能为空")
    private String deviceVersion;

    // 软件版本
    @NotNull(message = "软件版本不能为空")
    private String softVersion;

    // 设备编号
    @NotNull(message = "设备编号不能为空")
    private String deviceNo;

    // 出厂日期
    @NotNull(message = "出厂日期不能为空")
    private LocalDateTime productionDate;

    // 配置日期
    @NotNull(message = "配置日期不能为空")  
    private LocalDateTime configurationDate;

    // 信号机机型
    @NotNull(message = "信号机机型不能为空")
    private String model;

    // 授权码
    @NotNull(message = "授权码不能为空")
    private String authCode;

    @Override
    public int getDataNo() {
        return 1;
    }
}


package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.ValidIp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HostIpv6 {
    // 是否启用
    private int enabled;
    
    // 地址
    @ValidIp(
            versions = {ValidIp.IpVersion.IPv6},
            message = "必须是有效的IPv6地址"
    )
    private String ip;
    
    // 通信端口
    @Range(min = 0, max = 65535)
    private Integer port;
    
    // 通信类型
    @Range(min = 1, max = 3)
    private Integer commType;
    
    // 协议类型
    @Range(min = 1, max = 5)
    private Integer protoType;

    /**
     * 生成默认的ipv6地址
     * @return
     */
    public static HostIpv6 genDefaultHostIpv6(){
        HostIpv6 hostIp = new HostIpv6();
        hostIp.setEnabled(0);
        hostIp.setIp("fe80:1122:3352:96ae:0000:0000:3352:96ae");
        hostIp.setPort(7301);
        hostIp.setCommType(1);
        hostIp.setProtoType(1);
        return hostIp;
    }
} 
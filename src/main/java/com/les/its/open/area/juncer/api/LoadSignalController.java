package com.les.its.open.area.juncer.api;

import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabLoadService;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RequestMapping("/openles/load")
@Slf4j
@RestController
public class LoadSignalController {


    @Autowired
    private TabLoadService tabLoadService;

    /**
     * 同步加载信号机参数
     * @param controllerId
     * @param paramMsgType
     * @param data
     * @return
     */
    @PostMapping("/{controllerId}/{paramMsgType}")
    public JsonResult<?> loadSignalController(@PathVariable String controllerId,
                                              @PathVariable String paramMsgType,
                                              @RequestBody String data) {

        ParamMsgType paramMsgType1 = ParamMsgType.fromCode(Integer.parseInt(paramMsgType, 16));
        if(paramMsgType1 == ParamMsgType.PARAM_UNKNOWN){
            return JsonResult.error(ResultCode.NOT_SUPPORT_LOOK_TYPE.message());
        }

        Optional<Object> loadOp = tabLoadService.load(controllerId, paramMsgType1, data);
        if(loadOp.isPresent()){
            return new JsonResult<>(true, "20000", "获取信号参数正常", loadOp.get());
        }else{
            return JsonResult.error(ResultCode.FAILED_LOAD.message());
        }
    }

}

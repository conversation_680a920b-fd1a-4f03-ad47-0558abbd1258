package com.les.its.open.area.juncer.msg.param.lookload.dto.valid;


import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class MacAddressValidator implements ConstraintValidator<ValidMac, String> {

    private static final String COLON_PATTERN = "^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$";
    private static final String HYPHEN_PATTERN = "^([0-9A-Fa-f]{2}[-]){5}([0-9A-Fa-f]{2})$";
    private static final String NO_SEP_PATTERN = "^([0-9A-Fa-f]{12})$";
    private static final String DOT_PATTERN = "^([0-9A-Fa-f]{4}[.]){2}([0-9A-Fa-f]{4})$";

    private static final String BROADCAST_MAC = "FFFFFFFFFFFF";
    private static final String ZERO_MAC = "000000000000";

    private ValidMac.MacAddressFormat[] formats;
    private boolean ignoreCase;
    private boolean allowBroadcast;
    private boolean allowZero;

    @Override
    public void initialize(ValidMac constraintAnnotation) {
        this.formats = constraintAnnotation.formats();
        this.ignoreCase = constraintAnnotation.ignoreCase();
        this.allowBroadcast = constraintAnnotation.allowBroadcast();
        this.allowZero = constraintAnnotation.allowZero();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // null值由@NotNull处理
        }

        // 检查格式
        boolean formatValid = false;

        for (ValidMac.MacAddressFormat format : formats) {
            Pattern pattern = getPatternForFormat(format);
            if (pattern.matcher(value).matches()) {
                formatValid = true;
                break;
            }
        }

        if (!formatValid) {
            return false;
        }

        // 清除分隔符，便于进一步验证
        String cleanMac = value.replaceAll("[.:-]", "");

        // 转换为大写（如果需要）
        if (ignoreCase) {
            cleanMac = cleanMac.toUpperCase();
        }

        // 检查是否为广播地址
        if (!allowBroadcast && BROADCAST_MAC.equals(cleanMac.toUpperCase())) {
            return false;
        }

        // 检查是否为零地址
        if (!allowZero && ZERO_MAC.equals(cleanMac)) {
            return false;
        }

        return true;
    }

    private Pattern getPatternForFormat(ValidMac.MacAddressFormat format) {
        switch (format) {
            case COLON_SEPARATED:
                return Pattern.compile(COLON_PATTERN);
            case HYPHEN_SEPARATED:
                return Pattern.compile(HYPHEN_PATTERN);
            case NO_SEPARATOR:
                return Pattern.compile(NO_SEP_PATTERN);
            case DOT_SEPARATED:
                return Pattern.compile(DOT_PATTERN);
            default:
                throw new IllegalArgumentException("Unsupported MAC address format");
        }
    }
}
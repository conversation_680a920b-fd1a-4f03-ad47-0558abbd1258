package com.les.its.open.area.net.proc;

import com.les.its.open.area.juncer.msg.cmd.TabCancelCmd;
import com.les.its.open.area.juncer.msg.cmd.TabCancelCmdAck;
import com.les.its.open.area.juncer.msg.cmd.TabSetStage;
import com.les.its.open.area.juncer.msg.cmd.TabSetStageAck;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.area.net.utils.JuncerUtils;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class TabCmdService {

    @Autowired
    private InMsgService inMsgService;

    @Autowired
    private JuncerUtils juncerUtils;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private ControllerService controllerService;


    /**
     * 同步等待数据项应答，会阻塞所在线程，请特别注意
     * @param controllerId
     * @param crossingNo
     * @return
     */
    public Optional<Object> cancel(String controllerId, int crossingNo) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(controllerId, crossingNo);
        if(crossingInfoOp.isEmpty()){
            log.error("无法找到信号机关联路口-{}-{}", controllerId, crossingNo);
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if(signalInfoOp.isEmpty()){
            log.error("无法找到路口-{}", signalInfoOp.get().getSignalId());
            return Optional.empty();
        }

        TabCancelCmd tabCancelCmd = new TabCancelCmd();
        tabCancelCmd.setControllerId(crossingInfoOp.get().getControllerId());
        tabCancelCmd.setMsgType(MsgType.TAB_CANCEL_CMD);

        tabCancelCmd.setCrossingSeqNo(crossingInfoOp.get().getSubJuncNo());

        boolean chg2Outer = inMsgService.chg2Outer(tabCancelCmd);
        if(chg2Outer)
        {
            Optional<Object> op = juncerUtils.send2ControllerAndWaitForAck(signalInfoOp.get().getIp(), tabCancelCmd, false, false);
            if(op.isPresent()){
                if( op.get() instanceof TabCancelCmdAck ack){
                    return Optional.of(ack);
                }
            }
        }

        return Optional.empty();
    }


    /**
     * 同步等待数据项应答，会阻塞所在线程，请特别注意
     * @param setStageData
     * @return
     */
    public Optional<Object> setStage(String crossingId, TabSetStage setStageData) {

        Optional<CrossingService.CrossingBaseInfo> crossingInfoOp = crossingService.getCrossingInfo(crossingId);
        if(crossingInfoOp.isEmpty()){
            log.error("无法找到路口-{}", crossingId);
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(crossingInfoOp.get().getControllerId());
        if(signalInfoOp.isEmpty()){
            log.error("无法找到路口-{}", signalInfoOp.get().getSignalId());
            return Optional.empty();
        }

        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setControllerId(crossingInfoOp.get().getControllerId());
        tabSetStage.setMsgType(MsgType.TAB_SET_STAGE);

        tabSetStage.setCrossingSeqNo(crossingInfoOp.get().getSubJuncNo());
        tabSetStage.setCharacter(setStageData.getCharacter() != null ? setStageData.getCharacter() : 4);
        tabSetStage.setStageNo(setStageData.getStageNo());
        tabSetStage.setStageSeq(setStageData.getStageSeq());
        tabSetStage.setDuration(setStageData.getDuration());
        tabSetStage.setExtParams(setStageData.getExtParams());

        boolean chg2Outer = inMsgService.chg2Outer(tabSetStage);
        if(chg2Outer)
        {
            Optional<Object> op = juncerUtils.send2ControllerAndWaitForAck(signalInfoOp.get().getIp(), tabSetStage, false, false);
            if(op.isPresent()){
                if( op.get() instanceof TabSetStageAck ack){
                    return Optional.of(ack);
                }
            }
        }

        return Optional.empty();
    }


}

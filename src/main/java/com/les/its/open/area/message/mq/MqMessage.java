package com.les.its.open.area.message.mq;

import com.les.its.open.event.AckManager.AckAble;
import com.les.its.open.event.AckManager.NeedAck;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/19 16:04
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MqMessage implements NeedAck, AckAble {
    private String version;
    private String token;
    private int type;
    private int operator;
    private long sequenceCode;
    private int source;
    private long timeStamp;
    private String objectId;
    private String signalControllerID;
    private String errorCode;
    private String errorInfo;
    private List<Object> objectList;

    /**
     * 标记ack消息应答
     */
    private boolean ackMsg;

    /**
     * 收到数据数据tag
     */
    private long timeStampRev;

    public long getMapKey() {
        return sequenceCode + (timeStampRev << 8);
    }

    @Override
    public String getAckBackKey() {
        return String.valueOf(getMapKey());
    }

    @Override
    public String getAckKey() {
        return getAckBackKey();
    }
}

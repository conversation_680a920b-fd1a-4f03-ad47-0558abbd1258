package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabFault;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabFaultProcess  extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_FAULT;
    }

    @Override
    public int oneItemSize() {
        return (3 + 8 + 4);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            TabFault tabFault = new TabFault();
            ByteBuf buf = Unpooled.buffer(body.length);
            buf.writeBytes(body);

            // 故障状态
            tabFault.setFaultStatus(buf.readUnsignedByte() & 0xff);
            // 故障动作
            tabFault.setFaultAction(buf.readUnsignedByte() & 0xff);
            // 故障事件发生时间
            tabFault.setFaultTime(buf.readLongLE());
            // 灯故障类型
            tabFault.setFaultType(buf.readUnsignedByte() & 0xff);
            // 故障详情
            tabFault.setFaultDetail(buf.readUnsignedByte() & 0xff);
            tabFault.setFaultDetail2(buf.readUnsignedByte() & 0xff);
            tabFault.setFaultDetail3(buf.readUnsignedByte() & 0xff);
            tabFault.setFaultDetail4(buf.readUnsignedByte() & 0xff);

            buf.release();
            return Optional.of(tabFault);
        }

        return Optional.empty();
    }
}

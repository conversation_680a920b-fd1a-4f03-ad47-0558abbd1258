package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.utils.CurrentValueConverter;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Threshold {
    // 电压阈值上限
    @NotNull(message = "{juncer.param.threshold.voltageUpper.notNull}")
    @Range(min = 0, max = 510, message = "{juncer.param.threshold.voltageUpper.range}")
    private Integer voltageUpper;
    
    // 电压阈值下限
    @NotNull(message = "{juncer.param.threshold.voltageLower.notNull}")
    @Range(min = 0, max = 510, message = "{juncer.param.threshold.voltageLower.range}")
    private Integer voltageLower;
    
    // 电流阈值上限
    @NotNull(message = "{juncer.param.threshold.currentUpper.notNull}")
    @Range(min = 0, max = 3000, message = "{juncer.param.threshold.currentUpper.range}")
    private Integer currentUpper;
    
    // 电流阈值下限
    @NotNull(message = "{juncer.param.threshold.currentLower.notNull}")
    @Range(min = 0, max = 3000, message = "{juncer.param.threshold.currentLower.range}")
    private Integer currentLower;

    @AssertTrue(message = "{juncer.param.threshold.voltage.upperGreaterThanLower}")
    private boolean isVoltageThresholdValid() {
        if (voltageUpper == null || voltageLower == null) {
            return true; // Let @NotNull handle null validation
        }
        return voltageUpper > voltageLower;
    }

    @AssertTrue(message = "{juncer.param.threshold.current.upperGreaterThanLower}")
    private boolean isCurrentThresholdValid() {
        if (currentUpper == null || currentLower == null) {
            return true; // Let @NotNull handle null validation
        }
        return currentUpper > currentLower;
    }


    /**
     * 转换数据传输
     */
    public void chg2Load(){

        if(voltageUpper != null){
            voltageUpper /= 2;
        }
        if(voltageLower != null){
            voltageLower /= 2;
        }
        convertCurrentToBytes();
    }

    /**
     * 转换数据展示
     */
    public void chg2Look(){
        if(voltageUpper != null){
            voltageUpper *= 2;
        }
        if(voltageLower != null){
            voltageLower *= 2;
        }
        convertBytesToCurrent();
    }


    /**
     * 将电流阈值转换为字节表示，用于数据传输
     */
    public void convertCurrentToBytes() {
        if (currentUpper != null) {
            currentUpper = CurrentValueConverter.currentValueToByte(currentUpper);
        }
        if (currentLower != null) {
            currentLower = CurrentValueConverter.currentValueToByte(currentLower);
        }
    }

    /**
     * 将字节表示的电流阈值转换为实际电流值(mA)
     */
    public void convertBytesToCurrent() {
        if (currentUpper != null) {
            currentUpper = CurrentValueConverter.byteToCurrentValue(currentUpper);
        }
        if (currentLower != null) {
            currentLower = CurrentValueConverter.byteToCurrentValue(currentLower);
        }
    }
}

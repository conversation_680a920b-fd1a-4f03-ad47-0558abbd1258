package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;

/**
 * 屏蔽控制应答
 */
@Data
public class TabShieldCmdAck extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;
    
    /**
     * 原因
     */
    private Integer reason;
    
    /**
     * 属性
     */
    private Integer cmdNo;
    
    /**
     * 屏蔽状态
     */
    private TabShieldCmd tabShieldCmd;

    @Override
    public String getAckBackKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_SHIELD_CMD.getCode());
    }
} 
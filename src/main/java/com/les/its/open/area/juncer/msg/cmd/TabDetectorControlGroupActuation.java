package com.les.its.open.area.juncer.msg.cmd;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.NeedAck;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 检测器控制组
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabDetectorControlGroupActuation extends TabInBase implements NeedAck {
    
    /**
     * 检测器控制组触发
     */
    @NotNull(message = "检测器控制组不能为空")
    @Size(min = 64, max = 64, message = "检测器控制组固定长度64")
    @DiscreteValuesList(acceptedValues = {0, 1}, message = "检测器控制组值必须是0或1")
    private List<Integer> detectorControlGroupActuations;

    @NotNull(message = "标记不能为空")
    @DiscreteValuesList(acceptedValues = {0, 1}, message = "标记必须是0或者1")
    @Size(min = 64, max = 64, message = "检测器标记固定长度64")
    private List<Integer>  flags;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public String getAckKey() {
        return getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_DETECTOR_CONTROL_GROUP_ACTUATION.getCode());
    }
}
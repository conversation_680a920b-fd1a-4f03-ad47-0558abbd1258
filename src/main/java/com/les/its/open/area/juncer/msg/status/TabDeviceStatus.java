package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 *.设备状态(电流/电压/温度)
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabDeviceStatus extends TabOutBase {
    /**
     * 检测器状态
     */
    private List<Integer> detectorStatus;

    /**
     * 主控板状态
     */
    private int controlBoardStatus;

    /**
     * 检测板状态
     */
    private int detectionBoardStatus;

    /**
     * 信息板状态
     */
    private int informationBoardStatus;

    /**
     * 灯驱板状态
     */
    private int lampDriverStatus;

    /**
     * 机柜门状态
     */
    private int cabinetDoorStatus;
}
    

package com.les.its.open.area.message.dbsave;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/21 11:16
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DataSaveBean {
    private List<Object> objectList;
}

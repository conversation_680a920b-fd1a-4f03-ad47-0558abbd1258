package com.les.its.open.area.message.param.lookload;

import com.les.its.open.area.juncer.msg.param.lookload.dto.SegmentParam;

import com.les.its.open.area.message.param.SgpTransAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 日计划参数
 */
@Data
public class DayPlanParam implements SgpTransAble {
    public static final String MqObjectId = "10012";

    /**
     * 日计划编号
     */
    @NotNull(message = "{basic.param.dayPlan.dayPlanNo.notNull}")
    @Min(value = 1, message = "{basic.param.dayPlan.dayPlanNo.min}")
    @Max(value = 128, message = "{basic.param.dayPlan.dayPlanNo.max}")
    private Integer dayPlanNo;

    /**
     * 子路口号
     */
    @NotNull(message = "{basic.param.dayPlan.crossingSeqNo.notNull}")
    @Min(value = 1, message = "{basic.param.dayPlan.crossingSeqNo.min}")
    @Max(value = 8, message = "{basic.param.dayPlan.crossingSeqNo.max}")
    private Integer crossingSeqNo;

    /**
     * 时段参数
     */
    @NotNull(message = "{basic.param.dayPlan.segmentParams.notNull}")
    @Size(min = 1, max = 48, message = "{basic.param.dayPlan.segmentParams.size}")
    @Valid
    private List<SegmentParam> segmentParams;

    /**
     * 日计划名称
     */
    @NotNull(message = "{basic.param.dayPlan.dayPlanName.notNull}")
    private String dayPlanName;

    /**
     * 验证时段参数中的开始时间是否按升序排列
     * @return 如果开始时间按升序排列返回true，否则返回false
     */
    @AssertTrue(message = "{basic.param.dayPlan.segmentParams.startTime.order}")
    public boolean isSegmentParamsStartTimeOrdered() {
        if (segmentParams == null || segmentParams.size() <= 1) {
            return true;
        }
        
        for (int i = 0; i < segmentParams.size() - 1; i++) {
            if (segmentParams.get(i).getStartTime() >= segmentParams.get(i + 1).getStartTime()) {
                return false;
            }
        }
        return true;
    }

    private String signalControllerID;

    @Override
    public String getUrl() {
        return "";
    }

    @Override
    public Object transData() {
        return this;
    }

    @Override
    public String getClazzName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public String getMqNo() {
        return MqObjectId;
    }

    @Override
    public int getDataNo() {
        return dayPlanNo;
    }

    @Override
    public String getSignalId() {
        return signalControllerID;
    }
} 
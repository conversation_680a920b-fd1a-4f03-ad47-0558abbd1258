package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.BasicParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.sub.HostIp;
import com.les.its.open.area.message.param.lookload.sub.Ipv4;
import com.les.its.open.area.message.param.lookload.sub.Ipv6;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class BasicParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public BasicParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_BasicParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return BasicParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看设备信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_DEVICE_INFO, signalInfoOp.get());
            areaMessages.add(msg);
        }

        // 调看基础信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_BASE_INFO, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看信号机网络配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_NETWORK_CONFIGURATION, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看上位机网络配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_HOST_NETWORK, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 调看生产配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_PRE_CONFIG, signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures,
                                              StringBuilder errorMsgRet) {

        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            BasicParam basicParam = new BasicParam();
            //设置信号机编号
            basicParam.setSignalControllerID(requestMessage.getSignalControllerID());

            //设备信息
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.DeviceInfo> deviceInfoOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_DEVICE_INFO);
            if(deviceInfoOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_DEVICE_INFO);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), deviceInfoOp.get());

            //基础信息
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.TscBaseInfo> tscBaseInfoOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_BASE_INFO);
            if(tscBaseInfoOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_BASE_INFO);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), tscBaseInfoOp.get());

            //网络配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.NetworkConfig> networkConfigOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_NETWORK_CONFIGURATION);
            if(networkConfigOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_BASE_INFO);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), networkConfigOp.get());


            //上位机网络配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.HostNetwork> hostNetworkOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_HOST_NETWORK);
            if(hostNetworkOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_BASE_INFO);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), hostNetworkOp.get());


            //生产配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.PreConfig> preConfigOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_PRE_CONFIG);
            if(preConfigOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_PRE_CONFIG);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), preConfigOp.get());


            {
                //区域号
                basicParam.setNoArea((int)(tscBaseInfoOp.get().getTscId()/1000));
                //路口号
                basicParam.setNoJunc((int)(tscBaseInfoOp.get().getTscId()%1000));
                //信号机型
                basicParam.setType(deviceInfoOp.get().getModel());
                //出厂日期
                basicParam.setProductionDate(deviceInfoOp.get().getProductionDate().toLocalDate());
                //经度
                basicParam.setLon(tscBaseInfoOp.get().getLongitude());
                //纬度
                basicParam.setLat(tscBaseInfoOp.get().getLatitude());
                //控制路口
                basicParam.setControlledJunctionNum(tscBaseInfoOp.get().getControlledJunctionNum());
                //安装路口
                basicParam.setInstallIntersection(tscBaseInfoOp.get().getInstallIntersection());
                //通讯模式
                basicParam.setCmmType(networkConfigOp.get().getIpEnabled());
                // mac地址
                basicParam.setMacAddress(preConfigOp.get().getMacAddress());
                //ipv4信息
                {
                    Ipv4 ipv4 = new Ipv4();
                    ipv4.setIp(networkConfigOp.get().getIpv4().getIp());
                    ipv4.setMask(networkConfigOp.get().getIpv4().getMask());
                    ipv4.setGateway(networkConfigOp.get().getIpv4().getGateway());
                    basicParam.setIpv4(ipv4);
                }
                //ipv6信息
                {
                    Ipv6 ipv6 = new Ipv6();
                    ipv6.setIp(networkConfigOp.get().getIpv6().getIp());
                    ipv6.setMask(networkConfigOp.get().getIpv6().getMask());
                    ipv6.setGateway(networkConfigOp.get().getIpv6().getGateway());
                    basicParam.setIpv6(ipv6);
                }
                //上位机ipv4地址
                {
                    List<HostIp> hostIpv4s = new ArrayList<>();
                    basicParam.setHostIpv4s(hostIpv4s);
                    hostNetworkOp.get().getHostIpv4s().forEach(ipv4 -> {
                        HostIp hostIp = new HostIp();
                        hostIp.setEnabled(ipv4.getEnabled());
                        hostIp.setIp(ipv4.getIp());
                        hostIp.setPort(ipv4.getPort());
                        hostIp.setCommType(ipv4.getCommType());
                        hostIp.setProtoType(ipv4.getProtoType());
                        hostIpv4s.add(hostIp);

                    });
                }
                //上位机ipv6地址
                {
                    List<HostIp> hostIpv6s = new ArrayList<>();
                    basicParam.setHostIpv6s(hostIpv6s);
                    hostNetworkOp.get().getHostIpv6s().forEach(ipv6 -> {
                        HostIp hostIp = new HostIp();
                        hostIp.setEnabled(ipv6.getEnabled());
                        hostIp.setIp(ipv6.getIp());
                        hostIp.setPort(ipv6.getPort());
                        hostIp.setCommType(ipv6.getCommType());
                        hostIp.setProtoType(ipv6.getProtoType());
                        hostIpv6s.add(hostIp);
                    });
                }
            }

            //应答参数
            datas.add(basicParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        BasicParam basicParam = jsonObject.toJavaObject(BasicParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(basicParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, basicParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //基础信息
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.TscBaseInfo tscBaseInfo
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.TscBaseInfo();
            //安装位置
            tscBaseInfo.setInstallIntersection(basicParam.getInstallIntersection() != null
                    ? basicParam.getInstallIntersection() : "");
            //信号机id
            tscBaseInfo.setTscId(basicParam.getNoArea() * 1000 + basicParam.getNoJunc());
            //控制路口
            tscBaseInfo.setControlledJunctionNum(basicParam.getControlledJunctionNum());
            //经度
            tscBaseInfo.setLongitude((basicParam.getLon()));
            //纬度
            tscBaseInfo.setLatitude((basicParam.getLat()));

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_BASE_INFO, tscBaseInfo, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //网络配置
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.NetworkConfig networkConfig
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.NetworkConfig();
            networkConfig.setIpEnabled(basicParam.getCmmType());
            //ipv4配置
            if(basicParam.getIpv4() != null){
                com.les.its.open.area.juncer.msg.param.lookload.dto.Ipv4Info ipv4
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.Ipv4Info();
                ipv4.setIp(basicParam.getIpv4().getIp());
                ipv4.setMask(basicParam.getIpv4().getMask());
                ipv4.setGateway(basicParam.getIpv4().getGateway());
                networkConfig.setIpv4(ipv4);
            }
            //ipv6配置
            if(basicParam.getIpv6() != null){
                com.les.its.open.area.juncer.msg.param.lookload.dto.Ipv6Info ipv6
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.Ipv6Info();
                ipv6.setIp(basicParam.getIpv6().getIp());
                ipv6.setMask(basicParam.getIpv6().getMask());
                ipv6.setGateway(basicParam.getIpv6().getGateway());
                networkConfig.setIpv6(ipv6);
            }

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_NETWORK_CONFIGURATION, networkConfig, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //上位机网络配置
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.HostNetwork hostNetwork
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.HostNetwork();

            //ipv4配置
            {
                List<com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv4> hostIpv4s = new ArrayList<>();
                if(basicParam.getHostIpv4s() != null) {
                    basicParam.getHostIpv4s().forEach(hostIpv4 -> {
                        com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv4 ipv4 = new com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv4();
                        ipv4.setEnabled(hostIpv4.getEnabled());
                        ipv4.setIp(hostIpv4.getIp());
                        ipv4.setPort(hostIpv4.getPort());
                        ipv4.setCommType(hostIpv4.getCommType());
                        ipv4.setProtoType(hostIpv4.getProtoType());
                        hostIpv4s.add(ipv4);
                    });
                }
                hostNetwork.setHostIpv4s(hostIpv4s);
            }
            //ipv6配置
            {
                List<com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv6> hostIpv6s = new ArrayList<>();
                if(basicParam.getHostIpv6s() != null) {
                    basicParam.getHostIpv6s().forEach(hostIpv6 -> {
                        com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv6 ipv6 = new com.les.its.open.area.juncer.msg.param.lookload.dto.HostIpv6();
                        ipv6.setEnabled(hostIpv6.getEnabled());
                        ipv6.setIp(hostIpv6.getIp());
                        ipv6.setPort(hostIpv6.getPort());
                        ipv6.setCommType(hostIpv6.getCommType());
                        ipv6.setProtoType(hostIpv6.getProtoType());
                        hostIpv6s.add(ipv6);
                    });
                }
                hostNetwork.setHostIpv6s(hostIpv6s);
            }
            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_HOST_NETWORK, hostNetwork, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures,
                                                    StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}

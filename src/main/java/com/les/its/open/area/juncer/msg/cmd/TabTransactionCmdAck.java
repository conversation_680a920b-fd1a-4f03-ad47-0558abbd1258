package com.les.its.open.area.juncer.msg.cmd;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.AckAble;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 事务交易控制应答
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TabTransactionCmdAck  extends TabOutBase implements AckAble {
    
    /**
     * 控制结果
     */
    private Integer ack;

    /**
     * 原因
     */
    private Integer reason;

    private TabTransactionCmd tabTransactionCmd;

    @Override
    public String getAckBackKey() {

        String orgAck = getControllerId() + "###" + String.format("0x%08X", MsgType.TAB_TRANSACTION_CMD.getCode())
                + "###" + tabTransactionCmd.getTransactionCreate();
        return orgAck;
    }
}
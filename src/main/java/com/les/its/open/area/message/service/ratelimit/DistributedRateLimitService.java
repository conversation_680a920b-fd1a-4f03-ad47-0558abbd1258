package com.les.its.open.area.message.service.ratelimit;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.distributed.ExpirationAfterWriteStrategy;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.distributed.serialization.Mapper;
import io.github.bucket4j.redis.redisson.Bucket4jRedisson;
import jakarta.annotation.PostConstruct;
import org.redisson.Redisson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.function.Supplier;

import static java.time.Duration.ofSeconds;


/**
 * 分布式流控服务 - 基于Bucket4j + Redis
 */
@Service
public class DistributedRateLimitService {

    @Autowired
    private Redisson redisson;

    private ProxyManager<String> proxyManager;

    @PostConstruct
    public void init() {
        this.proxyManager = Bucket4jRedisson.casBasedBuilder(redisson.getCommandExecutor())
                .expirationAfterWrite(ExpirationAfterWriteStrategy.basedOnTimeForRefillingBucketUpToMax(ofSeconds(10)))
                .keyMapper(Mapper.STRING)
                .build();
    }

    /**
     * 检查是否允许访问
     * @param key 限流key（如用户ID、IP等）
     * @param limitConfig 限流配置
     * @return true表示允许访问，false表示被限流
     */
    public boolean tryConsume(String key, RateLimitConfig limitConfig) {
        Bucket bucket = getBucket(key, limitConfig);
        return bucket.tryConsume(1);
    }

    /**
     * 获取剩余令牌数
     */
    public long getAvailableTokens(String key, RateLimitConfig limitConfig) {
        Bucket bucket = getBucket(key, limitConfig);
        return bucket.getAvailableTokens();
    }

    /**
     * 阻塞式获取令牌（等待直到获取到令牌）
     */
    public boolean consume(String key, RateLimitConfig limitConfig, Duration maxWaitTime) throws InterruptedException {
        Bucket bucket = getBucket(key, limitConfig);
        return bucket.asBlocking().tryConsume(1, maxWaitTime);
    }

    private Bucket getBucket(String key, RateLimitConfig limitConfig) {
        Supplier<BucketConfiguration> configSupplier = () -> {
            return BucketConfiguration.builder()
                    .addLimit(Bandwidth.builder()
                            .capacity(limitConfig.getCapacity())
                            .refillIntervally(limitConfig.getTokens(), limitConfig.getPeriod())
                            .build())
                    .build();
        };

        return proxyManager.builder().build(key, configSupplier);
    }
}

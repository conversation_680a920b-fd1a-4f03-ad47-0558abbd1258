package com.les.its.open.area.net.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.status.TabCycle;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class TabCycleProcess extends TabOuterBaseMsgProcess {
    @Override
    public MsgType msgType() {
        return MsgType.TAB_CYCLE;
    }

    @Override
    public int oneItemSize() {
        return (4  + 16 + 16 + 3 + 16 + 16 + 1 + 1);
    }

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
           TabCycle tabCycle = new TabCycle();
           ByteBuf buf = Unpooled.buffer(body.length);
           buf.writeBytes(body);
           tabCycle.setCrossingSeqNo(buf.readUnsignedByte() & 0xff);
           tabCycle.setCycleLst(buf.readUnsignedShort() & 0xffff);
           tabCycle.setStageNumLst(buf.readUnsignedByte() & 0xff);

           // 上周期阶段链
           List<Integer> stageChainPre = new ArrayList<>();
           tabCycle.setStageChainPre(stageChainPre);
           for (int i = 0; i < 16; i++) {
               tabCycle.getStageChainPre().add(buf.readUnsignedByte() & 0xff);
           }

           //上周期时长链
           List<Integer> timeChainPre = new ArrayList<>();
           tabCycle.setTimeChainPre(timeChainPre);
           for (int i = 0; i < 16; i++) {
               tabCycle.getTimeChainPre().add(buf.readUnsignedShort() & 0xffff);
           }
           tabCycle.setCycle(buf.readUnsignedShort() & 0xffff);
           tabCycle.setStageNum(buf.readUnsignedByte() & 0xff);

           //本周期阶段链
            List<Integer> stageChain = new ArrayList<>();
            tabCycle.setStageChain(stageChain);
            for (int i = 0; i < 16; i++) {
               tabCycle.getStageChain().add(buf.readUnsignedByte() & 0xff);
            }

            //本周期时长链
            List<Integer> timeChain = new ArrayList<>();
            tabCycle.setTimeChain(timeChain);
            for (int i = 0; i < 16; i++) {
               tabCycle.getTimeChain().add(buf.readUnsignedShort() & 0xffff);
            }

           tabCycle.setCoordinatedStatus(buf.readUnsignedByte() & 0xff);
           tabCycle.setCoordinatedTransMode(buf.readUnsignedByte() & 0xff);
           buf.release();
           return Optional.of(tabCycle);
        }

        return Optional.empty();
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LampFaultDetect implements DataIndexAble {
    // 检测周期
    @NotNull(message = "{juncer.param.lampFaultDetect.detectCycle.notNull}")
    @Range(min = 1, max = 3, message = "{juncer.param.lampFaultDetect.detectCycle.range}")
    private Integer detectCycle;
    
    // 红灯损坏检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.redFaultDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.redFaultDetection.range}")
    private Integer redFaultDetection;
    
    // 绿冲突检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.greenConflictDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.greenConflictDetection.range}")
    private Integer greenConflictDetection;
    
    // 红绿同亮检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.redGreenDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.redGreenDetection.range}")
    private Integer redGreenDetection;
    
    // 黄灯损坏检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.yellowFaultDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.yellowFaultDetection.range}")
    private Integer yellowFaultDetection;
    
    // 绿灯损坏检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.greenFaultDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.greenFaultDetection.range}")
    private Integer greenFaultDetection;
    
    // 红黄同亮检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.redYellowDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.redYellowDetection.range}")
    private Integer redYellowDetection;
    
    // 黄绿同亮检测上报
    @NotNull(message = "{juncer.param.lampFaultDetect.yellowGreenDetection.notNull}")
    @Range(min = 0, max = 4, message = "{juncer.param.lampFaultDetect.yellowGreenDetection.range}")
    private Integer yellowGreenDetection;

    @Override
    public int getDataNo() {
        return 1;
    }
}
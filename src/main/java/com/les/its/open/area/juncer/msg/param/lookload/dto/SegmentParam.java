package com.les.its.open.area.juncer.msg.param.lookload.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentParam {
    // 开始时间
    @NotNull(message = "{juncer.param.segmentParam.startTime.notNull}")
    @Range(min = 0, max = 1439, message = "{juncer.param.segmentParam.startTime.range}")
    private Integer startTime;
    
    // 方案号
    @NotNull(message = "{juncer.param.segmentParam.planNo.notNull}")
    @Range(min = 1, max = 128, message = "{juncer.param.segmentParam.planNo.range}")
    private Integer planNo;
    
    // 控制方式
    @NotNull(message = "{juncer.param.segmentParam.controlMode.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.segmentParam.controlMode.range}")
    private Integer controlMode;
    
    // 方案过渡方式 0:shortway最优，1:dwell驻留，2:addOnly，3：subtractOnly
    @NotNull(message = "{juncer.param.segmentParam.coordCorrectionMode.notNull}")
    @Range(min = 0, max = 3, message = "{juncer.param.segmentParam.coordCorrectionMode.range}")
    private Integer coordCorrectionMode;

    // 协调方向
    @NotNull(message = "{juncer.param.segmentParam.coordDirection.notNull}")
    @Range(min = 0, max = 255, message = "{juncer.param.segmentParam.coordDirection.range}")
    private Integer coordDirection;
    
    // 动作链个数
    //@NotNull(message = "{juncer.param.segmentParam.actionNum.notNull}")
    @Range(min = 0, max = 16, message = "{juncer.param.segmentParam.actionNum.range}")
    private Integer actionNum;

    // 动作链
    @NotNull(message = "{juncer.param.segmentParam.segmentActions.notNull}")
    @Size(min = 0, max = 16, message = "{juncer.param.segmentParam.segmentActions.size}")
    @Valid
    private List<SegmentAction> segmentActions;
} 
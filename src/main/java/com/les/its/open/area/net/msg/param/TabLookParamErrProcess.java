package com.les.its.open.area.net.msg.param;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.param.TabLookParamErr;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabLookParamErrProcess extends TabOuterBaseMsgProcess {



    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        TabLookParamErr tabLookParamErr = new TabLookParamErr();

        ParamMsgType paramMsgType = ParamMsgType.fromCode((juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_3() << 8)
                + (juncerMsg.getOpenLesMessage().getLesHeader().getMessageID_4()));
        if(ParamMsgType.PARAM_UNKNOWN == paramMsgType) {
            return Optional.empty();
        }

        tabLookParamErr.setParamMsgType(paramMsgType);
        byte[] body = juncerMsg.getOpenLesMessage().getLesBody().getBody();
        if(body.length == oneItemSize()) {
            tabLookParamErr.setOffset((body[0] & 0xff));
            tabLookParamErr.setCount(body[1] & 0xff);
            tabLookParamErr.setErrCode(body[2] & 0xff);
            return Optional.of(tabLookParamErr);
        }

        return Optional.empty();
    }

    @Override
    public MsgType msgType() {
        return MsgType.TAB_LOOK_PARAM_ERR;
    }

    @Override
    public int oneItemSize() {
        return 3;
    }


}

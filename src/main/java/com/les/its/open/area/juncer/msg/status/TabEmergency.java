package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 紧急状态
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabEmergency extends TabOutBase {
    /**
     * 紧急编号
     */
    private int emergencyNo;
    
    /**
     * 紧急申请状态
     */
    private int applyStatus;
    
    /**
     * 紧急执行状态
     */
    private int executeStatus;


    @Override
    public List<TabInBase> proc(ControllerAgent controllerAgent) {
        //发送
        log.debug("处理信号机{}-{}", controllerAgent.getControllerId(), getMsgType().getDescription());

        //修改内存数据
        if(emergencyNo <= controllerAgent.getTabEmergencyList().size() && emergencyNo > 0 ){
              controllerAgent.getTabEmergencyList().get(emergencyNo - 1).setApplyStatus(applyStatus);
              controllerAgent.getTabEmergencyList().get(emergencyNo - 1).setExecuteStatus(executeStatus);
        }

        List<TabInBase> inBases = new ArrayList<>();
        return inBases;
    }

} 
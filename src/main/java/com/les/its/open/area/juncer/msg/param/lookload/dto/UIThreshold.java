package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UIThreshold implements DataIndexAble {
    // 电压阈值上限
    @Range(min = 0, max = 255, message = "{juncer.param.uiThreshold.voltageThresholdUpper.range}") 
    @NotNull(message = "{juncer.param.uiThreshold.voltageThresholdUpper.notNull}")
    private Integer voltageThresholdUpper;
    
    // 电压阈值下限
    @Range(min = 0, max = 255, message = "{juncer.param.uiThreshold.voltageThresholdLower.range}")
    @NotNull(message = "{juncer.param.uiThreshold.voltageThresholdLower.notNull}")
    private Integer voltageThresholdLower;
    
    // 电流阈值上限
    @Range(min = 0, max = 255, message = "{juncer.param.uiThreshold.currentThresholdUpper.range}")
    @NotNull(message = "{juncer.param.uiThreshold.currentThresholdUpper.notNull}")
    private Integer currentThresholdUpper;
    
    // 电流阈值下限
    @Range(min = 0, max = 255, message = "{juncer.param.uiThreshold.currentThresholdLower.range}")
    @NotNull(message = "{juncer.param.uiThreshold.currentThresholdLower.notNull}")
    private Integer currentThresholdLower;

    @AssertTrue(message = "{juncer.param.uiThreshold.voltageThreshold.valid}")
    private boolean isVoltageThresholdValid() {
        if (voltageThresholdUpper == null || voltageThresholdLower == null) {
            return true; // 让 @NotNull 处理空值验证
        }
        return voltageThresholdUpper > voltageThresholdLower;
    }

    @AssertTrue(message = "{juncer.param.uiThreshold.currentThreshold.valid}")
    private boolean isCurrentThresholdValid() {
        if (currentThresholdUpper == null || currentThresholdLower == null) {
            return true; // 让 @NotNull 处理空值验证
        }
        return currentThresholdUpper > currentThresholdLower;
    }

    @Override
    public int getDataNo() {
        return 1;
    }
}
package com.les.its.open.area.net.msg.link;

import com.les.its.open.area.juncer.msg.TabOutBase;
import com.les.its.open.area.juncer.msg.link.TabRequest;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.TabOuterBaseMsgProcess;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TabRequestProcess extends TabOuterBaseMsgProcess {

    @Override
    public Optional<TabOutBase> toInner(JuncerMsg juncerMsg, ControllerBaseInfo controllerBaseInfo) {
        TabRequest tabRequest = new TabRequest();
       return Optional.of(tabRequest);
    }

    @Override
    public MsgType msgType() {
        return MsgType.TAB_REQUEST;
    }

    @Override
    public int oneItemSize() {
        return 0;
    }
}

package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFlag implements DataIndexAble {
    // 用户变量编号
    @NotNull(message = "{juncer.param.userFlag.userFlagNo.notNull}")
    @Range(min = 1, max = 64, message = "{juncer.param.userFlag.userFlagNo.range}")
    private Integer userFlagNo;
    
    // 用户变量名称
    @NotNull(message = "{juncer.param.userFlag.userFlagName.notNull}")
    private String userFlagName;

    @Override
    public int getDataNo() {
        return userFlagNo;
    }
}
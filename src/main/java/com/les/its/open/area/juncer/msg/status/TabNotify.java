package com.les.its.open.area.juncer.msg.status;

import com.les.its.open.area.juncer.msg.TabOutBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据变更
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Slf4j
public class TabNotify extends TabOutBase {
    /**
     * 配置来源
     */
    private int configurationSource;
    
    /**
     * 修改数据项
     */
    private int modifiedItem;
} 
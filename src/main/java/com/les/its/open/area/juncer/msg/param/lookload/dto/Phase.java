package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DiscreteValuesList;
import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Phase implements DataIndexAble {
    // 相位编号
    @NotNull(message = "相位编号不能为空")
    @Range(min = 1, max = 64, message = "相位编号有效范围是[1,64]")
    private Integer phaseNo;

    // 相位灯序编号
    @NotNull(message = "相位灯序编号不能为空")
    @Range(min = 1, max = 16, message = "相位灯序编号有效范围是[1,16]")
    private Integer lampSequenceNo;
    
    // 相位灯组 [约束原则: 灯组控制源唯一，一个灯相只能属于一个相位]
    @NotNull(message = "相位灯组不能为空")
    @Size(min = 64, max = 64, message = "相位灯组长度有效长度是64")
    @DiscreteValuesList(min = 0, max = 1, message = "相位灯组有效范围是[0,1]")
    private List<Integer> phaseLightsGroups;

    /**
     * 绿灯开始方式
     * 0: 一直出现;
     * 1: 绿间隔前有需求;
     * 2: 黄口时间结束前有需求
     */
    @NotNull(message = "绿灯开始方式不能为空")
    @Range(min = 0, max = 2, message = "绿灯开始方式有效范围是[0,2]")
    private Integer appearance;

    /**
     * 绿灯结束方式
     * 0:阶段结束末
     */
    @NotNull(message = "绿灯结束方式不能为空")
    private Integer termination;

    // 关联相位
    @NotNull(message = "关联相位不能为空")
    @Range(min = 0, max = 64, message = "关联相位有效范围是[0,64]")
    private Integer assocPhase;

    // 相位名称
    private String phaseName;


    @Override
    public int getDataNo() {
        return phaseNo;
    }
}
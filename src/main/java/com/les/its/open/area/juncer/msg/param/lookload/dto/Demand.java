package com.les.its.open.area.juncer.msg.param.lookload.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Demand {
    // 需求类型 (0:无, 逻辑输入, 定时器, 用户变量)
    @NotNull(message = "{juncer.param.demand.inputType.notNull}")
    private Integer inputType;
        
    // 需求编号
    @NotNull(message = "{juncer.param.demand.inputNo.notNull}")
    private Integer inputNo;

    // 参数标记
    @NotNull(message = "{juncer.param.demand.inputFlag.notNull}")
    private Integer inputFlag;
} 
package com.les.its.open.task;


import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.MessagePublisher;
import com.les.its.open.netty.NettyClient;
import com.les.its.open.netty.NettyServer;
import com.les.its.open.netty.NettyUdp;
import com.les.its.open.netty.link.ClientType;
import com.les.its.open.netty.link.LinkManager;
import com.les.its.open.netty.link.ServerType;
import com.les.its.open.netty.reconnect.ReconnectAble;
import com.les.its.open.netty.stop.StopAble;
import com.les.its.open.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * <AUTHOR>
 * @Description 初始化监听端口/远程连接设置
 * @date 2014年3月15日
 * @version 1.0
 */
@Service
@Slf4j
public class AsyncTaskService {

    private final MessagePublisher messagePublisher;

    private final LinkManager linkManager;

    /**
     * 重连随机数
     */
    private static final Random delayRandom = new Random(System.currentTimeMillis());

    public AsyncTaskService(MessagePublisher messagePublisher, LinkManager linkManager) {
        this.messagePublisher = messagePublisher;
        this.linkManager = linkManager;
    }

    /**
     * 服务端监听协议
     * @param portPotocol
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeServerAsyncTask(ServerType.PortPotocol portPotocol)  {
        executeServerAsyncTask(portPotocol.getPort(), portPotocol.getProtocol());
    }

    /**
     * 新建本地端口以及协议
     * @param port
     * @param protocol
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeServerAsyncTask(int port, String protocol) {
        log.info("开始绑定端口数据port={}, 协议={}", port, protocol);
        try {
            NettyServer nettyServer = new NettyServer(port, protocol, messagePublisher);
            nettyServer.bind();
            linkManager.addLink(nettyServer);
        }catch (Exception e){
            log.error("绑定本地端口异常", e);
        }
    }

    /**
     * 新建串口/客户端协议
     * @param ipPortProtocol
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeClientAsyncTask(ClientType.IpPortProtocol ipPortProtocol)  {
        try {
            if (ipPortProtocol.getIp().matches(ConstValue.IP_PATTERN)) {
                /*IP类型的链路*/
                executeClientAsyncTask(ipPortProtocol.getIp(), ipPortProtocol.getPort(), ipPortProtocol.getLocalPort(),
                        ipPortProtocol.getProtocol());
            } else {
                /*串口类型的数据*/
                executeCommtAsyncTask(ipPortProtocol.getIp(), ipPortProtocol.getProtocol());
            }
        }catch (Exception e){
            log.error("连接端口异常", e);
        }
    }


    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeClientAsyncTask(String ip, int port, int localPort, String protocol){
        try {
            log.info("开始尝试连接端口数据port={}, ip= {}, 协议={}", port, ip, protocol);
            NettyClient nettyClient = new NettyClient(ip, port, localPort, protocol, messagePublisher);
            nettyClient.connect();
            linkManager.addLink(nettyClient);
        } catch (Exception e){
            log.error("连接{}{}异常", port,  ip,  e);
        }
    }


    /**
     * @param address
     * @param protocol
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeCommtAsyncTask(String address, String protocol) {
        log.error("不支持串口数据address={}, 协议={}" , address, protocol);
    }

    /**
     * @param address
     * @param protocol
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeCommtAsyncTaskRandomDelay(String address, String protocol) {
        int delay = delayRandom.nextInt(30) * 1000;
        log.info("不支持延迟{}ms, 尝试连接串口数据address={}, 协议={}", delay, address, protocol);
    }


    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeUdpAsyncTask(int port, String protocol) {
        log.info("开始绑定Udp端口数据port={}, 协议={}", port, protocol);
        try{
            NettyUdp nettyUdp =  new NettyUdp(port, protocol, messagePublisher);
            nettyUdp.bind();
            linkManager.addLink(nettyUdp);
        }catch (Exception e){
            log.error("绑定本地端口异常", e);
        }
    }


    /**
     * @param reconnectAble
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeReconnectEvent(ReconnectAble reconnectAble)
    {
        log.info("---开始重新连接---");
        reconnectAble.reconnect();
    }


    /**
     * 停止服务
     * @param stopAble
     */
    @Async(GlobalConfigure.LINK_ASYC_EXECUTOR)
    public void executeStopEvent(StopAble stopAble)
    {
        log.info("---开始停止---");
        stopAble.stop();
    }

}

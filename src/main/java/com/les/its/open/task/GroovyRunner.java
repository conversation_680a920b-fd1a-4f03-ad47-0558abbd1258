package com.les.its.open.task;

import com.les.its.open.groovy.SelfDefinedRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 系统初始化操作类
 * @date 2014年3月15日
 */
@Component
@Order(value = 3)
@Slf4j
public class GroovyRunner implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        log.info(">>>>>>>>>>>>>>>自定义规则准备，执行数据准备<<<<<<<<<<<<");
        try {
            //selfDefinedRuleManager.loadData();
        } catch (Exception e) {
            log.error("异常", e);
        }
    }
}

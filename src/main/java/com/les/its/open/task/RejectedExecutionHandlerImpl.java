package com.les.its.open.task;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class RejectedExecutionHandlerImpl implements RejectedExecutionHandler {

    private String name;

    public RejectedExecutionHandlerImpl(String name) {
        this.name = name;
    }

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        try {
            log.error("!!!  task full  ======== !!! {} -{}", name, executor.toString());
            executor.getQueue().clear();
        } catch (Exception e) {
            throw new RejectedExecutionException("There was an unexpected InterruptedException whilst waiting to " +
                    "add a Runnable in the executor's blocking queue", e);
        }
    }
}

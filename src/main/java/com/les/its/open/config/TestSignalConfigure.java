package com.les.its.open.config;

import com.les.its.open.config.dto.TestSignalConfigureDto;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/6/15 13:51
 */
@Data
@Component
@ConfigurationProperties(prefix = "test")
@Slf4j
public class TestSignalConfigure {
    private boolean useTest;
    private Map<String, TestSignalConfigureDto> signalMap;
}

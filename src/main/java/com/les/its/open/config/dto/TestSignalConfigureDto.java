package com.les.its.open.config.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/6/15 13:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class TestSignalConfigureDto {
    private boolean useTest;
    private String signalId;
    private List<String> crossingIds;
    private int noArea;
    private int noJunc;
    private List<Integer> subJuncNos;
    private String ip;
    private int port;
}

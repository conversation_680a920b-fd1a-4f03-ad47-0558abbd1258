package com.les.its.open.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Data
@Component
@ConfigurationProperties(prefix = "app.locale")
public class LocaleProperties {
    /**
     * 默认语言
     * 可选值：zh_CN（简体中文）, zh_TW（繁体中文）, en（英文）
     */
    private String defaultLocale = "zh_CN";

    /**
     * 获取默认的 Locale 对象
     */
    public Locale getDefaultLocale() {
        return switch (defaultLocale.toLowerCase()) {
            case "zh_tw" -> Locale.TRADITIONAL_CHINESE;
            case "en" -> Locale.ENGLISH;
            default -> Locale.SIMPLIFIED_CHINESE;
        };
    }
} 
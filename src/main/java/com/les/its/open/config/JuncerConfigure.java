package com.les.its.open.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/6/15 13:51
 */
@Data
@Component
@ConfigurationProperties(prefix = "its.juncer")
@Slf4j
public class JuncerConfigure {
    private List<Integer> disableLogMsgs;

    private List<String> disableFields;
}

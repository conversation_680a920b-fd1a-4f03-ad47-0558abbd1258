package com.les.its.open.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.stream.Collectors;

/**
 * The type Global configure.
 *
 * @ClassName: GlobalConfigure
 * @Description: 全局数据配置中心
 * @Author: king
 * @CreateDate: 2018 /12/6 9:59
 */
@Configuration
public class GlobalConfigure {


    /**
     * 链路管理使用的线程池
     */
    public final static String LINK_ASYC_EXECUTOR = "LINK";

    /**
     * 用于分发接收报文的分发线程池
     */
    public final static String MESSAGE_ASYC_EXECUTOR = "MESSAGE";

    /**
     * 用于分发接收向外系统发送报文的分发线程池
     */
    public final static String OUTER_MESSAGE_ASYC_EXECUTOR = "OUTER_MSG";

    /**
     * 用于JUNCER报文处理线程
     */
    public final static String JUNCER_ASYC_EXECUTOR = "JUNCER";


    /**
     * 用于处理数据存储的线程池
     */
    public final static String DB_MSG_PROCESS_EXECUTOR = "DB_MSG";

    /**
     * 用于处理系统mq消息的线程池
     */
    public final static String MQ_MSG_PROCESS_EXECUTOR = "MQ_MSG";


    /**
     * 用于sgp数据存储的线程池
     */
    public final static String SGP_PROCESS_EXECUTOR = "SGP_EXE";

    /**
     * 用于websocket数据线程池
     */
    public final static String WEBSOCKET_EXECUTOR = "WS_EXE";

    /**
     * 城市编码
     */
    public static String cityCode;

    /**
     * 交通管理部门机构代码
     */
    public static String departmentCode;

    /**
     * 城市编码
     */
    public static int areaNo;

    /**
     * ops 访问地址
     */
    public static String opsIp;

    /**
     * MQ的exchange名称
     */
    public static String exchangeName;

    /**
     * 中心机控制命令exchange
     */
    public static String centralExchange;

    /**
     * 中心机控制命令接收队列
     */
    public static String centralCmdQueue;

    /**
     * 中心机控制命令路由
     */
    public static String routingPrefix;

    /**
     * MQ的exchange名称
     */
    public static String exchangeName2;
    /**
     * MQ的路由前缀名称
     */
    public static String routingKeyPrefix2;

    /**
     * MQ获取数据项路口前缀
     */
    public static String routingKeyPrefixListen2;

    /**
     * MQ获取数据消息队列名称
     */
    public static String cmdQueue2;


    /**
     * 是否启用Sgp数据结构存储
     */
    public static volatile boolean enableUseSgp;

    /**
     * 数据请求的ip地址
     */
    public static String signalParamIp;

    /**
     * 是否检查信号机、路口数据项
     */
    public static boolean enableCheckSignalAndCrossingData = false;


    /**
     * 过滤的信号品牌参数
     */
    public static List<String> brandList;

    /**
     * 过滤的信号区域号
     */
    public static List<String> areaList;

    public static final String USER_DEFINED_RULE_FOLDER = "C:\\ruletaihe";

    @Value("${global.cityCode}")
    public void setCityCode(String cityCode) {
        GlobalConfigure.cityCode = cityCode;
    }

    @Value("${global.departmentCode}")
    public void setDepartmentCode(String departmentCode) {
        GlobalConfigure.departmentCode = departmentCode;
    }

    @Value("${global.areaNo}")
    public void setAreaNo(int areaNo) {
        GlobalConfigure.areaNo = areaNo;
    }

    @Value("${global.opsip}")
    public void setOpsIp(String opsIp) {
        GlobalConfigure.opsIp = opsIp;
    }

    /**
     * Sets routing key.
     *
     * @param exchangeName the routing key
     */
    @Value("${global.mq.exchange.name}")
    public void setExchangeName(String exchangeName) {
        GlobalConfigure.exchangeName = exchangeName;
    }


    @Value("${global.signalParamIp}")
    public void setSignalParamIp(String signalParamIp) {
        GlobalConfigure.signalParamIp = signalParamIp;
    }

    @Value("${global.enableCheckSignalAndCrossingData}")
    public void setEnableCheckSignalAndCrossingData(boolean enableCheckSignalAndCrossingData) {
        GlobalConfigure.enableCheckSignalAndCrossingData = enableCheckSignalAndCrossingData;
    }

    @Value("${global.enableUseSgp}")
    public void setEnableUseSgp(boolean enableUseSgp) {
        GlobalConfigure.enableUseSgp = enableUseSgp;
    }


    @Value("${global.mq.centralExchange.name}")
    public void setCentralExchange(String exchangeName) {
        GlobalConfigure.centralExchange = exchangeName;
    }

    @Value("${global.mq.centralExchange.queue}")
    public void setCentralCmdQueue(String centralCmdQueue) {
        GlobalConfigure.centralCmdQueue = centralCmdQueue;
    }

    @Value("${global.mq.centralExchange.routingPrefix}")
    public void setRoutingPrefix(String routingPrefix) {
        GlobalConfigure.routingPrefix = routingPrefix;
    }

    @Value("${global.mq.exchange2.name}")
    public void setExchangeName2(String exchangeName2) {
        GlobalConfigure.exchangeName2 = exchangeName2;
    }

    @Value("${global.mq.exchange2.routingKeyPrefix}")
    public void setRoutingKeyPrefix2(String routingKeyPrefix) {
        GlobalConfigure.routingKeyPrefix2 = routingKeyPrefix;
    }

    @Value("${global.mq.exchange2.routingKeyPrefixListen}")
    public void setRoutingKeyPrefixListen2(String routingKeyPrefixListen2) {
        GlobalConfigure.routingKeyPrefixListen2 = routingKeyPrefixListen2;
    }

    @Value("${global.mq.exchange2.cmdQueue}")
    public void setCmdQueue2(String cmdQueue2) {
        GlobalConfigure.cmdQueue2 = cmdQueue2;
    }


    @Value("#{'${global.brandList}'.split(',')}")
    public void setBrandList(List<String> brandList) {
        if (null == brandList) {
            return;
        }

        if (brandList.isEmpty()) {
            return;
        }

        GlobalConfigure.brandList = brandList.stream().filter(
                brand -> {
                    if (brand == null) {
                        return false;
                    } else if (brand.trim().isEmpty()) {
                        return false;
                    }
                    return true;
                }
        ).collect(Collectors.toList());;
    }

    @Value("#{'${global.areaList}'.split(',')}")
    public void setAreaList(List<String> areaList) {
        if (null == areaList) {
            return;
        }

        if (areaList.isEmpty()) {
            return;
        }

        GlobalConfigure.areaList = areaList.stream().filter(
                areaNo -> {
                    if (areaNo == null) {
                        return false;
                    } else if (areaNo.trim().isEmpty()) {
                        return false;
                    }
                    return true;
                }
        ).collect(Collectors.toList());
    }

}

package com.les.its.open.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Component
@Slf4j
public class ITSSocketConfigure {

    /**
     * 本地端口监听配置
     */
    public static List<Integer> listenPortList = new ArrayList<>();
    /**
     * 本地端口监听协议类型
     */
    public static List<String> listenProtocolList = new ArrayList<>();
    /**
     * 远程监听IP
     */
    public static List<String> connectIPList = new ArrayList<>();
    /**
     * 远程端口监听配置
     */
    public static List<Integer> connectPortList = new ArrayList<>();


    /**
     * 本地端口监听配置
     */
    public static List<Integer> connectLocalPortList = new ArrayList<>();

    /**
     * 远程端口监听协议类型
     */
    public static List<String> connectProtocolList = new ArrayList<>();

    /**
     * 串口地址链表
     */
    public static List<String> commAddressList = new ArrayList<>();

    /**
     * 串口协议类型
     */
    public static List<String> commProtocolList = new ArrayList<>();



    /**
     * Sets listen port list.
     *
     * @param listenPortList the listen port list
     */
    @Value("#{'${its.server.local.listens.port}'.split(',')}")
    public void setListenPortList(List<Integer> listenPortList) {
        if (null == listenPortList) {
            return;
        }

        if (listenPortList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.listenPortList = listenPortList.stream().filter(integer -> {
            return integer != null && integer != 0;
        }).collect(Collectors.toList());
    }

    /**
     * Sets listen protocol list.
     *
     * @param listenProtocolList the listen protocol list
     */
    @Value("#{'${its.server.local.listens.protocol}'.split(',')}")
    public void setListenProtocolList(List<String> listenProtocolList) {
        if (null == listenProtocolList) {
            return;
        }

        if (listenProtocolList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.listenProtocolList = listenProtocolList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }

    /**
     * Sets connect ip list.
     *
     * @param connectIPList the connect ip list
     */
    @Value("#{'${its.server.local.connect.ip}'.split(',')}")
    public void setConnectIPList(List<String> connectIPList) {
        if (null == connectIPList) {
            return;
        }

        if (connectIPList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.connectIPList = connectIPList.stream().filter(ip -> {
            return ip != null && !ip.isEmpty();
        }).collect(Collectors.toList());
    }

    /**
     * Sets connect port list.
     *
     * @param connectPortList the connect port list
     */
    @Value("#{'${its.server.local.connect.port}'.split(',')}")
    public void setConnectPortList(List<Integer> connectPortList) {
        if (null == connectPortList) {
            return;
        }

        if (connectPortList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.connectPortList = connectPortList.stream().filter(port -> {
            return port != null && port != 0;
        }).collect(Collectors.toList());
    }

    /**
     * Sets connect port list.
     *
     * @param connectPortList the connect port list
     */
    @Value("#{'${its.server.local.connect.localPort}'.split(',')}")
    public void setConnectLocalPortList(List<Integer> connectPortList) {
        if (null == connectPortList) {
            return;
        }

        if (connectPortList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.connectLocalPortList = connectPortList.stream().filter(port -> {
            return port != null && port != 0;
        }).collect(Collectors.toList());
    }


    /**
     * Sets connect protocol list.
     *
     * @param connectProtocolList the connect protocol list
     */
    @Value("#{'${its.server.local.connect.protocol}'.split(',')}")
    public void setConnectProtocolList(List<String> connectProtocolList) {
        if (null == connectProtocolList) {
            return;
        }

        if (connectProtocolList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.connectProtocolList = connectProtocolList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }


    @Value("#{'${its.server.local.comm.address}'.split(',')}")
    public void setCommAddressList(List<String> addressList) {
        if (null == addressList) {
            return;
        }

        if (addressList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.commAddressList = addressList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }

    @Value("#{'${its.server.local.comm.protocol}'.split(',')}")
    public void setCommProtocolList(List<String> commList) {
        if (null == commList) {
            return;
        }

        if (commList.isEmpty()) {
            return;
        }

        ITSSocketConfigure.commProtocolList = commList.stream().filter(protocol -> {
            return protocol != null && !protocol.isEmpty();
        }).collect(Collectors.toList());
    }

}

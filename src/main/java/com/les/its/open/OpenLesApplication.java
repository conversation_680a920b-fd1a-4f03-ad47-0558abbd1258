package com.les.its.open;


import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;



/**
 * -Xms2048m -Xmx2048m
 * -XX:NewSize=1600m -XX:MaxNewSize=1600m
 * -XX:MaxDirectMemorySize=1024m
 * -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
 * -Xloggc:gc.log -XX:+UseGCLogFileRotation
 * -XX:NumberOfGCLogFiles=100
 * -XX:GCLogFileSize=10m
 * -XX:+PrintGCApplicationStoppedTime
 * -XX:+PrintGCApplicationConcurrentTime
 * -XX:+HeapDumpOnOutOfMemoryError
 * 一行数据项
 * -Xms2048m -Xmx2048m -XX:NewSize=1600m -XX:MaxNewSize=1600m -XX:MaxDirectMemorySize=1024m -XX:+PrintGCDetails -XX:+PrintGCTimeStamps  -Xloggc:gc.log -XX:+UseGCLogFileRotation  -XX:NumberOfGCLogFiles=100 -XX:GCLogFileSize=10m -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCApplicationConcurrentTime  -XX:+HeapDumpOnOutOfMemoryError
 */
@SpringBootApplication
@EnableConfigurationProperties
@ComponentScan({"com.myweb", "com.les.its"})
@EnableScheduling
public class OpenLesApplication {

    public static void main(String[] args) {

        SpringApplication.run(OpenLesApplication.class, args);
    }

    @Bean
    public JPAQueryFactory jpaQueryFactory(EntityManager entityManager) {
        return new JPAQueryFactory(entityManager);
    }
}

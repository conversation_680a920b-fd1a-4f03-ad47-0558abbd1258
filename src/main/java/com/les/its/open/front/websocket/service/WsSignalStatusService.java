package com.les.its.open.front.websocket.service;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.front.websocket.bean.ResponseCode;
import com.les.its.open.front.websocket.bean.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WsSignalStatusService {

    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    @Autowired
    private WsMessageService wsMessageService;

    @Autowired
    private ControllerAgentService controllerAgentService;

    /**
     * 广播数据项变更
     */
    @Async(GlobalConfigure.WEBSOCKET_EXECUTOR)
    @Scheduled(fixedDelay = 1, initialDelay = 10, timeUnit = TimeUnit.SECONDS)
    public void sendSignalStatus()
    {

        String controllerId = wsMessageService.getMonitorParam().getControllerId();
        if(controllerId == null || !wsMessageService.isMonitor(controllerId)){
            return;
        }

        Optional<ControllerAgent> controllerAgentOp = controllerAgentService.getControllerAgent(controllerId);
        if(controllerAgentOp.isPresent()){
            ResponseMessage message = ResponseMessage.builder().id(ResponseCode.GAT1049_SIGNAL_STATUS.code())
                    .data(JSONObject.toJSONString(controllerAgentOp.get().getControllerStatusList())).build();
            simpMessagingTemplate.convertAndSend(WsMessageService.BROADCAST_DESTINATION,message);
        }
    }


}

package com.les.its.open.front.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class UserService {

    public static String ADMIN_TOKEN = "ADMIN_TOKEN";
    public static final String EDITOR_TOKEN = "EDITOR_TOKEN";
    public static final Map<String, SimmUser> userMap = new ConcurrentHashMap<>();

    static {
        List<String> roles2 = new ArrayList<>();
        roles2.add("editor");
        SimmUser editorUser = SimmUser.builder()
                .roles(roles2)
                .introduction("I am a editor")
                .avatar("https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif")
                .name("editor").build();
        userMap.put(EDITOR_TOKEN, editorUser);
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SimmUser {
        private List<String> roles;
        private String introduction;
        private String avatar;
        private String name;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Token {
        private String token;
    }

    /**
     * 随机生成的tokenId
     * @return
     */
    private String generateRandomToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }


    public void addAdmin(){
        ADMIN_TOKEN = generateRandomToken();

        List<String> roles = new ArrayList<>();
        roles.add("admin");
        SimmUser adminUser = SimmUser.builder()
                .roles(roles)
                .introduction("I am a super administrator")
                .avatar("https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif")
                .name("supper admin").build();
        userMap.clear();
        userMap.put(ADMIN_TOKEN, adminUser);
    }


    /**
     * 判定是否正常登录
     * @param username
     * @param password
     * @return
     */
    public Optional<Token> login(String username, String password){
        if(username.equalsIgnoreCase("les") && password.equalsIgnoreCase("4434117")){
            addAdmin();
            return Optional.of(Token.builder().token(ADMIN_TOKEN).build());
        }else{
            return Optional.empty();
        }
    }

    /**
     * 获取用户信息
     * @param token
     * @return
     */
    public Optional<SimmUser> getUser(String token){
        return Optional.ofNullable(userMap.get(token));
    }


    /**
     * 用户登出
     * @param token
     * @return
     */
    public boolean logout(String token){
        return userMap.remove(token) != null;
    }

}

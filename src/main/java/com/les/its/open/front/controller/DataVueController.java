package com.les.its.open.front.controller;

import com.les.ads.ds.ReturnEntity;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.bussiness.service.LocalSignalCacheService;
import com.les.its.open.netty.ChannelHolder;
import com.les.its.open.protocol.common.OuterProtocolType;
import com.les.its.open.protocol.openles.bean.CrossDataDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2022/4/8 15:52
 */
@RestController
@RequestMapping("/data")
public class DataVueController {
    @Autowired
    private CrossingService crossingService;
    @Autowired
    private ControllerService controllerService;
    @Autowired
    private LocalSignalCacheService localSignalCacheService;

    /**
     * 读取莱斯路口数据项
     *
     * @return
     */
    @GetMapping("/lesCrossing")
    public ReturnEntity<?> getLesCrossing(@RequestParam(required = false) String name) {
        List<CrossingService.CrossingBaseInfo> crossingBaseInfos = crossingService.getCrossingBaseInfoMap().values().stream().collect(Collectors.toList());
        if (name != null && !name.isEmpty()) {
            crossingBaseInfos = crossingBaseInfos.stream().filter(
                    crossingBaseInfo -> crossingBaseInfo.getName() != null && crossingBaseInfo.getName().contains(name)
            ).collect(Collectors.toList());
        }
        return new ReturnEntity<>(true,
                crossingBaseInfos.stream().sorted((info1, info2) -> info1.getCrossingId().compareToIgnoreCase(info2.getCrossingId()))
                        .collect(Collectors.toList()));
    }

    /**
     * 读取莱斯信号机数据项
     *
     * @return
     */
    @GetMapping("/lesController")
    public ReturnEntity<?> getLesController(@RequestParam(required = false) String name) {
        List<ControllerBaseInfo> controllerBaseInfos = controllerService.getSignalBaseInfoMap().values().stream().collect(Collectors.toList());
        if (name != null && !name.isEmpty()) {
            controllerBaseInfos = controllerBaseInfos.stream().filter(
                    signalBaseInfo -> signalBaseInfo.getSignalId() != null && signalBaseInfo.getSignalId().contains(name)
            ).collect(Collectors.toList());
        }

        //查询信号机链路状态
        if(!controllerBaseInfos.isEmpty()){

            controllerBaseInfos.forEach(
                    controllerBaseInfo -> {
                        Optional<ChannelHolder.ChannelInfo> channelInfo = ChannelHolder.getChannelInfo(controllerBaseInfo.getIp(),
                                0,
                                OuterProtocolType.OPENNLES.value());

                        //链路存在且当前为激活状态
                        if (channelInfo.isPresent() && channelInfo.get().getChannel().isActive()) {
                            controllerBaseInfo.setLink(true);
                        }else {
                            controllerBaseInfo.setLink(false);
                        }
                    }
            );

        }

        return new ReturnEntity<>(true, controllerBaseInfos.stream().sorted((info1, info2) -> info1.getSignalId().compareToIgnoreCase(info2.getSignalId()))
                .collect(Collectors.toList()));
    }


    @GetMapping("/crossing1049")
    @Transactional
    @Operation(summary = "路口参数")
    public ReturnEntity<?> getCrossings(@RequestParam(required = false) String name) {
        List<CrossDataDTO> crossParamList = new ArrayList<>();
        Optional<Map<String, Map<String, CrossDataDTO>>> ht1049SignalCacheServiceData = localSignalCacheService.getData(0, CrossDataDTO.class);
        if (ht1049SignalCacheServiceData.isPresent()) {
            Map<String, Map<String, CrossDataDTO>> stringMapMap = ht1049SignalCacheServiceData.get();
            //过滤掉进行转换后的数据项
            stringMapMap.keySet().stream().filter(
                    id -> {
                        return true;
                    }
            ).forEach(
                    id -> {
                        List<CrossDataDTO> crossParams = stringMapMap.get(id).values().stream().collect(Collectors.toList());
                        if (name != null && !name.isEmpty()) {
                            crossParams = crossParams.stream().filter(
                                    crossParam -> crossParam != null && crossParam.getName().contains(name)
                            ).collect(Collectors.toList());
                        }

                        crossParams.stream().forEach(
                                crossDataDTO -> {
                                    crossDataDTO.setCrossID(String.valueOf(crossDataDTO.getCrossingId()));
                                    crossDataDTO.setCrossName(crossDataDTO.getName());
                                }
                        );

                        crossParamList.addAll(crossParams);
                    }
            );

        }


        return new ReturnEntity<>(true, crossParamList.stream().sorted((info1, info2) -> info1.getCrossID().compareToIgnoreCase(info2.getCrossID()))
                .collect(Collectors.toList()));
    }

}

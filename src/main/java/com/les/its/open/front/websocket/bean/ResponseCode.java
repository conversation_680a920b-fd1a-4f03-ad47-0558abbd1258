package com.les.its.open.front.websocket.bean;

/**
 * @ClassName: ResponseCode
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/21 15:22
 */
public enum ResponseCode {

    /**
     * 基础展示信息
     */
    DASHBOARD("50001","基础展示信息"),
    /**
     * 链路状态信息
     */
    LINKINFO("50002","链路状态信息"),
    /**
     * 入口报文数据项
     */
    INDEBUGMESAGE("50003","入口报文数据项"),
    /**
     * 出口报文数据项
     */
    OUTDEBUGMESAGE("50004","出口报文数据项"),


    GAT1049_SND("60001", "系统发送数据项"),
    GAT1049_RCV("60002", "系统接收数据项"),
    GAT1049_LINK("60003", "系统状态"),
    GAT1049_SIGNAL_STATUS("60004", "信号机状态"),

    GAT1049_SND_NATS("60005", "NATS系统发送数据项"),
    GAT1049_RCV_NATS("60006", "NATS系统接收数据项"),

    GAT1049_PHASE_STATUS("60007", "信号机相位状态"),
    GAT1049_STAGE_STATUS("60008", "信号机阶段状态"),

    GAT1049_RTT("60009", "链路质量"),
    GAT1049_LINK_STATUS("60010", "链路状态")

    ;

    private String code;
    private String message;

    ResponseCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }

}

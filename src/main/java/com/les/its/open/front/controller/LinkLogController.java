package com.les.its.open.front.controller;

import com.les.its.open.bussiness.bean.LinkLogEntity;
import com.les.its.open.bussiness.process.LinkLogProcess;
import com.les.its.open.front.controller.dto.TimeQuery;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("openles")
@Slf4j
public class LinkLogController {

    private final LinkLogProcess linkLogProcess;

    public LinkLogController(LinkLogProcess linkLogProcess) {
        this.linkLogProcess = linkLogProcess;
    }


    /**
     * 查询历史链路数据项
     * @param controllerId
     * @param timeQuery
     * @return
     */
    @PostMapping("/query/link/{controllerId}")
    public JsonResult<?> linkLogs(@PathVariable String controllerId, @RequestBody TimeQuery timeQuery) {
        List<LinkLogEntity> linkLogEntities = linkLogProcess.queryLinkLog(controllerId, timeQuery);
        return new JsonResult<>(true, "20000", "获取成功", linkLogEntities);

    }

    /**
     * 删除过期数据项
     * @return
     */
    @PostMapping("/rm/link")
    public JsonResult<?> rmOld(){
        linkLogProcess.cleanupExpiredDataLink();
        return new JsonResult<>(true, "20000", "删除成功", "");
    }

}

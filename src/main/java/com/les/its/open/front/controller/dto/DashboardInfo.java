package com.les.its.open.front.controller.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: DashBoardInfo
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/21 15:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardInfo {
    private int total;
    private int connected;
    private int log;
    private String time;

    private int radar;
    private int positiveRadar;
    private int microwaveRadar;
    private int radarvideoRadar;
    private int holographic;

    private int radarActive;
    private int positiveRadarActive;
    private int microwaveRadarActive;
    private int radarvideoRadarActive;
    private int holographicActive;
}
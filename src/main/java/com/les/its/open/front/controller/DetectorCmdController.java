package com.les.its.open.front.controller;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.cmd.TabDetectorControlGroupActuation;
import com.les.its.open.area.juncer.msg.cmd.TabDetectorReset;
import com.les.its.open.area.message.MqMsgSimSyncProcess;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.IntStream;

@RestController
@RequestMapping("openles/detector")
@Slf4j
public class DetectorCmdController {

    @Autowired
    private MqMsgSimSyncProcess mqMsgSimSyncProcess;

    /**
     * 使能信号机检测器
     * @return
     */
    @PostMapping("/enable/{controllerId}/{flag}")
    public JsonResult<?> enable(@PathVariable String controllerId, @PathVariable int flag, @RequestBody Map<Integer, Integer> detectors) {

        if(detectors == null || detectors.isEmpty()){
            return new JsonResult<>(false, "50000", "没有设置检测器", "");
        }

        List<String> datas = new ArrayList<>();
        TabDetectorControlGroupActuation tabDetectorControlGroupActuation = new TabDetectorControlGroupActuation();
        tabDetectorControlGroupActuation.setDetectorControlGroupActuations(new ArrayList<>());
        tabDetectorControlGroupActuation.setFlags(new ArrayList<>());
        IntStream.rangeClosed(1, 64).forEach(
                i -> {

                    if(detectors.containsKey(i)){
                        tabDetectorControlGroupActuation.getFlags().add(detectors.get(i));
                        tabDetectorControlGroupActuation.getDetectorControlGroupActuations().add(1);
                    }else{
                        tabDetectorControlGroupActuation.getFlags().add(0);
                        tabDetectorControlGroupActuation.getDetectorControlGroupActuations().add(0);
                    }
                }
        );

        datas.add(JSONObject.toJSONString(tabDetectorControlGroupActuation));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_DETECTOR_CONTROL_GROUP_ACTUATION.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "检测器控制成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "检测器控制异常", "");
    }



    /**
     * 重置信号机检测器
     * @return
     */
    @PostMapping("/disable/{controllerId}")
    public JsonResult<?> disable(@PathVariable String controllerId) {

        List<String> datas = new ArrayList<>();
        TabDetectorReset tabDetectorReset = new TabDetectorReset();
        tabDetectorReset.setDetectorResets(new ArrayList<>());

        IntStream.rangeClosed(1, 64).forEach(
                i -> {
                    tabDetectorReset.getDetectorResets().add(1);
                }
        );

        datas.add(JSONObject.toJSONString(tabDetectorReset));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_DETECTOR_RESET.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "检测器重置成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "检测器重置异常", "");
    }

}

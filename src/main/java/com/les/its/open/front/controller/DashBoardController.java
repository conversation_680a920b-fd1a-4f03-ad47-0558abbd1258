package com.les.its.open.front.controller;

import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.front.controller.dto.DashboardInfo;
import com.les.its.open.netty.ChannelHolder;
import com.les.its.open.protocol.common.OuterProtocolType;
import com.les.its.open.task.MyStartupRunner;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

@RestController
@RequestMapping("dashboard")
@Slf4j
public class DashBoardController {

    @Autowired
    private ControllerService controllerService;


    /**
     * @return
     */
    @RequestMapping("info")
    public JsonResult<DashboardInfo> list() {
        DashboardInfo dashboardInfo = new DashboardInfo();

        //服务启动时间
        DateTimeFormatter format = DateTimeFormatter.ofPattern("MM/dd HH:mm:ss");
        String time = MyStartupRunner.APP_START_TIME.format(format);
        dashboardInfo.setTime(time);

        dashboardInfo.setLog(ChannelHolder.getLinkLogQueue().size());

        //配置参数以及
        controllerService.getSignalBaseInfoMap().values().forEach(signalBaseInfo -> {

            dashboardInfo.setTotal(dashboardInfo.getTotal() + 1);

            Optional<ChannelHolder.ChannelInfo> channelInfo = ChannelHolder.getChannelInfo(signalBaseInfo.getIp(),
                    0,
                    OuterProtocolType.OPENNLES.value());

            //链路存在且当前为激活状态
            if (channelInfo.isPresent() && channelInfo.get().getChannel().isActive()) {
                dashboardInfo.setConnected(dashboardInfo.getConnected() + 1);
            }
        });


        dashboardInfo.setPositiveRadar(dashboardInfo.getTotal());
        dashboardInfo.setPositiveRadarActive(dashboardInfo.getConnected());

        return new JsonResult<>(true, "0", "请求基础数据正确", dashboardInfo);
    }

}




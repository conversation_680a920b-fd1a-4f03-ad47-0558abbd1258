package com.les.its.open.front.controller;

import com.les.its.open.area.message.MqMessageProcess;
import com.les.its.open.area.message.MqMsgSimSyncProcess;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.status.StatusMqObject;
import com.les.its.open.area.net.msg.MsgSource;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.area.net.msg.MsgTypeCat;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabCmdService;
import com.les.its.open.bussiness.service.rtt.RTTService;
import com.les.its.open.front.controller.dto.MsgDto;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("openles")
@Slf4j
public class OpenLesController {

    private final WsMessageService wsMessageService;

    private final MqMessageProcess mqMessageProcess;

    private final TabCmdService tabCmdService;

    private final MqMsgSimSyncProcess mqMsgSimSyncProcess;

    private final RTTService rttService;

    public OpenLesController(WsMessageService wsMessageService,
                             MqMessageProcess mqMessageProcess,
                             TabCmdService tabCmdService,
                             MqMsgSimSyncProcess mqMsgSimSyncProcess,
                             RTTService rttService) {
        this.wsMessageService = wsMessageService;
        this.mqMessageProcess = mqMessageProcess;
        this.tabCmdService = tabCmdService;
        this.mqMsgSimSyncProcess = mqMsgSimSyncProcess;
        this.rttService = rttService;
    }

    /**
     * 更新监控路口数据项
     * @param controllerId
     * @param flag
     * @return
     */
    @PostMapping("/monitor/{controllerId}/{flag}")
    public JsonResult<?> monitor(@PathVariable String controllerId, @PathVariable int flag) {
        wsMessageService.updateMonitorParam(controllerId, flag);
        wsMessageService.updateHbTime();
        return new JsonResult<>(true, "20000", flag == 1? "开始监控成功" : "停止监控成功", "");
    }

    @GetMapping("/monitor/heartbeat")
    public JsonResult<?> heartbeat() {
        wsMessageService.updateHbTime();
        log.info("收到web心跳更新请求");
        return new JsonResult<>(true, "20000", "心跳更新成功", "");
    }

    /**
     * 获取系统数据项
     * @return
     */
    @GetMapping("/monitor/msg/{type}")
    public JsonResult<?> monitorRev(@PathVariable int type) {

       List<MsgDto> msgDtoList = new ArrayList<>();

        Arrays.asList(MsgType.values()).forEach(
                msgType ->
                {
                    Optional<MsgTypeCat> msgTypeCatOp = MsgTypeCat.parseMsgTypeCat(msgType);
                     if(msgTypeCatOp.isEmpty()){
                         return;
                     }

                     //非加载调看参数
                     if(msgTypeCatOp.get() != MsgTypeCat.CAT_PARAM) {
                         MsgDto msgDto = new MsgDto();
                         msgDto.setType(msgTypeCatOp.get().getDescription());
                         msgDto.setLabel(msgType.getDescription());
                         msgDto.setValue(msgType.getCode());
                         msgDto.setHexValue(String.format("0x%08x", msgType.getCode()));
                         msgDto.setEnglishLabel(msgType.name());
                         msgDto.setSource(msgType.getMsgSource().getCode());
                         msgDtoList.add(msgDto);
                     }else{
                         //加载调看参数扩展
                         Arrays.stream(ParamMsgType.values()).forEach(
                             paramMsgType -> {
                                 MsgDto msgDto = new MsgDto();
                                 msgDto.setType(msgTypeCatOp.get().getDescription());
                                 msgDto.setLabel(msgType.getDescription() + "-" + paramMsgType.getDescription());
                                 msgDto.setValue((msgType.getCode() & 0xffff0000) | paramMsgType.getCode());
                                 msgDto.setHexValue(String.format("0x%08x", msgDto.getValue()));
                                 msgDto.setEnglishLabel(paramMsgType.name());
                                 msgDto.setSource(msgType.getMsgSource().getCode());
                                 msgDtoList.add(msgDto);
                             }
                         );

                     }
                }
        );

        msgDtoList.sort(Comparator.comparingInt(MsgDto::getValue));

        return new JsonResult<>(true, "20000", "获取成功", msgDtoList);
    }

    /**
     * 获取配置数据类型
     * @return
     */
    @GetMapping("/monitor/lookload")
    public JsonResult<?> lookload() {
        List<MsgDto> msgDtoList = new ArrayList<>();
        //加载调看参数扩展
        Arrays.stream(ParamMsgType.values()).forEach(
                paramMsgType -> {

                    if(paramMsgType == ParamMsgType.PARAM_UNKNOWN){
                        return;
                    }

                    MsgDto msgDto = new MsgDto();
                    msgDto.setType(paramMsgType.getDescription());
                    msgDto.setLabel(String.format("0x%04x-%s", paramMsgType.getCode(), paramMsgType.getDescription()));
                    msgDto.setValue(paramMsgType.getCode());
                    msgDto.setHexValue(String.format("0x%04x", paramMsgType.getCode()));
                    msgDto.setEnglishLabel(paramMsgType.name());
                    msgDtoList.add(msgDto);
                }
        );
        msgDtoList.sort(Comparator.comparingInt(MsgDto::getValue));
        return new JsonResult<>(true, "20000", "获取成功", msgDtoList);
    }


    /**
     * 获取系统NATS交互数据项
     * @return
     */
    @GetMapping("/monitor/msgNats/{type}")
    public JsonResult<?> monitorNats(@PathVariable int type) {

        List<MsgDto> msgDtoList = new ArrayList<>();


        {

            final String statusType = "状态类";
            Arrays.stream(StatusMqObject.values()).forEach(
                    statusMqObject -> {

                        if(statusMqObject.objectId().isEmpty()){
                            return;
                        }

                        MsgDto msgDto = new MsgDto();
                        msgDto.setType(statusType);
                        msgDto.setLabel(statusMqObject.des());
                        msgDto.setValue(Integer.valueOf(statusMqObject.objectId()));
                        msgDto.setHexValue(statusMqObject.objectId());
                        msgDto.setEnglishLabel(statusMqObject.name());
                        msgDto.setSource(MsgSource.MSG_SRC_SYS.getCode());
                        msgDtoList.add(msgDto);
                    }
            );
        }

        {

            final String statusType = "命令类";
            Arrays.stream(CmdMqObject.values()).forEach(
                    statusMqObject -> {
                        if(statusMqObject.objectId().isEmpty()){
                            return;
                        }

                        {
                            MsgDto msgDto = new MsgDto();
                            msgDto.setType(statusType);
                            msgDto.setLabel(statusMqObject.des());
                            msgDto.setValue(Integer.valueOf(statusMqObject.objectId()));
                            msgDto.setHexValue(statusMqObject.objectId());
                            msgDto.setEnglishLabel(statusMqObject.name());
                            msgDto.setSource(MsgSource.MSG_SRC_SYS.getCode());
                            msgDtoList.add(msgDto);
                        }

                        {
                            MsgDto msgDto = new MsgDto();
                            msgDto.setType(statusType);
                            msgDto.setLabel(statusMqObject.des());
                            msgDto.setValue(Integer.valueOf(statusMqObject.objectId()));
                            msgDto.setHexValue(statusMqObject.objectId());
                            msgDto.setEnglishLabel(statusMqObject.name());
                            msgDto.setSource(MsgSource.MSG_SRC_SIGNAL.getCode());
                            msgDtoList.add(msgDto);
                        }
                    }
            );
        }


        {
            mqMessageProcess.getMqMsgBaseHandlerMap().values().stream().filter(
                    mqMsgBaseHandler -> Integer.valueOf(mqMsgBaseHandler.getObjectId()).intValue() > 10000
                    && Integer.valueOf(mqMsgBaseHandler.getObjectId()).intValue() < 11000
            ).forEach(
                    mqMsgBaseHandler -> {


                        Optional<LookLoadMqObject> lookLoadMqObjectOp = LookLoadMqObject.getType(mqMsgBaseHandler.getObjectId());

                        {
                            MsgDto msgDto = new MsgDto();
                            msgDto.setType("参数类");
                            msgDto.setLabel(lookLoadMqObjectOp.isPresent() ?
                                    lookLoadMqObjectOp.get().des() : mqMsgBaseHandler.dataType().getSimpleName());
                            msgDto.setValue(Integer.valueOf(mqMsgBaseHandler.getObjectId()));
                            msgDto.setHexValue(mqMsgBaseHandler.getObjectId());
                            msgDto.setEnglishLabel(mqMsgBaseHandler.dataType().getSimpleName());
                            msgDto.setSource(MsgSource.MSG_SRC_SYS.getCode());
                            msgDtoList.add(msgDto);
                        }

                        {
                            MsgDto msgDto = new MsgDto();
                            msgDto.setType("参数类");
                            msgDto.setLabel(lookLoadMqObjectOp.isPresent() ?
                                    lookLoadMqObjectOp.get().des() : mqMsgBaseHandler.dataType().getSimpleName());
                            msgDto.setValue(Integer.valueOf(mqMsgBaseHandler.getObjectId()));
                            msgDto.setHexValue(mqMsgBaseHandler.getObjectId());
                            msgDto.setEnglishLabel(mqMsgBaseHandler.dataType().getSimpleName());
                            msgDto.setSource(MsgSource.MSG_SRC_SIGNAL.getCode());
                            msgDtoList.add(msgDto);
                        }
                    }
            );
        }


        msgDtoList.sort(Comparator.comparingInt(MsgDto::getValue));

        return new JsonResult<>(true, "20000", "获取成功", msgDtoList);
    }




    /**
     * 获取配置数据类型
     * @return
     */
    @GetMapping("/monitor/lookloadNats")
    public JsonResult<?> lookloadNats() {
        List<MsgDto> msgDtoList = new ArrayList<>();
        {
            mqMessageProcess.getMqMsgBaseHandlerMap().values().stream().filter(
                    mqMsgBaseHandler -> Integer.valueOf(mqMsgBaseHandler.getObjectId()).intValue() > 10000
                            && Integer.valueOf(mqMsgBaseHandler.getObjectId()).intValue() < 11000
            ).forEach(
                    mqMsgBaseHandler -> {


                        Optional<LookLoadMqObject>
                                lookLoadMqObjectOp = LookLoadMqObject.getType(mqMsgBaseHandler.getObjectId());

                        {
                            MsgDto msgDto = new MsgDto();
                            msgDto.setType("参数类");
                            msgDto.setLabel(String.format("%s-%s", mqMsgBaseHandler.getObjectId(),
                                    (lookLoadMqObjectOp.isPresent() ? lookLoadMqObjectOp.get().des(): mqMsgBaseHandler.dataType().getSimpleName())));
                            msgDto.setValue(Integer.valueOf(mqMsgBaseHandler.getObjectId()));
                            msgDto.setHexValue(mqMsgBaseHandler.getObjectId());
                            msgDto.setEnglishLabel(mqMsgBaseHandler.dataType().getSimpleName());
                            msgDto.setSource(MsgSource.MSG_SRC_SYS.getCode());
                            msgDtoList.add(msgDto);
                        }
                    }
            );
        }
        msgDtoList.sort(Comparator.comparingInt(MsgDto::getValue));
        return new JsonResult<>(true, "20000", "获取成功", msgDtoList);
    }


    /**
     * 中心系统模拟数据
     * @param mqMessage
     * @return
     */
    @PostMapping("/center")
    public JsonResult<?> monitor(@RequestBody String mqMessage) {
        mqMessageProcess.consumer(mqMessage);
        return new JsonResult<>(true, "20000", "模拟发送成功", "");
    }


    /**
     * 取消控制
     * @param crossingSeqNo
     * @return
     */
    @PostMapping("/cancel/{controllerId}/{crossingSeqNo}")
    public JsonResult<?> cancel(@PathVariable String controllerId,
                                @PathVariable int crossingSeqNo) {
        Optional<?> cancelled = tabCmdService.cancel(controllerId, crossingSeqNo);
        if(cancelled.isPresent()) {
            return new JsonResult<>(true, "20000", "已发送信号机恢复控制", cancelled.get());
        }else{
            return new JsonResult<>(false, "20001", "发送信号机恢复控制失败", "");
        }
    }


    @GetMapping("/nats/{controllerId}/{id}")
    public JsonResult<?> natsParam(@PathVariable String controllerId,
                                   @PathVariable String id) {
        Optional<Object> natsParam = mqMsgSimSyncProcess.getNatsParam(controllerId, id);
        if(natsParam.isPresent()){
            return new JsonResult<>(true, "20000", "获取成功", natsParam.get());
        }else{
            return new JsonResult<>(false, "20001", "获取失败", "");
        }
    }




}

package com.les.its.open.front.websocket.controller;


import com.les.its.open.front.websocket.bean.RequestMessage;
import com.les.its.open.front.websocket.bean.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

/**
 * @ClassName: MessageController
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/21 9:38
 */
@Controller
@Slf4j
public class MessageController {

    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;


    /**
     * 处理前端的各种报文数据项
     * @param message
     * @return
     */
    public ResponseMessage messsageProcess(RequestMessage message)
    {
        return ResponseMessage.builder().id("111").data("{}").build();
    }

    @MessageMapping("/request")
    public void request(RequestMessage message) {
        log.info("收到来自前端的数据项-{}", message);
        simpMessagingTemplate.convertAndSendToUser(message.getGuid(),
                "", messsageProcess(message));
    }
}

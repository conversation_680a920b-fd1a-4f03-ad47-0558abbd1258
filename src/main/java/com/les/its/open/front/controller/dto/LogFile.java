package com.les.its.open.front.controller.dto;

import lombok.Data;

import java.nio.file.Path;
import java.time.LocalDate;

@Data
public class LogFile {
    public enum Type {
        CURRENT, COMPRESSED
    }

    private final Path path;
    private final Type type;
    private final LocalDate date;

    public LogFile(Path path, Type type, LocalDate date) {
        this.path = path;
        this.type = type;
        this.date = date;
    }

    // Getters
    public Path getPath() { return path; }
    public Type getType() { return type; }
    public LocalDate getDate() { return date; }

}

package com.les.its.open.front.controller;

import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.front.controller.dto.LogFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/openles/logs")
@Slf4j
public class LogController {

    @Value("${log.base.path:daa-log}")
    private String logBasePath;

    private final Executor asyncExecutor = Executors.newFixedThreadPool(5);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB限制


    @Autowired
    private ControllerService controllerService;

    /**
     * 获取指定信号机和日期范围的日志文件
     *
     * @param signalId 信号机ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param async 是否异步处理（可选）
     * @return 压缩后的日志文件
     */
    @GetMapping("/juncer/{signalId}")
    public ResponseEntity<?> getSignalLogs(
            @PathVariable String signalId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "false") boolean async) {

        // 参数验证
        validateRequest(signalId, startDate, endDate);

        if (async) {
            return handleAsyncRequest(signalId, startDate, endDate);
        } else {
            return handleSyncRequest(signalId, startDate, endDate);
        }
    }

    /**
     * 同步处理请求
     */
    private ResponseEntity<InputStreamResource> handleSyncRequest(
            String signalId, LocalDate startDate, LocalDate endDate) {
        try {
            ByteArrayOutputStream zipStream = createZipArchive(signalId, startDate, endDate);

            ByteArrayInputStream inputStream = new ByteArrayInputStream(zipStream.toByteArray());
            InputStreamResource resource = new InputStreamResource(inputStream);

            String filename = String.format("controller_logs_%s_%s_to_%s.zip",
                    signalId, startDate.format(DATE_FORMATTER), endDate.format(DATE_FORMATTER));

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .contentLength(zipStream.size())
                    .body(resource);

        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                    "处理日志文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 异步处理请求（适用于大文件）
     */
    private ResponseEntity<Map<String, String>> handleAsyncRequest(
            String signalId, LocalDate startDate, LocalDate endDate) {

        String taskId = UUID.randomUUID().toString();

        CompletableFuture.supplyAsync(() -> {
            try {
                return createZipArchive(signalId, startDate, endDate);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, asyncExecutor).thenAccept(zipStream -> {
            // 这里可以将文件保存到临时目录或云存储
            // 并通知前端下载链接准备就绪
            saveTemporaryFile(taskId, zipStream);
        });

        Map<String, String> response = new HashMap<>();
        response.put("taskId", taskId);
        response.put("status", "processing");
        response.put("message", "日志文件正在处理中，请稍后查询状态");

        return ResponseEntity.accepted().body(response);
    }

    /**
     * 创建ZIP压缩包
     */
    private ByteArrayOutputStream createZipArchive(
            String signalId, LocalDate startDate, LocalDate endDate) throws IOException {


        Optional<ControllerBaseInfo> signalInfo = controllerService.getSignalInfo(signalId);

        if(signalInfo.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                    "信号机不存在: " + signalId);
        }

        String subPath = String.format("%02d-%03d-%s",
                signalInfo.get().getNoArea(),
                signalInfo.get().getNoJunc(),
                signalInfo.get().getIp());


        String messagePath = String.format("%s", signalId);

        Path signalDir = Paths.get(logBasePath, "juncer", subPath);
        if (!Files.exists(signalDir)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                    "信号机目录不存在: " + subPath);
        }

        List<LogFile> logFiles = findLogFiles("device", signalDir, startDate, endDate);
        if (logFiles.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                    "在指定日期范围内未找到日志文件");
        }

        {
            Path messageDir = Paths.get(logBasePath, "message", messagePath);
            if (Files.exists(messageDir)) {
                List<LogFile> logFilesMessage = findLogFiles("control", messageDir, startDate, endDate);
                if(!logFilesMessage.isEmpty()){
                    logFiles.addAll(logFilesMessage);
                }
            }
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try (ZipOutputStream zos = new ZipOutputStream(baos)) {
            for (LogFile logFile : logFiles) {
                addFileToZip(zos, logFile);
            }
        }

        return baos;
    }

    /**
     * 查找指定日期范围内的日志文件
     */
    private List<LogFile> findLogFiles(String prefix, Path signalDir, LocalDate startDate, LocalDate endDate)
            throws IOException {

        List<LogFile> logFiles = new ArrayList<>();

        // 添加当前日志文件
        Path currentLog = signalDir.resolve(prefix + ".log");
        if (Files.exists(currentLog)) {
            logFiles.add(new LogFile(currentLog, LogFile.Type.CURRENT, null));
        }

        // 查找压缩日志文件
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(signalDir, prefix + "-*.log.gz")) {
            for (Path file : stream) {
                String filename = file.getFileName().toString();
                LocalDate fileDate = extractDateFromFilename(filename);

                if (fileDate != null &&
                        !fileDate.isBefore(startDate) &&
                        !fileDate.isAfter(endDate)) {
                    logFiles.add(new LogFile(file, LogFile.Type.COMPRESSED, fileDate));
                }
            }
        }

        // 按日期排序
        logFiles.sort((a, b) -> {
            if (a.getDate() == null) return -1;
            if (b.getDate() == null) return 1;
            return a.getDate().compareTo(b.getDate());
        });

        return logFiles;
    }

    /**
     * 从文件名提取日期
     */
    private LocalDate extractDateFromFilename(String filename) {
        try {
            // 解析 device-2025-05-27.1.log.gz 格式
            String[] parts = filename.split("-");
            if (parts.length >= 4) {
                String dateStr = parts[1] + "-" + parts[2] + "-" + parts[3].split("\\.")[0];
                return LocalDate.parse(dateStr, DATE_FORMATTER);
            }
        } catch (Exception e) {
            // 忽略解析失败的文件
        }
        return null;
    }

    /**
     * 将文件添加到ZIP
     */
    private void addFileToZip(ZipOutputStream zos, LogFile logFile) throws IOException {
        String entryName = generateZipEntryName(logFile);
        ZipEntry entry = new ZipEntry(entryName);
        zos.putNextEntry(entry);

        if (logFile.getType() == LogFile.Type.COMPRESSED) {
            // 解压gzip文件再添加到zip
            try (GZIPInputStream gzis = new GZIPInputStream(Files.newInputStream(logFile.getPath()));
                 BufferedInputStream bis = new BufferedInputStream(gzis)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = bis.read(buffer)) != -1) {
                    zos.write(buffer, 0, bytesRead);
                }
            }
        } else {
            // 直接添加普通文件
            Files.copy(logFile.getPath(), zos);
        }

        zos.closeEntry();
    }

    /**
     * 生成ZIP条目名称
     */
    private String generateZipEntryName(LogFile logFile) {
        if (logFile.getType() == LogFile.Type.CURRENT) {
            return logFile.getPath().getFileName().toString();
        } else {
            return logFile.getPath().getFileName().toString().replaceAll(".gz", "");
        }
    }

    /**
     * 请求参数验证
     */
    private void validateRequest(String signalId, LocalDate startDate, LocalDate endDate) {
        if (signalId == null || signalId.trim().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "信号机ID不能为空");
        }

        if (startDate == null || endDate == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "开始和结束日期不能为空");
        }

        if (startDate.isAfter(endDate)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "开始日期不能晚于结束日期");
        }

        if (startDate.isBefore(LocalDate.now().minusMonths(6))) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "查询日期范围过长，最多支持6个月");
        }

        // 安全性检查：防止路径遍历攻击
        if (signalId.contains("..") || signalId.contains("/") || signalId.contains("\\")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "信号机ID格式不正确");
        }
    }

    /**
     * 保存临时文件（异步处理时使用）
     */
    private void saveTemporaryFile(String taskId, ByteArrayOutputStream zipStream) {
        try {
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "log-downloads");
            Files.createDirectories(tempDir);

            Path tempFile = tempDir.resolve(taskId + ".zip");
            Files.write(tempFile, zipStream.toByteArray());

            // 可以在这里发送通知给前端，告知文件准备就绪
            // 例如通过WebSocket、消息队列等方式

        } catch (IOException e) {
            // 记录错误日志
            log.error("压缩日志文件异常", e);
        }
    }

    /**
     * 查询异步任务状态
     */
    @GetMapping("/tasks/{taskId}/status")
    public ResponseEntity<Map<String, String>> getTaskStatus(@PathVariable String taskId) {
        Path tempFile = Paths.get(System.getProperty("java.io.tmpdir"), "log-downloads", taskId + ".zip");

        Map<String, String> response = new HashMap<>();
        if (Files.exists(tempFile)) {
            response.put("status", "completed");
            response.put("downloadUrl", "/api/v1/logs/tasks/" + taskId + "/download");
        } else {
            response.put("status", "processing");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 下载异步处理完成的文件
     */
    @GetMapping("/tasks/{taskId}/download")
    public ResponseEntity<InputStreamResource> downloadAsyncFile(@PathVariable String taskId)
            throws IOException {

        Path tempFile = Paths.get(System.getProperty("java.io.tmpdir"), "log-downloads", taskId + ".zip");

        if (!Files.exists(tempFile)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "文件不存在或已过期");
        }

        InputStreamResource resource = new InputStreamResource(Files.newInputStream(tempFile));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"logs_" + taskId + ".zip\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(Files.size(tempFile))
                .body(resource);
    }

}

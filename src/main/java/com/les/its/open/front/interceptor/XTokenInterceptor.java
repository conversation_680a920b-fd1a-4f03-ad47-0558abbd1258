package com.les.its.open.front.interceptor;

import com.les.its.open.front.service.UserService;
import com.myweb.commons.utils.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

@Component
public class XTokenInterceptor implements HandlerInterceptor {

    private static final String X_TOKEN_HEADER = "X-Token";
    private static final String ERROR_MESSAGE = "缺少必要的X-Token请求头";

    @Autowired
    private UserService userService;

    @Value("${app.token.check:false}")
    private boolean checkXToken;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {

        // 检查是否需要验证X-Token
        //if (needsXTokenValidation(request, handler))

        if(checkXToken)
        {
            String xToken = request.getHeader(X_TOKEN_HEADER);

            if (StringUtils.isEmpty(xToken)) {
                handleMissingToken(response);
                return false;
            }

            // 可选：验证Token格式或内容
            if (!isValidToken(xToken)) {
                handleInvalidToken(response);
                return false;
            }
        }else{
            return true;
        }

        return true;
    }

    /**
     * 判断是否需要验证X-Token
     */
    private boolean needsXTokenValidation(HttpServletRequest request, Object handler) {
        // 方式1: 基于URL路径判断
        String requestPath = request.getRequestURI();
        if (requestPath.startsWith("/api/protected/") ||
                requestPath.startsWith("/api/admin/")) {
            return true;
        }

        // 方式2: 基于注解判断
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;

            // 检查方法级注解
            RequireXToken methodAnnotation = handlerMethod.getMethodAnnotation(RequireXToken.class);
            if (methodAnnotation != null) {
                return true;
            }

            // 检查类级注解
            RequireXToken classAnnotation = handlerMethod.getBeanType().getAnnotation(RequireXToken.class);
            if (classAnnotation != null) {
                return true;
            }
        }

        return false;
    }

    /**
     * 验证Token格式（可根据需要自定义）
     */
    private boolean isValidToken(String token) {
        // 示例：检查Token长度和格式
        return userService.getUser(token).isPresent();
    }

    /**
     * 处理缺少Token的情况
     */
    private void handleMissingToken(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        response.setContentType("application/json;charset=UTF-8");

        String jsonResponse = """
            {
                "code": 400,
                "message": "%s",
                "timestamp": %d
            }
            """.formatted(ERROR_MESSAGE, System.currentTimeMillis());

        response.getWriter().write(jsonResponse);
    }

    /**
     * 处理无效Token的情况
     */
    private void handleInvalidToken(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");

        String jsonResponse = """
            {
                "code": 401,
                "message": "无效的X-Token",
                "timestamp": %d
            }
            """.formatted(System.currentTimeMillis());

        response.getWriter().write(jsonResponse);
    }

}

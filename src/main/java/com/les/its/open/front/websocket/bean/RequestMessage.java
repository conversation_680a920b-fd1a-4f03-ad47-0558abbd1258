package com.les.its.open.front.websocket.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: RequestMessage
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/21 9:39
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class RequestMessage {
    private String id;
    /**
     * guid标记socket链路，前端会订阅 /usr/{guid}/ 频道；
     * 前端发送数据需要填写正确guid数据项；
     * 后端接收之后发送数据的时候以guid标记user用户；
     */
    private String guid;
    private String data;
}

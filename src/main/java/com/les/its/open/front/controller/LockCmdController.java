package com.les.its.open.front.controller;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.cmd.*;
import com.les.its.open.area.message.MqMsgSimSyncProcess;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("openles/lock")
@Slf4j
public class LockCmdController {

    @Autowired
    private MqMsgSimSyncProcess mqMsgSimSyncProcess;

    /**
     * 锁定阶段
     * @return
     */
    @PostMapping("/stage/{controllerId}/{subId}/{stageNo}/{seq}/{duration}/{chara}")
    public JsonResult<?> enable(@PathVariable String controllerId, @PathVariable int subId,
                                @PathVariable int stageNo,
                                @PathVariable int seq,
                                @PathVariable int duration,
                                @PathVariable int chara) {

        List<String> datas = new ArrayList<>();
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setControllerId(controllerId);
        tabSetStage.setCrossingSeqNo(subId);
        tabSetStage.setCharacter(chara);
        tabSetStage.setStageSeq(seq);
        tabSetStage.setStageNo(stageNo);
        tabSetStage.setDuration(duration);
        tabSetStage.setExtParams(new ArrayList<>());

        log.error("收到锁定阶段命令{}-{}", controllerId, tabSetStage);

        datas.add(JSONObject.toJSONString(tabSetStage));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_SET_STAGE.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "锁定阶段控制成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "锁定阶段控制异常", "");
    }


    /**
     * 锁定相位
     * @return
     */
    @PostMapping("/phase/{controllerId}/{subId}/{duration}")
    public JsonResult<?> enable(@PathVariable String controllerId, @PathVariable int subId,
                                @PathVariable int duration, @RequestBody List<Integer> phaseIds) {

        List<String> datas = new ArrayList<>();
        TabDwell tabDwell = new TabDwell();
        tabDwell.setControllerId(controllerId);
        tabDwell.setCrossingSeqNo(subId);
        tabDwell.setDuration(duration);

        Long phaseId = 0L;
        for (int i = 0; i < phaseIds.size(); i++) {
            phaseId |= ( 0x01L << (phaseIds.get(i) - 1));
        }
        tabDwell.setPhases(phaseId);

        log.error("收到锁定相位命令{}-{}", controllerId, tabDwell);

        datas.add(JSONObject.toJSONString(tabDwell));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_DWELL.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "锁定相位控制成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "锁定相位控制异常", "");
    }


    /**
     * 特殊控制
     * @return
     */
    @PostMapping("/sp/{controllerId}/{subId}/{controlMode}")
    public JsonResult<?> sp(@PathVariable String controllerId, @PathVariable int subId,
                                @PathVariable int controlMode) {

        List<String> datas = new ArrayList<>();
        TabSpecialMode tabSpecialMode = new TabSpecialMode();
        tabSpecialMode.setControllerId(controllerId);
        tabSpecialMode.setCrossingSeqNo(subId);
        tabSpecialMode.setControlMode(controlMode);

        log.error("收到特殊命令{}-{}", controllerId, tabSpecialMode);

        datas.add(JSONObject.toJSONString(tabSpecialMode));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_SPECIAL_MODE.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "特殊控制成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "特殊控制异常", "");
    }


    /**
     * 周期优化
     * @return
     */
    @PostMapping("/cycle/{controllerId}")
    public JsonResult<?> cycle(@PathVariable String controllerId, @RequestBody TabPlanCmd tabPlanCmd) {

        List<String> datas = new ArrayList<>();


        log.error("收到方案命令{}-{}", controllerId, tabPlanCmd);

        tabPlanCmd.setStageNum(tabPlanCmd.getStageNos().size());
        tabPlanCmd.setPhaseTableNo(1);

        datas.add(JSONObject.toJSONString(tabPlanCmd));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_PLAN_CMD.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "方案控制成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "方案控制异常", "");
    }

    /**
     * 实时优化
     * @return
     */
    @PostMapping("/realtime/{controllerId}")
    public JsonResult<?> realtime(@PathVariable String controllerId, @RequestBody TabRealTimeOptimize tabRealTimeOptimize) {

        List<String> datas = new ArrayList<>();


        log.error("收到实时优化 命令{}-{}", controllerId, tabRealTimeOptimize);

        datas.add(JSONObject.toJSONString(tabRealTimeOptimize));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_REAL_TIME_OPTIMIZE.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "实时优化控制成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "实时优化控制异常", "");
    }
}

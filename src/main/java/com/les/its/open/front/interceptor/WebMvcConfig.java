package com.les.its.open.front.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private XTokenInterceptor xTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(xTokenInterceptor)
                .addPathPatterns("/**")  // 拦截所有API请求
                .excludePathPatterns(
                        "/",
                        "/index.html",
                        "/favicon.ico",
                        "/user/login",       // 排除登录接口
                        "/user/logout",      // 排除登出接口
                        "/static/**",          // 静态文件数据项
                        "/stomp/**" ,
                        "//stomp/**"
                );
    }
}

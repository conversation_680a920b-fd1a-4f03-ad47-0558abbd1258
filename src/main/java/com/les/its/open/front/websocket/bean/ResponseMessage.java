package com.les.its.open.front.websocket.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: ResponseMessage
 * @Description:
 * @Author: king
 * @CreateDate: 2019/5/21 10:06
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class ResponseMessage {
    private String id;
    private String data;
}

package com.les.its.open.front.controller;

import com.alibaba.fastjson.JSONObject;
import com.les.its.open.front.service.UserService;
import com.myweb.commons.persistence.JsonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * @ClassName: UserController
 * @Description: 用于信号控制
 * @Author: king
 * @CreateDate: 2019/5/14 18:29
 */
@RestController
@RequestMapping("user")
public class UserController {


    @Autowired
    private UserService userService;



    @PostMapping("login")
    public JsonResult<?> login(@RequestBody String body) {
        String username = JSONObject.parseObject(body).get("username").toString();
        String password = JSONObject.parseObject(body).get("password").toString();
        Optional<UserService.Token> login = userService.login(username, password);
        if(login.isPresent()){
            return new JsonResult<>(true, "20000", "登录成功", login.get());
        }else{
            return new JsonResult<>(false, "50000", "用户名或密码错误", null);
        }
    }

    @GetMapping("info")
    public JsonResult<?> info(@RequestHeader("X-Token") String token) {
        Optional<UserService.SimmUser> userOp = userService.getUser(token);
        if (userOp.isPresent()) {
            return new JsonResult<>(true, "20000", "查询成功", userOp.get());
        }else{
            return new JsonResult<>(false, "50000", "查询失败", "");
        }

    }

    @PostMapping("logout")
    public JsonResult<?> logout(@RequestHeader("X-Token") String token) {
        userService.logout(token);
        return new JsonResult<>(true, "20000", "退出成功", token);
    }
}

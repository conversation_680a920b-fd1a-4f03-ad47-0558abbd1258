package com.les.its.open.front.controller;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.DataItemGenerator;
import com.les.its.open.area.juncer.msg.cmd.TabTransactionCmd;
import com.les.its.open.area.message.MqMsgSimSyncProcess;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.les.its.open.area.message.param.cmd.TransactionType;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("openles/cmd")
@Slf4j
public class NatsCmdController {

    @Autowired
    private DataItemGenerator dataItemGenerator;

    @Autowired
    private MqMsgSimSyncProcess mqMsgSimSyncProcess;

    /**
     * 验证默认数据
     * @return
     */
    @GetMapping("/default/{controllerId}/{objectId}")
    public JsonResult<?> valid(@PathVariable String controllerId, @PathVariable String objectId) {
        Optional<Object> objectOp = dataItemGenerator.genDefaultCmdMqMsg(controllerId, objectId);
        if(objectOp.isPresent()){
            return new JsonResult<>(true, "20000", "生成成功", objectOp.get());
        }
        return new JsonResult<>(false, "50000", "生成失败", "");
    }

    /**
     * 验证默认数据
     * @return
     */
    @GetMapping("/look/{controllerId}/{objectId}")
    public JsonResult<?> look(@PathVariable String controllerId, @PathVariable String objectId) {
        Optional<Object> objectOp = dataItemGenerator.genDefaultLookMqMsg(controllerId, objectId);
        if(objectOp.isPresent()){
            return new JsonResult<>(true, "20000", "生成成功", objectOp.get());
        }
        return new JsonResult<>(false, "50000", "生成失败", "");
    }

    /**
     * 开启事务状态
     * @return
     */
    @PostMapping("/startTrans/{controllerId}")
    public JsonResult<?> startTrans(@PathVariable String controllerId) {

        List<String> datas = new ArrayList<>();

        TabTransactionCmd tabTransactionCmd = new TabTransactionCmd();
        tabTransactionCmd.setTransactionCreate(TransactionType.TRANSACTION.getCode());
        tabTransactionCmd.setTransactionTimeout(30);
        tabTransactionCmd.setTransactionNote("交易事务");
        tabTransactionCmd.setTransactionStatus(0);
        tabTransactionCmd.setTransactionErrorCode(0);

        datas.add(JSONObject.toJSONString(tabTransactionCmd));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_TRANSACTION_CMD.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "事务开启成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "事务开启异常", "");
    }



    /**
     * 开启参数校验
     * @return
     */
    @PostMapping("/verify/{controllerId}")
    public JsonResult<?> verify(@PathVariable String controllerId) {

        List<String> datas = new ArrayList<>();

        TabTransactionCmd tabTransactionCmd = new TabTransactionCmd();
        tabTransactionCmd.setTransactionCreate(TransactionType.VERIFYING.getCode());
        tabTransactionCmd.setTransactionTimeout(30);
        tabTransactionCmd.setTransactionNote("交易事务");
        tabTransactionCmd.setTransactionStatus(0);
        tabTransactionCmd.setTransactionErrorCode(0);

        datas.add(JSONObject.toJSONString(tabTransactionCmd));

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId,
                CmdMqObject.TAB_TRANSACTION_CMD.objectId()
                , datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "事务校验成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "事务校验失败", "");
    }




}

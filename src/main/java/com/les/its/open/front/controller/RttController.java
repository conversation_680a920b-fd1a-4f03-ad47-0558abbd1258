package com.les.its.open.front.controller;

import com.les.its.open.area.message.param.status.TabRTT;
import com.les.its.open.bussiness.bean.RttLogEntity;
import com.les.its.open.bussiness.process.RttLogProcess;
import com.les.its.open.bussiness.service.rtt.RTTService;
import com.les.its.open.front.controller.dto.TimeQuery;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("openles")
@Slf4j
public class RttController {

    private final RttLogProcess rttLogProcess;

    private final RTTService rttService;

    public RttController(RttLogProcess rttLogProcess, RTTService rttService) {
        this.rttLogProcess = rttLogProcess;
        this.rttService = rttService;
    }

    /**
     * 查询历史RTT数据项
     * @param controllerId
     * @param timeQuery
     * @return
     */
    @PostMapping("/query/rtt/{controllerId}")
    public JsonResult<?> rttLogs(@PathVariable String controllerId, @RequestBody TimeQuery timeQuery) {
        List<RttLogEntity> rttLogEntities = rttLogProcess.queryRttLog(controllerId, timeQuery);
        return new JsonResult<>(true, "20000", "获取成功", rttLogEntities);

    }

    /**
     * 删除过期数据项
     * @return
     */
    @PostMapping("/rm/rtt")
    public JsonResult<?> rmOld(){
        rttLogProcess.cleanupExpiredData();
        return new JsonResult<>(true, "20000", "删除成功", "");
    }


    /**
     * 实时rtt数据状态
     * @param controllerId
     * @return
     */
    @GetMapping("/rtt/{controllerId}")
    public JsonResult<?> natsParam(@PathVariable String controllerId) {
        List<TabRTT> rtts = rttService.getRtt(controllerId);
        return new JsonResult<>(true, "20000", "获取成功", rtts);

    }

}

package com.les.its.open.protocol;


/**
 * @ClassName: ProtocolType
 * @Description: 内部协议类型定义
 * @Author: king
 * @CreateDate: 2018/12/3 15:12
 */
public enum InterProtocolType {

    /**
     * 未知报文
     */
    UNKOWN_MESSAGE(-1, "未知报文"),

    OPENLES_DATA(0, "OPENLES报文"),

    ;


    private int value;
    private String description;

    InterProtocolType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int value()
    {
        return this.value;
    }

    public String description()
    {
        return this.description;
    }
}

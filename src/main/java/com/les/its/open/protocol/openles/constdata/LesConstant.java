/*
 * Copyright 2013-2018 Lilinfeng.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.les.its.open.protocol.openles.constdata;

/**
 * <AUTHOR>
 * @Description 基础数据项
 * @date 2014年3月15日
 * @version 1.0
 */
public final class LesConstant {
    /**
     * 报文开始标志符号
     */
    public static final byte START_FLAG = (byte)0xFD;

    /**
     * 报文最大长度
     */
    public static final int MESSAGE_MAX_SIZE =  5 + 2 + 65000;


    /**
     * 默认版本号
     */
    public static final int DEFAULT_VERSION = 0x0001;

}

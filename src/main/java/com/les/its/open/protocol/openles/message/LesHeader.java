/*
 * Copyright 2013-2018 Lil<PERSON><PERSON>.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.les.its.open.protocol.openles.message;


import com.les.its.open.protocol.openles.constdata.LesConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 协议头定义
 * @date 2014年3月15日
 * @version 1.0
 */
@Data
public final class LesHeader {

    /**
     * 协议头数据 5个 0XFE
     */
    public static final byte[] START_FLAG = {LesConstant.START_FLAG, LesConstant.START_FLAG,
            LesConstant.START_FLAG, LesConstant.START_FLAG,
            LesConstant.START_FLAG};


    public static final int HEADER_LENGTH = 21;

    /**
     * 协议版本号 2个字节
     */
    private int version;

    /**
     * 协议参数 1个字节
     */
    private int option;

    /**
     * 协议报文长度 2个字节
     * 长度为除去协议头START_FLAG以及CRC校验位
     */
    private int messageLength;


    /**
     * 协议报文类型 4个字节
     */
    private int messageID_1;
    private int messageID_2;
    private int messageID_3;
    private int messageID_4;

    /**
     * 报文数据源 1个 字节
     */
    private int  source;

    /**
     * 区域号 1个字节
     */
    private  int noArea;

    /**
     * 路口号 2个字节
     */
    private  int noJunc;

    /**
     * 时间戳 8个字节
     */
    private long timeStamp;


    /**
     * 用于前端数据过滤，勿要删除
     * @return
     */
    public int getOrgId(){
      return (messageID_1 << 24) + (messageID_2 << 16) + (messageID_3 << 8) + messageID_4;
    }

    @Override
    public String toString() {
        return "{" +
                "version=0x" + Integer.toHexString(version) +
                ", option=0b" + Integer.toBinaryString(option) +
                ", messageLength=" + messageLength +
                ", messageID=" + "[" + messageID_1 +  "-" + messageID_2 +  "-" + messageID_3 +  "-" + messageID_4 +  "]" +
                ", source=" + source +
                ", noArea=" + noArea +
                ", noJunc=" + noJunc +
                ", timeStamp=" + timeStamp +
                '}';
    }
}

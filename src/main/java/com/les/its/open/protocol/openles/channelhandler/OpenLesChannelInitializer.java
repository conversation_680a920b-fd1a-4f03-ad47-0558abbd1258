package com.les.its.open.protocol.openles.channelhandler;


import com.les.its.open.event.MessagePublisher;
import com.les.its.open.protocol.common.channelhandler.ProtocolChannelInitializer;
import com.les.its.open.protocol.common.codec.CommonMessageHandler;
import com.les.its.open.protocol.common.codec.ProtocolMonitorHandler;
import com.les.its.open.protocol.common.constdata.ProtocolType;
import com.les.its.open.protocol.openles.codec.OpenLesMessageDecoder;
import com.les.its.open.protocol.openles.codec.OpenLesMessageEncoder;
import com.les.its.open.protocol.openles.constdata.LesConstant;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import lombok.Data;

/**
 * @ClassName: MicroWaveChannelInitializer
 * @Description: 继承ProtocolChannelInitializer返回处理链
 * @Author: king
 * @CreateDate: 2018/12/3 15:43
 */
@Data
public class OpenLesChannelInitializer<B extends Channel> implements ProtocolChannelInitializer {

    private MessagePublisher messagePublisher;

    public OpenLesChannelInitializer(MessagePublisher messagePublisher) {
        this.messagePublisher = messagePublisher;
    }

    @Override
    public ChannelInitializer<B> getChannelInitializer()
    {
        return
        new ChannelInitializer<B>() {
            @Override
            public void initChannel(B ch) {
                ch.pipeline().addLast(new ProtocolMonitorHandler(ProtocolType.OPENLES, messagePublisher));
                ch.pipeline().addLast(new LoggingHandler(LogLevel.DEBUG));
                ch.pipeline().addLast(
                        new OpenLesMessageDecoder(LesConstant.MESSAGE_MAX_SIZE, 3, 2,
                                -5 + 2,0));
                ch.pipeline().addLast(
                        new OpenLesMessageEncoder());

                ch.pipeline().addLast("readTimeoutHandler",
                        new ReadTimeoutHandler(60 * 1));

                //报文处理类
                CommonMessageHandler messageHandler = new CommonMessageHandler();
                messageHandler.setMessagePublisher(messagePublisher);
                ch.pipeline().addLast("LesMessageHandler",
                        messageHandler );
            }
        };
    }

}

/*
 * Copyright 2013-2018 Lil<PERSON>feng.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.les.its.open.protocol.common.codec;


import com.les.its.open.event.MessagePublisher;
import com.les.its.open.protocol.common.message.AbstractProtocolMessage;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @Description 报文处理末端，当前为发送数据到总线
 * @date 2014年3月15日
 * @version 1.0
 */
@Slf4j
public class CommonMessageHandler extends ChannelInboundHandlerAdapter {

    @Setter
    private MessagePublisher messagePublisher;

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg){
        AbstractProtocolMessage message = (AbstractProtocolMessage) msg;
		//设置channel上下文对象
		message.setCtx(ctx);
        messagePublisher.publishMessage(message);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause){
        ctx.close();
        log.error("close the connection -{} for exception exist", ctx, cause);
    }

}

package com.les.its.open.protocol.common;

/**
 * @ClassName: OuterProtocolType
 * @Description:
 * 对应于 com.myweb.dataacquisition.protocol.common.constdata.ProtocolType 中的协议类别，只保留了id以及名称
 * @Author: king
 * @CreateDate: 2019/3/25 13:51
 */
public enum OuterProtocolType {
    /**
     * 莱斯信号机协议
     */
    OPENNLES(300,"开放式信号机莱斯协议");

    private int value;
    private String description;


    OuterProtocolType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int value()
    {
        return this.value;
    }

    public String description()
    {
        return this.description;
    }

}
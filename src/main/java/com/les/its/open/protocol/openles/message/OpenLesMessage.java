package com.les.its.open.protocol.openles.message;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.protocol.common.constdata.ProtocolType;
import com.les.its.open.protocol.common.message.AbstractProtocolMessage;
import com.les.its.open.protocol.openles.constdata.OpenLesType;
import com.les.its.open.utils.Crc16Utils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/10 16:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OpenLesMessage extends AbstractProtocolMessage {


    private LesHeader lesHeader;

    private LesBody lesBody;

    private LesTail lesTail;

    private long revTime;

    public OpenLesMessage() {
        super(ProtocolType.OPENLES);
    }

    private String remoteIp;

    private int remotePort;

    @Override
    @JsonIgnore
    @JSONField(serialize=false)
    public byte[] getBody() {
        return lesBody.getBody();
    }

    @Override
    public short getMessageID() {
        return OpenLesType.Tab_DATA.value();
    }


    /**
     * 获取数据项
     * @return
     */
    public byte[] genSendBodyWithOutParity()
    {
        ByteBuf byteBuf = Unpooled.buffer(lesBody.getBody().length + LesHeader.HEADER_LENGTH);
        //协议版本
        byteBuf.writeShortLE(lesHeader.getVersion());
        //协议参数
        byteBuf.writeByte(lesHeader.getOption() & 0xff);
        //报文长度
        byteBuf.writeShortLE(lesHeader.getMessageLength());
        //报文类别
        byteBuf.writeByte(lesHeader.getMessageID_1() & 0xff);
        byteBuf.writeByte(lesHeader.getMessageID_2() & 0xff);
        byteBuf.writeByte(lesHeader.getMessageID_3() & 0xff);
        byteBuf.writeByte(lesHeader.getMessageID_4() & 0xff);
        //数据源
        byteBuf.writeByte(lesHeader.getSource() & 0xff);
        //区域号
        byteBuf.writeByte(lesHeader.getNoArea() & 0xff);
        //路口号
        byteBuf.writeShortLE(lesHeader.getNoJunc());
        //时间戳
        byteBuf.writeLongLE(lesHeader.getTimeStamp());
        //正文数据项
        if (lesBody.getBody().length > 0){
            byteBuf.writeBytes(lesBody.getBody());
        }
        byte[] data = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(data);
        byteBuf.release();
        return data;
    }

    /**
     * 生成数据包校验位
     * @param lesMessage
     */
    public void updateParity(OpenLesMessage lesMessage)
    {
        //计算CRC参数
        int calculate = Crc16Utils.calculate(lesMessage);
        lesMessage.getLesTail().setParity(calculate);
    }

}

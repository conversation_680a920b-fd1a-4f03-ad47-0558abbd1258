package com.les.its.open.protocol.common.utils;


import com.les.its.open.netty.ChannelHolder;
import com.les.its.open.protocol.InterProtocolType;
import com.les.its.open.protocol.common.InterProtocol;
import com.les.its.open.protocol.common.OuterProtocolType;
import com.les.its.open.protocol.common.constdata.CommonConst;
import com.les.its.open.protocol.common.constdata.ProtocolType;
import com.les.its.open.protocol.common.message.AbstractMessageType;
import com.les.its.open.protocol.common.message.AbstractProtocolMessage;
import com.les.its.open.protocol.common.message.GetMessageTypeable;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.IntStream;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description 协议转换协助类
 * @date 2014年3月15日
 */
@Slf4j
@Component
public class MessageProcessUtils implements ApplicationContextAware {

    /**
     * 用于打印的基础数据，来自netty5中
     */
    private static final char[] HEXDUMP_TABLE = new char[256 * 4];

    static {
        final char[] DIGITS = "0123456789abcdef".toCharArray();
        for (int i = 0; i < 256; i++) {
            HEXDUMP_TABLE[i << 1] = DIGITS[i >>> 4 & 0x0F];
            HEXDUMP_TABLE[(i << 1) + 1] = DIGITS[i & 0x0F];
        }
    }

    /**
     * spring上下文
     */
    private ApplicationContext applicationContext;


    public static String hexDump(byte[] array) {
        return hexDump(array, 0, array.length);
    }

    public static String hexDump(byte[] array, int fromIndex, int length) {
        if (length < 0) {
            throw new IllegalArgumentException("length: " + length);
        }
        if (length == 0) {
            return "";
        }

        int endIndex = fromIndex + length;
        char[] buf = new char[length << 1];

        int srcIdx = fromIndex;
        int dstIdx = 0;
        for (; srcIdx < endIndex; srcIdx++, dstIdx += 2) {
            System.arraycopy(HEXDUMP_TABLE, (array[srcIdx] & 0xFF) << 1, buf, dstIdx, 2);
        }

        return new String(buf);
    }

    /**
     * 按照小端序从数据数据中获取数据项
     *
     * @param body
     * @param firstBitIndex
     * @param endBitIndex
     * @return
     */
    public static short getDataFromByte(byte[] body, int firstBitIndex, int endBitIndex) {
        short result = 0;
        if (body == null) {
            log.error("body is null");
            return result;
        }

        if (firstBitIndex < 0 || endBitIndex > 8 * (body.length) || (endBitIndex < firstBitIndex)) {
            log.error("invalid first bit index =[{}], end bit index = [{}] , body length = [{}]",
                    firstBitIndex,
                    endBitIndex,
                    body.length);
            return result;
        }

        if ((endBitIndex - firstBitIndex) > 16) {
            log.error("invalid first bit index =[{}], end bit index = [{}] , max support 16 bits data",
                    firstBitIndex,
                    endBitIndex);
            return result;
        }

        StringBuffer stringBuffer = new StringBuffer();
        IntStream.range(firstBitIndex, endBitIndex + 1).forEach(
                i ->
                {
                /*
                log.debug("i/8 == {}", i/8);
                log.debug("body[i/8] == {}", body[i/8]);
                log.debug( "(1 << (i - ((i/8) * 8)) == {}", 1 << (i - ((i/8) * 8)));
                log.debug( "(body[i/8] & (1 << (i - ((i/8) * 8))) == {}", (body[i/8] & (1 << (i - ((i/8) * 8)))));
                */
                    stringBuffer.append(((body[i / 8] & (1 << (i - ((i / 8) * 8)))) == (1 << (i - ((i / 8) * 8)))) ? "1" : "0");
                }
        );

        result = Short.valueOf(stringBuffer.reverse().toString(), 2);

        return result;
    }

    /**
     * 将外部报文装换到内部报文处理函数
     *
     * @param abstractProtocolMessage
     * @return
     */
    public boolean processMessage(AbstractProtocolMessage abstractProtocolMessage) {

        byte[] b = abstractProtocolMessage.getBody();
        log.trace("message body ====length=" + b.length);
        short msgId = abstractProtocolMessage.getMessageID();
        log.trace("message msgId=" + msgId);

        Optional<Class> classOptional = getMessageTypeClass(abstractProtocolMessage);

        if (classOptional.isPresent()) {
            AbstractMessageType abstractMessageType = (AbstractMessageType) (applicationContext.getBean(classOptional.get()));
            if (null == abstractMessageType) {
                log.error("无法从spring中获取数据 {}", classOptional.get());
            } else {
                //判定长度是否匹配,或者数据是可变长度
                if (abstractMessageType.getMessageLength() == b.length
                        || (abstractMessageType.getMessageLength() == CommonConst.CHANGE_ABLE_LENGTH)) {
                    //判定是否已经支持解析
                    if (abstractMessageType.getInterProtocolTypeOptional().isPresent()) {
                        log.trace("数据类型{} 数据长度 {} ", abstractMessageType.getType(), b.length);
                        Optional<Object> objectOptional = abstractMessageType.toInner(abstractProtocolMessage);
                        if (objectOptional.isPresent()) {
                            /*发送数据报文*/
                            applicationContext.publishEvent(objectOptional.get());
                            return true;
                        } else {
                            log.error("报文转换失败{}", abstractMessageType.getType());
                        }
                    } else {
                        log.error("报文尚不支持解析{}", abstractMessageType.getType());
                    }
                } else {
                    log.error("长度不匹配 实际为{} 应该为{}", b.length, abstractMessageType.getMessageLength());
                }
            }
        } else {
            log.error("其他报文报文编号:{} 报文数据{}", msgId, hexDump(abstractProtocolMessage.getBody()));
        }
        return false;
    }

    /**
     * 获取外部协议报文类
     *
     * @param abstractProtocolMessage
     * @return
     */
    private Optional<Class> getMessageTypeClass(AbstractProtocolMessage abstractProtocolMessage) {
        GetMessageTypeable getMessageTypeable = (GetMessageTypeable) (applicationContext.getBean(abstractProtocolMessage.getProtocolType().getMessageTypeable()));
        if (null == getMessageTypeable) {
            return Optional.empty();
        }
        return getMessageTypeable.getMessageTypeClass(abstractProtocolMessage.getMessageID());
    }

    /**
     * 将内部报文转化为外部报文处理函数
     *
     * @param interProtocol
     * @return
     */
    public Optional<AbstractProtocolMessage> processInternalMessage(InterProtocol interProtocol, ProtocolType protocolType) {

        Optional<AbstractMessageType> abstractMessageTypeOptional = getMessageTypeClass(interProtocol.getInterProtocolType(), protocolType);

        if (abstractMessageTypeOptional.isPresent()) {
            Optional<AbstractProtocolMessage> optionalAbstractProtocolMessage = abstractMessageTypeOptional.get().toOutter(interProtocol.getData());
            if (optionalAbstractProtocolMessage.isPresent()) {
                log.trace("转化外部报文成功-{}", optionalAbstractProtocolMessage.get());
                return optionalAbstractProtocolMessage;
            } else {
                log.error("转化为外部报错出错-{}", interProtocol);
            }
        } else {
            log.error("没有找到内部报文转化类:" + interProtocol.getInterProtocolType());
        }
        return Optional.empty();
    }

    /**
     * 获取内外部协议转换报文类
     *
     * @param interProtocolType
     * @return
     */
    private Optional<AbstractMessageType> getMessageTypeClass(InterProtocolType interProtocolType, ProtocolType protocolType) {
        Map<String, AbstractMessageType> beansOfType = applicationContext.getBeansOfType(AbstractMessageType.class);
        Optional<AbstractMessageType> abstractMessageTypeOptional =
                beansOfType.values().stream().filter(
                        abstractMessageType ->
                        {
                            if (abstractMessageType.getInterProtocolTypeOptional().isPresent()) {
                                if (abstractMessageType.getInterProtocolTypeOptional().get() == interProtocolType
                                        && abstractMessageType.getProtocolType() == protocolType) {
                                    return true;
                                }
                            }
                            return false;
                        }
                ).findAny();
        return abstractMessageTypeOptional;
    }

    /**
     * 发送设备数据
     *
     * @param ctx
     * @param protocolType
     * @param b
     * @param linkChange
     * @return
     */
    public static EquipmentStatus buildEquipmentStatus(ChannelHandlerContext ctx, ProtocolType protocolType, boolean b, boolean linkChange) {

        Optional<String> remoteAddress = ChannelHolder.getAddressIp(ChannelHolder.getKey(ctx), false);
        Optional<Integer> remotePort = ChannelHolder.getAddressPort(ChannelHolder.getKey(ctx), false);

        Optional<String> localAddress = ChannelHolder.getAddressIp(ChannelHolder.getKey(ctx), true);
        Optional<Integer> localPort = ChannelHolder.getAddressPort(ChannelHolder.getKey(ctx), true);

        EquipmentStatus equipmentStatus = EquipmentStatus.builder()
                .ip(remoteAddress.isPresent() ? remoteAddress.get() : "")
                .port(remotePort.isPresent() ?  remotePort.get() : 0)
                .localIp(localAddress.isPresent() ? localAddress.get() : "")
                .localPort(localPort.isPresent() ? localPort.get() : 0).active(b).linkChange(linkChange).build();

        Optional<OuterProtocolType> outerProtocolType = Arrays.asList(OuterProtocolType.values()).stream().filter(
                outerProtocolType1 ->
                        outerProtocolType1.value() == protocolType.value()
        ).findAny();
        if (outerProtocolType.isPresent()) {
            equipmentStatus.setOuterProtocolType(outerProtocolType.get());
        }

        return equipmentStatus;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

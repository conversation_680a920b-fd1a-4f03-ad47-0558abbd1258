/*
 * Copyright 2013-2018 <PERSON><PERSON><PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.les.its.open.protocol.openles.codec;


import com.les.its.open.netty.ChannelHolder;
import com.les.its.open.protocol.openles.constdata.LesConstant;
import com.les.its.open.protocol.openles.message.LesBody;
import com.les.its.open.protocol.openles.message.LesHeader;
import com.les.its.open.protocol.openles.message.LesTail;
import com.les.its.open.protocol.openles.message.OpenLesMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteOrder;
import java.util.Optional;

import static com.les.its.open.protocol.openles.message.LesHeader.HEADER_LENGTH;


/**
 * <AUTHOR>
 * @version 1.0
 * @Description 分包处理
 * @date 2014年3月15日
 */
@Slf4j
public class OpenLesMessageDecoder extends LengthFieldBasedFrameDecoder {

    private DecodeStatus decodeStatus;
    private int fd_num;

    public OpenLesMessageDecoder(int maxFrameLength, int lengthFieldOffset,
                                 int lengthFieldLength, int lengthAdjustment, int initialBytesToStrip) {
        super(ByteOrder.LITTLE_ENDIAN,
                maxFrameLength, lengthFieldOffset, lengthFieldLength, lengthAdjustment, initialBytesToStrip, true);
        decodeStatus = DecodeStatus.UNPACK;
        fd_num = 0;
    }

    @Override
    protected Object decode(ChannelHandlerContext ctx, ByteBuf in)
            throws Exception {

        /*
          同步字节5个FD
         */

        if(decodeStatus == DecodeStatus.UNPACK) {
            int readableBytes = in.readableBytes();
            for (int i = 0; i < readableBytes; i++) {
                in.markReaderIndex();
                if (in.readByte() == LesConstant.START_FLAG) {
                    fd_num++;
                } else {
                    if (fd_num < 5) {
                        fd_num = 0;
                    } else {
                        in.resetReaderIndex();
                        fd_num = 0;
                        decodeStatus = DecodeStatus.DATACHAR;
                        break;
                    }
                }
            }
        }

        //解包尚未完成
        if(decodeStatus != DecodeStatus.DATACHAR){
            return null;
        }

         {
            ByteBuf frame = (ByteBuf) super.decode(ctx, in);
            if (frame == null) {
                return null;
            }

            //重新置解包状态
            decodeStatus = DecodeStatus.UNPACK;

            //判定数据长度
            if (frame.readableBytes() < HEADER_LENGTH + 2) {
                frame.release();
                return null;
            }

            OpenLesMessage message = new OpenLesMessage();
            //读取报文头数据
            LesHeader messageHeader = new LesHeader();
            //协议版本数据
            messageHeader.setVersion(frame.readUnsignedShortLE());
            //协议参数数据
            messageHeader.setOption(frame.readUnsignedByte());
            //报文长度数据
            messageHeader.setMessageLength(frame.readUnsignedShortLE());
            //报文类别
            messageHeader.setMessageID_1(frame.readUnsignedByte());
            messageHeader.setMessageID_2(frame.readUnsignedByte());
            messageHeader.setMessageID_3(frame.readUnsignedByte());
            messageHeader.setMessageID_4(frame.readUnsignedByte());
            //数据源
            messageHeader.setSource(frame.readUnsignedByte());
            //区域号
            messageHeader.setNoArea(frame.readUnsignedByte());
            //路口号
            messageHeader.setNoJunc(frame.readUnsignedShortLE());
            //时间戳
            messageHeader.setTimeStamp(frame.readLongLE());

            //读取报文体数据
            LesBody messageBody = new LesBody();
            int size = messageHeader.getMessageLength() - HEADER_LENGTH;
            if (size > 0) {
                byte[] bytesBody = new byte[size];
                frame.readBytes(bytesBody);
                messageBody.setBody(bytesBody);
            } else {
                messageBody.setBody(new byte[0]);
            }

            //读取校验位数据
            LesTail messageTail = new LesTail();
            messageTail.setParity(frame.readUnsignedShortLE());

            //生成数据报文
            message.setLesHeader(messageHeader);
            message.setLesBody(messageBody);
            message.setLesTail(messageTail);
            message.setRevTime(System.currentTimeMillis());

            message.setCtx(ctx);

            //设置源IP以及端口
            Optional<String> address = ChannelHolder.getIp(ChannelHolder.getRemoteAddress(ctx));
            int localPort = ChannelHolder.getLocalPort(ctx.channel());
            message.setRemoteIp(address.orElse(""));
            message.setRemotePort(localPort);

            frame.release();
            return message;

        }
    }
}

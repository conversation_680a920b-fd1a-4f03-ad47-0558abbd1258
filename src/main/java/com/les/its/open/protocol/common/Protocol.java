package com.les.its.open.protocol.common;


import com.les.its.open.event.MessagePublisher;
import com.les.its.open.protocol.common.channelhandler.ProtocolChannelInitializer;
import com.les.its.open.protocol.common.constdata.ProtocolType;
import com.les.its.open.protocol.openles.channelhandler.OpenLesChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * @ClassName: Protocol
 * @Description: 协议管理类
 * @Author: king
 * @CreateDate: 2018/12/3 15:04
 */
@Data
@Slf4j
public class Protocol {

    private static final String DEFAULT_PROTOCL_STRING = ProtocolType.OPENLES.name();

    private ProtocolType protocolType;

    private ProtocolChannelInitializer protocolChannelInitializer;


    private Protocol(ProtocolType protocolType,
                     ProtocolChannelInitializer protocolChannelInitializer) {
        this.protocolType = protocolType;
        this.protocolChannelInitializer = protocolChannelInitializer;
    }

    /**
     * 构建协议器
     * @param protocol
     * @param messagePublisher
     * @param isComm
     * @return
     */

    public static Optional<Protocol> buildProtocol(String protocol, MessagePublisher messagePublisher, boolean isComm)
    {
        if(protocol.trim().isEmpty())
        {
            protocol = DEFAULT_PROTOCL_STRING;
        }
        ProtocolType protocolType = ProtocolType.valueOf(protocol.trim());
        return buildProtocol(protocolType, messagePublisher, isComm);
    }

    public static Optional<Protocol> buildProtocol(ProtocolType protocolType, MessagePublisher messagePublisher, boolean isComm)
    {
        if(ProtocolType.OPENLES.value() == protocolType.value())
        {
            OpenLesChannelInitializer openLesChannelInitializer = new OpenLesChannelInitializer<SocketChannel>(messagePublisher);
            return Optional.of(new Protocol(protocolType, openLesChannelInitializer));
        }
        else
         {
             log.error("暂不支持协议 {} - {} ", protocolType.value(), protocolType.description());
            return Optional.empty();
        }
    }



}

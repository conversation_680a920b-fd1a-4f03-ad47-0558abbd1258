package com.les.its.open.protocol.openles.constdata.inner;

import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.msg.JuncerMsg;
import com.les.its.open.protocol.InterProtocolType;
import com.les.its.open.protocol.common.constdata.CommonConst;
import com.les.its.open.protocol.common.constdata.ProtocolType;
import com.les.its.open.protocol.common.message.AbstractMessageType;
import com.les.its.open.protocol.common.message.AbstractProtocolMessage;
import com.les.its.open.protocol.openles.constdata.OpenLesType;
import com.les.its.open.protocol.openles.message.LesBody;
import com.les.its.open.protocol.openles.message.LesHeader;
import com.les.its.open.protocol.openles.message.LesTail;
import com.les.its.open.protocol.openles.message.OpenLesMessage;
import com.les.its.open.utils.Crc16Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/10 15:49
 */
@Component
@Slf4j
public class DataOpenLes extends AbstractMessageType {


    public DataOpenLes() {
        super(OpenLesType.Tab_DATA.name(), OpenLesType.Tab_DATA.value(), CommonConst.CHANGE_ABLE_LENGTH,
                Optional.of(InterProtocolType.OPENLES_DATA),
                ProtocolType.OPENLES);
    }

    @Override
    public Optional<Object> toInner(AbstractProtocolMessage abstractProtocolMessage) {

        //转换到内部数据项
        if(abstractProtocolMessage instanceof OpenLesMessage){
            OpenLesMessage openLesMessage = (OpenLesMessage)abstractProtocolMessage;

            JuncerMsg juncerMsg = JuncerMsg.builder()
                    .ip(openLesMessage.getRemoteIp())
                    .openLesMessage(openLesMessage)
                    .build();

            //校验crc16位参数是否正确
            int calculateCrc = Crc16Utils.calculate(juncerMsg.getOpenLesMessage());
            if(calculateCrc != juncerMsg.getOpenLesMessage().getLesTail().getParity()){
                String logStr = String.format("crc校验失败,应该是0x%x,实际是0x%x", calculateCrc,
                        juncerMsg.getOpenLesMessage().getLesTail().getParity());
                log.error("ip-{}-{}", juncerMsg.getIp(), logStr);
            }


            return Optional.of(juncerMsg);
        }

        return Optional.empty();
    }

    @Override
    public Optional<AbstractProtocolMessage> toOutter(Object object) {
        if(object instanceof TabInBase){
            TabInBase tabInBase = (TabInBase)object;
            log.debug("向外部-{}发送报文数据-{}", tabInBase.getIp(), tabInBase);

            //头部数据项
            LesHeader lesHeader = new LesHeader();
            lesHeader.setVersion(0x0001);
            lesHeader.setOption(0b01);
            lesHeader.setMessageLength(tabInBase.getData().length + LesHeader.HEADER_LENGTH);
            lesHeader.setMessageID_1((int) (tabInBase.getMsgTypeCode() >> 24) & 0xff);
            lesHeader.setMessageID_2((int) (tabInBase.getMsgTypeCode()  >> 16) & 0xff);
            lesHeader.setMessageID_3((int) (tabInBase.getMsgTypeCode()  >> 8) & 0xff);
            lesHeader.setMessageID_4((int) (tabInBase.getMsgTypeCode() ) & 0xff);
            lesHeader.setSource(0x01);
            lesHeader.setNoArea(tabInBase.getNoArea());
            lesHeader.setNoJunc(tabInBase.getNoJunc());
            lesHeader.setTimeStamp(System.currentTimeMillis());

            //正文数据项
            LesBody lesBody = new LesBody();
            lesBody.setBody(tabInBase.getData());

            //尾部
            LesTail lesTail = new LesTail();

            OpenLesMessage openLesMessage = new OpenLesMessage();
            openLesMessage.setLesHeader(lesHeader);
            openLesMessage.setLesBody(lesBody);
            openLesMessage.setLesTail(lesTail);

            openLesMessage.updateParity(openLesMessage);

            return Optional.of(openLesMessage);
        }
        return Optional.empty();
    }

}

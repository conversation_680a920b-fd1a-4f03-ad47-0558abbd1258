package com.les.its.open.protocol.common.utils;


import com.les.its.open.protocol.common.OuterProtocolType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: EquipmentStatus
 * @Description:
 * @Author: king
 * @CreateDate: 2019/3/26 11:03
 */
@Data
@Slf4j
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentStatus {
    /**
     * 设备连接的ip地址
     */
    private String ip;

    /**
     * 设备连接的端口
     */
    private long port;

    /**
     * 本地连接的ip地址
     */
    private String localIp;

    /**
     * 本地连接的端口
     */
    private long localPort;

    /**
     * 外部协议类别
     */
    private OuterProtocolType outerProtocolType;

    /**
     * 通信连接
     */
    private boolean active;

    /**
     * 是否由于链路状态变更而发布的告警
     */
    private boolean linkChange;
}

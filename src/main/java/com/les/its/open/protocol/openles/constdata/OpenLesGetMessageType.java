package com.les.its.open.protocol.openles.constdata;



import com.les.its.open.protocol.common.message.GetMessageTypeable;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

/**
 * @ClassName: MicrowaveGetMessageType
 * @Description:
 * @Author: king
 * @CreateDate: 2018/12/14 15:48
 */
@Component
public class OpenLesGetMessageType implements GetMessageTypeable {

    /**
     * 根据orgValue获取协议报文
     * @param orgValue
     * @return
     */
    @Override
    public Optional<Class> getMessageTypeClass(short orgValue) {
        Optional<OpenLesType> messageTypeOptional = Arrays.asList(OpenLesType.values()).stream()
                .filter(messageType -> messageType.value() == orgValue)
                .findAny();

        if(messageTypeOptional.isPresent())
        {
            return Optional.of(messageTypeOptional.get().classType());
        }else {
            return Optional.empty();
        }
    }
}

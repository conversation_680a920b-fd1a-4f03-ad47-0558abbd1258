package com.les.its.open.protocol.openles.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2023/6/8 14:53
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@Builder
public class CrossDataDTO {

    /**
     * 信号机id
     */
    private Integer id;

    /**
     * 路口名称
     */
    private String name;

    /**
     * 路口id
     */
    private Integer crossingId;

    /**
     * 信号机类型
     * /1：arm2016 信号机    2：GBT20999 信号机    3：GB25280 信号机    4：2010 信号机（4：th-vii 5：th-vi 6：th-v）7：NTCIP
     */
    private Integer type;
    private String ip;
    private Integer port;
    private String longitude;
    private String latitude;
    private String manufacturer;
    private String version;
    private String code;
    private String productionDate;
    private Integer sftp;
    private String configDate;
    private String installCrossing;
    private Integer timezone;
    private Integer controlCount;
    private Integer status;
    private String typeName;
    private Object keepTime;
    private Object keepPlan;
    private Object keepStage;
    private String createTime;
    private String updateTime;
    private String statusName;


    //用于前端数据展示
    private String crossID;
    private String crossName;
}

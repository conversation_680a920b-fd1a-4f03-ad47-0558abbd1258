package com.les.its.open.protocol.common.channelhandler;

import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;

/**
 * @ClassName: ProtocolChannelInitializer
 * @Description: 分包、拆包、数据处理数据链接口
 * @Author: king
 * @CreateDate: 2018/12/3 15:51
 */
public interface ProtocolChannelInitializer<B extends Channel> {
    /**
     * 定义协议处理handler链
     * @return
     */
    ChannelInitializer<B> getChannelInitializer();
}

package com.les.its.open.protocol.openles.message;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.les.its.open.protocol.common.utils.MessageProcessUtils;
import lombok.Data;

/**
 * @ClassName: MicrowaveBody
 * @Description: 协议正文数据
 * @Author: king
 * @CreateDate: 2018/11/5 11:58
 */
@Data
public class LesBody {

    /**
     * 数据段 长度不定
     */
    @JsonIgnore
    @JSONField(serialize=false)
    private byte[] body;

    public LesBody() {
        this.body = new byte[0];
    }

    @Override
    public String toString() {
        return "{" +
                "body=0x" + MessageProcessUtils.hexDump(body) +
                '}';
    }
}

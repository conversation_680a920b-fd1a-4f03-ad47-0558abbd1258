package com.les.its.open.protocol.openles.message;

import lombok.Data;

/**
 * @ClassName: MicrowaveTail
 * @Description: 协议尾部定义
 * @Author: king
 * @CreateDate: 2018/11/5 11:58
 */
@Data
public class LesTail {
    /**
     * 校验段 2个字节
     * 数据为除去协议头和CRC校验位
     */
    private int parity;

    @Override
    public String toString() {
        return "{" +
                "parity=0x" + Integer.toHexString(parity) +
                '}';
    }
}

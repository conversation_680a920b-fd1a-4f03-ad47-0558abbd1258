package com.les.its.open.protocol.common.message;



import com.les.its.open.protocol.InterProtocolType;
import com.les.its.open.protocol.common.constdata.ProtocolType;
import lombok.Getter;

import java.util.Optional;

/**
 * @ClassName: AbstractMessageType
 * @Description: 外部数据包定义抽象类
 * @Author: king
 * @CreateDate: 2018/12/14 10:48
 */
public abstract class AbstractMessageType implements ToInnerAble, ToOutterAble{

    /**
     * 数据类型
     */
    @Getter
    private String type;

    /**
     * 类型值
     */
    @Getter
    private short msgID;

    /**
     * 此处的定义只是报文的可变内容的长度
     */
    @Getter
    private int messageLength;

    /**
     * 转换出来的内部协议类型
     */
    @Getter
    private Optional<InterProtocolType> interProtocolTypeOptional;

    /**
     * 外部协议类型
    */
    @Getter
    private ProtocolType protocolType;

    protected AbstractMessageType(String type, short msgID, int messageLength, Optional<InterProtocolType> interProtocolTypeOptional, ProtocolType protocolType) {
        this.type = type;
        this.msgID = msgID;
        this.messageLength = messageLength;
        this.interProtocolTypeOptional = interProtocolTypeOptional;
        this.protocolType = protocolType;
    }


}

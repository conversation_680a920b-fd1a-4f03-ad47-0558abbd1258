package com.les.its.open.protocol.common.constdata;


import com.les.its.open.protocol.common.OuterProtocolType;
import com.les.its.open.protocol.openles.constdata.OpenLesGetMessageType;

/**
 * @ClassName: ProtocolType
 * @Description: 外部协议枚举类型
 * @Author: king
 * @CreateDate: 2018/12/3 15:12
 */
public enum ProtocolType {

    /**
     * GB20999 udp协议
     */
    OPENLES(OuterProtocolType.OPENNLES.value(),OuterProtocolType.OPENNLES.description(), OpenLesGetMessageType.class);


    private int value;
    private String description;
    private Class getMessageTypeable;

    ProtocolType(int value, String description, Class getMessageTypeable) {
        this.value = value;
        this.description = description;
        this.getMessageTypeable = getMessageTypeable;
    }

    public int value()
    {
        return this.value;
    }

    public String description()
    {
        return this.description;
    }

    public Class getMessageTypeable()
    {
        return getMessageTypeable;
    }
}

/*
 * Copyright 2013-2018 Lil<PERSON>feng.
 *  
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *  
 *      http://www.apache.org/licenses/LICENSE-2.0
 *  
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.les.its.open.protocol.openles.codec;


import com.les.its.open.protocol.openles.message.LesHeader;
import com.les.its.open.protocol.openles.message.OpenLesMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description 数据发送处理
 * @date 2014年3月15日
 * @version 1.0
 */
@Slf4j
public final class OpenLesMessageEncoder extends
        MessageToByteEncoder<OpenLesMessage> {

    @Override
    protected void encode(ChannelHandlerContext ctx, OpenLesMessage msg,
                          ByteBuf sendBuf) throws Exception {
        if (msg == null || msg.getLesHeader() == null || msg.getLesBody() == null || msg.getLesTail() == null) {
            throw new Exception("The encode message is null");
        }

        //报文头数据
        sendBuf.writeBytes(LesHeader.START_FLAG);

        //报文本体数据
        byte[] sendBodyWithOutParity = msg.genSendBodyWithOutParity();
        if(sendBodyWithOutParity.length > 0){
            sendBuf.writeBytes(sendBodyWithOutParity);
        }

        sendBuf.writeShortLE(msg.getLesTail().getParity() & 0xffff);

    }


}

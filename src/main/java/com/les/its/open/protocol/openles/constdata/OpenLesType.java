package com.les.its.open.protocol.openles.constdata;

import com.les.its.open.protocol.openles.constdata.inner.DataOpenLes;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/10 15:46
 */
public enum OpenLesType {

    Tab_DATA((short) (0), DataOpenLes.class)
    ;

    /**
     * 类型值
     */
    private short msgId;
    /**
     * 报文类型
     */
    private Class classType;


    OpenLesType(short msgId, Class classType) {
        this.msgId = msgId;
        this.classType = classType;
    }

    public short value() {
        return this.msgId;
    }

    public Class classType()
    {
        return classType;
    }
}

package com.les.its.open.monitor.bean;


import com.les.its.open.netty.ChannelHolder;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Queue;

/**
 * @ClassName: SocketInfo
 * @Description: 连接监控项
 * @Author: king
 * @CreateDate: 2018/12/19 14:36
 */
@Builder
@Slf4j
@Getter
public class SocketInfo {
    private long activeSocket;
    private long inactiveSocket;
    private long totalSocket;
    List<ChannelHolder.ChannelInfo> channelInfoList;
    private Queue<ChannelHolder.LogInfo> linkLogQueue;
}

package com.les.its.open.monitor;


import com.les.its.open.monitor.bean.SocketInfo;
import com.les.its.open.netty.ChannelHolder;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ChannelMonitor
 * @Description: 继承AbstractEndpoint实现监控
 * @Author: king
 * @CreateDate: 2018/12/4 8:19
 */
@Configuration
@Endpoint(id="channel-info")
public class ChannelMonitor {

    @ReadOperation
    public SocketInfo invoke() {

        Map<String, ChannelHolder.ChannelInfo> channelInfoMap = ChannelHolder.getMAP();
        List<ChannelHolder.ChannelInfo> channelInfoList = new ArrayList<>();
        channelInfoMap.keySet().forEach(
            key ->
            {
                channelInfoList.add(channelInfoMap.get(key));
            }
        );

       //构造显示信息
        long activeSocket = ChannelHolder.getMAP().values().stream()
                .filter(channelInfo -> channelInfo.getChannelHandlerContext().channel().isActive())
                .count();
        long inactiveSocket = ChannelHolder.getMAP().values().stream()
                .filter(channelInfo -> !channelInfo.getChannelHandlerContext().channel().isActive())
                .count();

        return SocketInfo.builder()
                .channelInfoList(channelInfoList)
                .activeSocket(activeSocket)
                .inactiveSocket(inactiveSocket)
                .totalSocket(activeSocket + inactiveSocket)
                .linkLogQueue(ChannelHolder.getLinkLogQueue()).build();
    }
}

package com.les.its.open.monitor;


import com.les.its.open.bussiness.bean.P1049Entity;
import com.les.its.open.bussiness.service.LocalSignalCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: ChannelMonitor
 * @Description: 继承AbstractEndpoint实现监控
 * @Author: king
 * @CreateDate: 2018/12/4 8:19
 */
@Configuration
@Endpoint(id = "data-info-ht")
public class HtSignalMonitor {

    @Autowired
    private LocalSignalCacheService localSignalCacheService;

    @ReadOperation
    public ConcurrentHashMap<String, Map<String, P1049Entity>> invoke() {
        return localSignalCacheService.getSignalInfoMap();
    }
}

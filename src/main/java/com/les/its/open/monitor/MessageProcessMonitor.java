package com.les.its.open.monitor;


import com.les.its.open.monitor.bean.MessageProcessInfo;
import com.les.its.open.event.AbstractSummary;
import org.springframework.beans.BeansException;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: MessageProcessMonitor
 * @Description:
 * @Author: king
 * @CreateDate: 2018/12/19 14:54
 */
@Configuration
@Endpoint(id = "message-info")
public class MessageProcessMonitor implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private MessageProcessInfo get(AbstractSummary abstractSummary) {
        long success = abstractSummary.getAtomicSuccessLong().get();
        long error = abstractSummary.getAtomicErrorLong().get();
        double errorRate = 0;
        if (0 != (error + success)) {
            errorRate = error * 1.0 / (error + success);
        }
        return MessageProcessInfo.builder()
                .successMessage(success)
                .errorMessage(error)
                .totalMessage(success + error)
                .errorPercent(errorRate)
                .successPS(abstractSummary.getSuccessMessageSecond())
                .errorPS(abstractSummary.getFailedMessageSecond())
                .build();
    }

    @ReadOperation
    public Map<String, MessageProcessInfo> invoke() {
        Map<String, AbstractSummary> beansOfType = applicationContext.getBeansOfType(AbstractSummary.class);
        Map<String, MessageProcessInfo> messageProcessInfoMap = new HashMap<>();
        beansOfType.keySet().stream().forEach(key ->
        {
            messageProcessInfoMap.put(key, get(beansOfType.get(key)));
        });
        return messageProcessInfoMap;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

server:
  port: 16000
spring:
  application:
    name: adatper1049
  #resources:
  #static-locations: classpath:/static/
  datasource:
    #url: *****************************************
    #username: admin
    url: *********************************************************************************************************************************************************************
    #url: jdbc:h2:file:./db/areasignal;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false
    username: root
    password: system
    configuration:
      maximum-pool-size: 40
      max-total: 40
    hikari:
      minimum-idle: 20
      maximum-pool-size: 40

    #driverClassName: oracle.jdbc.driver.OracleDriver
  #    type: com.alibaba.druid.pool.DruidDataSource
  jpa:
    # Specify the DBMS
    #database: ORACLE
    database: mysql
    # Show or not log for each sql query
    show-sql: true
    #open-in-view: true
    hibernate:
      # Hibernate ddl auto (create, create-drop, update)
      ddl-auto: update
      # Naming strategy
      #naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
      #hibernate5配置
    properties:
      hibernate:
        # stripped before adding them to the bean manager)
        #dialect: org.hibernate.dialect.Oracle10gDialect
        cache:
          # 打开二级缓存
          use_second_level_cache: false
        show_sql: false
        use_sql_comments: true
        format_sql: true
  data:
    rest:
      base-path: /api
  rabbitmq:
    host: ***************
    port: 5672
    username: admin
    password: admin
    virtual-host: /
    listener:
      simple:
        prefetch: 200
  task:
    scheduling:
      pool:
        size: 4
  h2:
    console:
      enabled: true
      path: /h2
      settings:
        web-allow-others: true
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  security:
    enabled: true
eureka:
  instance:
    metadata-map:
      user.name: ${spring.security.user.name}
      user.password: ${spring.security.user.password}
info.tags.security: secured
logging:
  level:
    com.myweb: debug
    io.netty: debug
  path: ./daa-log/signal
  name:
  file:
    max-size: 100MB
    max-history: 30

global:
  mq:
    exchange:
      name: adapter-ntcip
      routingKeyPrefix: adapter.ntcip.area.
      routingKeyPrefixListen: adapter.ntcip.system.
      scatsCmdQueue: 1049CmdQueue2
    centralExchange:
      name: MQREC.direct
      routingKeyPrefix: MQREC.JSON
      queue: 1049-signal-cmd-queue
  server:
    local:
      #监听类型
      listens:
        port: 2117 #2117
        protocol: P1049_SIGNAL
        #18001 POINT_FORWARD_CARI
        #客户端类型，需要同时配置ip
      connect:
        ip:
        port:
        localPort:
        protocol:
        # ************** 4002
      comm:
        address:
        protocol:
        # COM1 LES_SIGNAL
  cityCode: 321200
  departmentCode:
  areaNo: 0
  simulateNo: 0
  simulateTcpNo: 0
  signalIpPrefix: 192.168.127
  comStart: 3001
  comJuncStart: 301
  tcpJuncStart: 801
  #推送到mq消息类型 les/ntcip
  mqMsgType: les
  #推送的通道类型 mq/websocket
  channelType: websocket,mq
  #ops的访问地址
  opsip: *************:80
  #是否启用sgp的数据存储
  enableUseSgp: true
  #signal-param数据地址
  signalParamIp: ***************:30080/sgp
  #**************:8232
  #是否启动信号机、路口数据项数据校验更新
  enableCheckSignalAndCrossingData: false
  #是否针对信号品牌进行过滤
  brandList: YL #DW #YL
  # area区域号过滤
  areaList:
  #信号机、路口静态数据存储地址，因为存在只有sgp的地方，此设置为可配置
  basicSignalParamIp: ***************:30080/scs
test:
  signal:
    useTest: true
    signalId: 320100YL00167 #320100LS00150 #320100DW00147
    crossingIds: 320100YL001671 #320100LS001501  #,320100LS140012
    noArea: 0
    noJunc: 167
    subJuncNos: 1 #,2
    ip: ************** #************* #************** #************* #***********
    port: 3000 #716 #7716 #716 #7716 #4050
    signalId_1049: 12345678901200021 #1100000001 #
    crossingIds_1049: 12345600000021 #860506 #
    #用于标记路口所属的系统ip地址
    systemIp: **************


##### 区域编号 全局唯一，取值 6 位行政区划代码+3 位数字
##### 子区编号。全局唯一，取值区域编号+2 位数字
##### 路口编号。全局唯一，取值区域编号+5 位数字
##### 信号机设备的编号。取值 12 位交通管理部门机构代码（见GA/T 380-2012） +5 位数字
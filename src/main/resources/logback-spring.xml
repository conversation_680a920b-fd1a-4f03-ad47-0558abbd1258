<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<configuration scan="true" scanPeriod="30 seconds">
    <!-- 引入 Spring Boot 默认配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- 获取应用名称 -->
    <springProperty scope="context" name="application_name" source="spring.application.name" defaultValue="openles"/>

    <!-- 日志存储路径 -->
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path" defaultValue="daa-log"/>

    <!-- 日志格式定义 -->
    <property name="PATTERN_FORMAT" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-18thread] %-5level %logger{36} - %msg%n"/>
    <property name="PATTERN_FORMAT_WITH_EX" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-18thread] %-5level %logger{36} - %msg%n%exception{full}"/>
    <property name="PATTERN_FORMAT_JUNCER" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%-18.18thread] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%highlight(%-5level)] [%magenta(${PID:- })] [%yellow(%-18thread)] %cyan(%logger{36}) - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 系统日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/openles/${application_name:-openles}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/openles/${application_name:-openles}-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN_FORMAT}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/openles/${application_name:-openles}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/openles/${application_name:-openles}-error-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN_FORMAT_WITH_EX}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- IP 基础日志文件 -->
    <appender name="IP_BASED_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>clientIP</key>
            <defaultValue>unknown</defaultValue>
        </discriminator>

        <filter class="com.les.its.open.area.net.log.IpBasedLoggingFilter">
            <clientIP>${clientIP}</clientIP>
        </filter>

        <sift>
            <appender name="FILE-${clientIP}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${LOG_PATH}/juncer/${clientIP}/device.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>${LOG_PATH}/juncer/${clientIP}/device-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                    <maxFileSize>100MB</maxFileSize>
                    <maxHistory>30</maxHistory>
                    <totalSizeCap>5GB</totalSizeCap>
                </rollingPolicy>
                <encoder>
                    <pattern>${PATTERN_FORMAT_JUNCER}</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>


    <!-- 信号机日志文件 -->
    <appender name="SIGNAL_BASED_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>signalId</key>
            <defaultValue>unknown</defaultValue>
        </discriminator>

        <filter class="com.les.its.open.area.message.log.SignalBasedLoggingFilter">
            <signalId>${signalId}</signalId>
        </filter>

        <sift>
            <appender name="FILE-${signalId}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${LOG_PATH}/message/${signalId}/control.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>${LOG_PATH}/message/${signalId}/control-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
                    <maxFileSize>100MB</maxFileSize>
                    <maxHistory>30</maxHistory>
                    <totalSizeCap>5GB</totalSizeCap>
                </rollingPolicy>
                <encoder>
                    <pattern>${PATTERN_FORMAT_JUNCER}</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <!-- 异步日志处理 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 异步错误日志处理 -->
    <appender name="ASYNC_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!-- 第三方框架日志级别控制 -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.apache" level="INFO"/>
    <logger name="org.hibernate" level="INFO"/>
    <logger name="org.mongodb" level="WARN"/>
    <logger name="org.apache.kafka" level="INFO"/>
    <logger name="org.apache.http" level="INFO"/>
    <logger name="com.zaxxer" level="INFO"/>
    <logger name="io.netty" level="INFO"/>
    <logger name="org.slf4j" level="INFO"/>
    <logger name="ch.qos.logback" level="WARN"/>



    <logger name="com.les.its.open.area.net.log" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="IP_BASED_FILE"/>
    </logger>


    <logger name="com.les.its.open.area.message.log" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="SIGNAL_BASED_FILE"/>
    </logger>

    <!-- 应用日志级别控制 -->
    <logger name="com.les.its.open" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="ASYNC_ERROR"/>
    </logger>

    <!-- 根日志级别 -->
    <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="ASYNC_ERROR"/>
        <appender-ref ref="IP_BASED_FILE"/>
        <appender-ref ref="SIGNAL_BASED_FILE"/>
    </root>
</configuration>

package com.les.its.open.protocol.openles.codec;

import com.les.its.open.protocol.openles.constdata.LesConstant;
import com.les.its.open.protocol.openles.message.OpenLesMessage;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static io.netty.buffer.ByteBufUtil.hexDump;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Slf4j
class OpenLesMessageDecoderTest {

    private OpenLesMessageDecoder decoder;
    
    @Mock
    private ChannelHandlerContext ctx;

    @BeforeEach
    void setUp() {
        // Initialize decoder with same parameters as production
        decoder = new OpenLesMessageDecoder(10000,
                3, 2, -5 + 2, 0);
    }

    private void logHexDump(String message, ByteBuf buffer) {
        log.info("{} = {}", message, hexDump(buffer));
    }

    @Test
    void testDecodeWithValidSyncBytes() throws Exception {
        // Create a ByteBuf with 5 sync bytes (0xFD) followed by valid message data
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength (header length + empty body + tail)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        
        // Add tail
        buffer.writeShortLE(0xFFFF); // parity

        // Decode the message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        // Assertions
        assertNotNull(message);
        assertEquals(1, message.getLesHeader().getVersion());
        assertEquals(0, message.getLesHeader().getOption());
        assertEquals(21, message.getLesHeader().getMessageLength());
        assertEquals(1, message.getLesHeader().getMessageID_1());
        assertEquals(2, message.getLesHeader().getMessageID_2());
        assertEquals(3, message.getLesHeader().getMessageID_3());
        assertEquals(4, message.getLesHeader().getMessageID_4());
        assertEquals(5, message.getLesHeader().getSource());
        assertEquals(6, message.getLesHeader().getNoArea());
        assertEquals(7, message.getLesHeader().getNoJunc());
        assertEquals(0xFFFF, message.getLesTail().getParity());
        assertEquals(0, message.getLesBody().getBody().length);
    }

    @Test
    void testDecodeWithInsufficientSyncBytes() throws Exception {
        // Create a ByteBuf with only 3 sync bytes (insufficient)
        ByteBuf buffer = Unpooled.buffer();
        
        // Add only 3 sync bytes
        for (int i = 0; i < 3; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Decode should return null as sync bytes are insufficient
        Object result = decoder.decode(ctx, buffer);
        assertNull(result);
    }

    @Test
    void testDecodeWithMessageBody() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength = 21 + bodyData.length; // header length + body length + tail

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(messageLength); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        
        // Add body data
        buffer.writeBytes(bodyData);
        
        // Add tail
        buffer.writeShortLE(0xFFFF); // parity

        // Decode the message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        // Assertions
        assertNotNull(message);
        assertArrayEquals(bodyData, message.getLesBody().getBody());
        assertEquals(messageLength, message.getLesHeader().getMessageLength());
    }

    @Test
    void testDecodeWithMoreSyncSequence() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 3 sync bytes, then an invalid byte, then 5 sync bytes
        for (int i = 0; i < 3; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeByte(0x00); // Invalid byte
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add minimal valid message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // decode should return null due to interrupted sequence
        OpenLesMessage result2 = (OpenLesMessage) decoder.decode(ctx, buffer);

        assertNotNull(result2);
        assertEquals(21, result2.getLesHeader().getMessageLength());
    }

    @Test
    void testDecodeWithEmptyBuffer() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        Object result = decoder.decode(ctx, buffer);
        assertNull(result);
    }

    @Test
    void testDecodeMultipleMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // First message
        // Add 5 sync bytes for first message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data for first message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Second message (with body data)
        // Add 5 sync bytes for second message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength2 = 21 + bodyData.length; // header length + body length + tail

        // Add header data for second message
        buffer.writeShortLE(2); // version
        buffer.writeByte(1); // option
        buffer.writeShortLE(messageLength2); // messageLength
        buffer.writeByte(5); // messageID_1
        buffer.writeByte(6); // messageID_2
        buffer.writeByte(7); // messageID_3
        buffer.writeByte(8); // messageID_4
        buffer.writeByte(9); // source
        buffer.writeByte(10); // noArea
        buffer.writeShortLE(11); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(bodyData); // body
        buffer.writeShortLE(0xAAAA); // parity

        // Decode first message
        OpenLesMessage message1 = (OpenLesMessage) decoder.decode(ctx, buffer);
        
        // Assertions for first message
        assertNotNull(message1);
        assertEquals(1, message1.getLesHeader().getVersion());
        assertEquals(0, message1.getLesHeader().getOption());
        assertEquals(21, message1.getLesHeader().getMessageLength());
        assertEquals(1, message1.getLesHeader().getMessageID_1());
        assertEquals(0xFFFF, message1.getLesTail().getParity());
        assertEquals(0, message1.getLesBody().getBody().length);

        // Decode second message
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        
        // Assertions for second message
        assertNotNull(message2);
        assertEquals(2, message2.getLesHeader().getVersion());
        assertEquals(1, message2.getLesHeader().getOption());
        assertEquals(messageLength2, message2.getLesHeader().getMessageLength());
        assertEquals(5, message2.getLesHeader().getMessageID_1());
        assertEquals(0xAAAA, message2.getLesTail().getParity());
        assertArrayEquals(bodyData, message2.getLesBody().getBody());

        // Verify no more messages
        Object message3 = decoder.decode(ctx, buffer);
        assertNull(message3);
    }

    @Test
    void testDecodeWithMixedValidAndInvalidMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // First message (valid)
        // Add 5 sync bytes for first message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data for first message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Second message (invalid - wrong message length)
        // Add 5 sync bytes for second message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data with invalid message length
        buffer.writeShortLE(2); // version
        buffer.writeByte(1); // option
        buffer.writeShortLE(10); // messageLength (invalid - too small)
        buffer.writeByte(5); // messageID_1
        buffer.writeByte(6); // messageID_2
        buffer.writeByte(7); // messageID_3
        buffer.writeByte(8); // messageID_4
        buffer.writeByte(9); // source
        buffer.writeByte(10); // noArea
        buffer.writeShortLE(11); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xAAAA); // parity

        // Third message (valid with body)
        // Add 5 sync bytes for third message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength3 = 21 + bodyData.length; // header length + body length + tail

        // Add header data for third message
        buffer.writeShortLE(3); // version
        buffer.writeByte(2); // option
        buffer.writeShortLE(messageLength3); // messageLength
        buffer.writeByte(9); // messageID_1
        buffer.writeByte(10); // messageID_2
        buffer.writeByte(11); // messageID_3
        buffer.writeByte(12); // messageID_4
        buffer.writeByte(13); // source
        buffer.writeByte(14); // noArea
        buffer.writeShortLE(15); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(bodyData); // body
        buffer.writeShortLE(0xBBBB); // parity

        // Decode and verify first message (should be valid)
        OpenLesMessage message1 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message1, "First message should be decoded successfully");
        assertEquals(1, message1.getLesHeader().getVersion());
        assertEquals(0, message1.getLesHeader().getOption());
        assertEquals(21, message1.getLesHeader().getMessageLength());
        assertEquals(1, message1.getLesHeader().getMessageID_1());
        assertEquals(0xFFFF, message1.getLesTail().getParity());
        assertEquals(0, message1.getLesBody().getBody().length);

        // Try to decode second message (should be skipped due to invalid length)
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNull(message2, "Second message should be skipped due to invalid length");

        // Decode and verify third message (should be valid)
        OpenLesMessage message3 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message3, "Third message should be decoded successfully");
        assertEquals(3, message3.getLesHeader().getVersion());
        assertEquals(2, message3.getLesHeader().getOption());
        assertEquals(messageLength3, message3.getLesHeader().getMessageLength());
        assertEquals(9, message3.getLesHeader().getMessageID_1());
        assertEquals(0xBBBB, message3.getLesTail().getParity());
        assertArrayEquals(bodyData, message3.getLesBody().getBody());

        // Verify no more messages
        Object message4 = decoder.decode(ctx, buffer);
        assertNull(message4, "No more messages should be available");
    }

    @Test
    void testDecodeWithFragmentedMessages() throws Exception {
        // 创建一个完整的消息
        ByteBuf completeBuffer = Unpooled.buffer();
        
        // 添加同步字节
        for (int i = 0; i < 5; i++) {
            completeBuffer.writeByte(LesConstant.START_FLAG);
        }

        // 添加消息头
        completeBuffer.writeShortLE(1); // version
        completeBuffer.writeByte(0); // option
        completeBuffer.writeShortLE(21 + 5); // messageLength (header + body + tail)
        completeBuffer.writeByte(1); // messageID_1
        completeBuffer.writeByte(2); // messageID_2
        completeBuffer.writeByte(3); // messageID_3
        completeBuffer.writeByte(4); // messageID_4
        completeBuffer.writeByte(5); // source
        completeBuffer.writeByte(6); // noArea
        completeBuffer.writeShortLE(7); // noJunc
        completeBuffer.writeLongLE(System.currentTimeMillis()); // timeStamp

        // 添加5字节的消息体
        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        completeBuffer.writeBytes(bodyData);
        
        // 添加校验位
        completeBuffer.writeShortLE(0xFFFF);

        // 获取完整的字节数组
        byte[] completeMessage = new byte[completeBuffer.readableBytes()];
        completeBuffer.getBytes(0, completeMessage);

        // 第一次接收：只接收同步字节和部分头部
        ByteBuf firstPart = Unpooled.buffer();
        firstPart.writeBytes(completeMessage, 0, 10);
        
        // 尝试解码第一部分（应该返回null因为数据不完整）
        Object result1 = decoder.decode(ctx, firstPart);
        assertNull(result1, "Decoding incomplete message should return null");

        // 第二次接收：接收剩余的头部数据
        firstPart.writeBytes(completeMessage, 10, 11);
        
        // 尝试解码（应该还是返回null因为数据仍不完整）
        Object result2 = decoder.decode(ctx, firstPart);
        assertNull(result2, "Decoding incomplete message should return null");

        // 第三次接收：接收消息体数据
        firstPart.writeBytes(completeMessage, 21, 5 + 5);
        
        // 尝试解码（应该还是返回null因为还没有接收到校验位）
        Object result3 = decoder.decode(ctx, firstPart);
        assertNull(result3, "Decoding message without tail should return null");

        // 最后接收：接收校验位
        firstPart.writeBytes(completeMessage, 26 + 5, 2);
        
        // 最终解码（应该成功解码完整消息）
        OpenLesMessage finalResult = (OpenLesMessage) decoder.decode(ctx, firstPart);
        
        // 验证解码结果
        assertNotNull(finalResult, "Final decoding should succeed");
        assertEquals(1, finalResult.getLesHeader().getVersion());
        assertEquals(0, finalResult.getLesHeader().getOption());
        assertEquals(21 + 5, finalResult.getLesHeader().getMessageLength());
        assertEquals(1, finalResult.getLesHeader().getMessageID_1());
        assertEquals(2, finalResult.getLesHeader().getMessageID_2());
        assertEquals(3, finalResult.getLesHeader().getMessageID_3());
        assertEquals(4, finalResult.getLesHeader().getMessageID_4());
        assertEquals(5, finalResult.getLesHeader().getSource());
        assertEquals(6, finalResult.getLesHeader().getNoArea());
        assertEquals(7, finalResult.getLesHeader().getNoJunc());
        assertArrayEquals(bodyData, finalResult.getLesBody().getBody());
        assertEquals(0xFFFF, finalResult.getLesTail().getParity());

        // 验证缓冲区已经被完全读取
        assertEquals(0, firstPart.readableBytes(), "Buffer should be fully consumed");
        
        firstPart.release();
    }

    @Test
    void testDecodeWithFragmentedMultipleMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // 第一个消息的第一部分（同步字节和部分头部）
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength

        // 尝试解码第一部分（应该返回null）
        Object result1 = decoder.decode(ctx, buffer);
        assertNull(result1, "Decoding incomplete first message should return null");

        // 添加第一个消息的剩余部分
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // 解码第一个完整消息
        OpenLesMessage message1 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message1, "First complete message should be decoded");
        assertEquals(1, message1.getLesHeader().getVersion());
        assertEquals(21, message1.getLesHeader().getMessageLength());

        // 添加第二个消息的部分数据（带消息体）
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength2 = 21 + bodyData.length;

        buffer.writeShortLE(2); // version
        buffer.writeByte(1); // option
        buffer.writeShortLE(messageLength2); // messageLength
        buffer.writeByte(5); // messageID_1

        // 尝试解码不完整的第二个消息（应该返回null）
        Object result2 = decoder.decode(ctx, buffer);
        assertNull(result2, "Decoding incomplete second message should return null");

        // 添加第二个消息的剩余数据
        buffer.writeByte(6); // messageID_2
        buffer.writeByte(7); // messageID_3
        buffer.writeByte(8); // messageID_4
        buffer.writeByte(9); // source
        buffer.writeByte(10); // noArea
        buffer.writeShortLE(11); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(bodyData); // body
        buffer.writeShortLE(0xAAAA); // parity

        // 解码完整的第二个消息
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message2, "Second complete message should be decoded");
        assertEquals(2, message2.getLesHeader().getVersion());
        assertEquals(messageLength2, message2.getLesHeader().getMessageLength());
        assertArrayEquals(bodyData, message2.getLesBody().getBody());

        buffer.release();
    }

    @Test
    void testDecodeWithLargeMessageBody() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Create large body data (6KB)
        byte[] largeBodyData = new byte[6 * 1024];
        for (int i = 0; i < largeBodyData.length; i++) {
            largeBodyData[i] = (byte)(i % 256);
        }
        int messageLength = 21 + largeBodyData.length;

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(messageLength); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(largeBodyData);
        buffer.writeShortLE(0xFFFF);

        // Decode message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        
        assertNotNull(message, "Large message should be decoded successfully");
        assertEquals(messageLength, message.getLesHeader().getMessageLength());
        assertArrayEquals(largeBodyData, message.getLesBody().getBody());

        buffer.release();
    }

    @Test
    void testDecodeWithInvalidMessageLength() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with invalid length (too large)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(12000); // messageLength (maximum unsigned short value)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF);

        // Decode should return null for invalid length
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with invalid length should not be decoded");

        buffer.release();
    }


    @Test
    void testDecodeWithInvalidMessageLengthAndRight() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with invalid length (too large)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(12000); // messageLength (maximum unsigned short value)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF);


        // First message (valid)
        // Add 5 sync bytes for first message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data for first message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should return null for invalid length
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with invalid length should not be decoded");

        buffer.release();
    }

    @Test
    void testDecodeWithNegativeBodyLength() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with message length smaller than header length
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(10); // messageLength (smaller than header length)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF);

        // Decode should return null for negative body length
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with negative body length should not be decoded");

        buffer.release();
    }

    @Test
    void testDecodeWithIncompleteParityBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeByte(0xFF); // Only one byte of parity

        // Decode should return null for incomplete parity
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with incomplete parity should not be decoded");

        buffer.release();
    }

    @Test
    void testDecodeWithInterferenceInSyncBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        
        // Add 3 sync bytes, then interference, then 5 sync bytes
        for (int i = 0; i < 3; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeByte(0x00); // Interference byte
        buffer.writeByte(0xFF); // Interference byte
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add valid message data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity


        //  decode attempt should succeed
        OpenLesMessage result2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(result2, " decode after interference should succeed");
        assertEquals(1, result2.getLesHeader().getVersion());
        assertEquals(21, result2.getLesHeader().getMessageLength());

        buffer.release();
    }
} 
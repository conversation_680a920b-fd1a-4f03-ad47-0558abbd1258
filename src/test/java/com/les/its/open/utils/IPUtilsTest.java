package com.les.its.open.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("IPUtils Tests")
class IPUtilsTest {

    @Mock
    private HttpServletRequest mockRequest;

    @Test
    @DisplayName("getHostIp should return IP from CF-Connecting-IP header when available")
    void getHostIp_WhenCfConnectingIpHeaderAvailable_ShouldReturnIp() {
        // Arrange
        String expectedIp = "*************";
        when(mockRequest.getHeader("CF-Connecting-IP")).thenReturn(expectedIp);

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(expectedIp);
        verify(mockRequest).getHeader("CF-Connecting-IP");
    }

    @Test
    @DisplayName("getHostIp should return IP from X-Real-IP header when CF-Connecting-IP not available")
    void getHostIp_WhenXRealIpHeaderAvailable_ShouldReturnIp() {
        // Arrange
        String expectedIp = "*************";
        when(mockRequest.getHeader("CF-Connecting-IP")).thenReturn(null);
        when(mockRequest.getHeader("True-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("X-Original-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(expectedIp);

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(expectedIp);
        verify(mockRequest).getHeader("X-Real-IP");
    }

    @Test
    @DisplayName("getHostIp should return IP from X-Forwarded-For header and handle multiple IPs")
    void getHostIp_WhenXForwardedForHasMultipleIps_ShouldReturnFirstPublicIp() {
        // Arrange
        String forwardedFor = "*************, ***********, ********";
        when(mockRequest.getHeader("CF-Connecting-IP")).thenReturn(null);
        when(mockRequest.getHeader("True-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("X-Original-Forwarded-For")).thenReturn(null);
        when(mockRequest.getHeader("X-Client-IP")).thenReturn(null);
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(null);
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(forwardedFor);

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo("*************");
    }

    @Test
    @DisplayName("getHostIp should fall back to remote address when no headers available")
    void getHostIp_WhenNoHeadersAvailable_ShouldFallBackToRemoteAddress() {
        // Arrange
        String remoteAddr = "*************";
        when(mockRequest.getRemoteAddr()).thenReturn(remoteAddr);
        
        // Mock all headers as null or invalid
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Real-IP", "X-Forwarded-For", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(remoteAddr);
        verify(mockRequest).getRemoteAddr();
    }

    @Test
    @DisplayName("getHostIp should handle IPv6 localhost and normalize to IPv4")
    void getHostIp_WhenIpv6Localhost_ShouldNormalizeToIpv4() {
        // Arrange
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("::1");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isNull(); // localhost should be filtered out as invalid public IP
    }

    @Test
    @DisplayName("getHostIp should handle IPv4-mapped IPv6 addresses")
    void getHostIp_WhenIpv4MappedIpv6_ShouldNormalizeToIpv4() {
        // Arrange
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("::ffff:*************");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo("*************");
    }

    @Test
    @DisplayName("getHostIp should handle IP with port and extract pure IP")
    void getHostIp_WhenIpHasPort_ShouldExtractPureIp() {
        // Arrange
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("*************:8080");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo("*************");
    }

    @Test
    @DisplayName("getHostIp should handle IPv6 with port in brackets")
    void getHostIp_WhenIpv6HasPortInBrackets_ShouldExtractPureIp() {
        // Arrange
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("[2001:db8::1]:8080");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo("2001:db8::1");
    }

    @Test
    @DisplayName("getHostIp should return null for private IP addresses")
    void getHostIp_WhenPrivateIp_ShouldReturnNull() {
        // Arrange
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("***********");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("getHostIp should return null for localhost IP")
    void getHostIp_WhenLocalhostIp_ShouldReturnNull() {
        // Arrange
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("127.0.0.1");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("getHostIp should handle unknown header values")
    void getHostIp_WhenHeaderValueIsUnknown_ShouldIgnoreHeader() {
        // Arrange
        String remoteAddr = "*************";
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("unknown");
        when(mockRequest.getRemoteAddr()).thenReturn(remoteAddr);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Forwarded-For", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(remoteAddr);
    }

    @Test
    @DisplayName("getHostIp should handle empty header values")
    void getHostIp_WhenHeaderValueIsEmpty_ShouldIgnoreHeader() {
        // Arrange
        String remoteAddr = "*************";
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("");
        when(mockRequest.getRemoteAddr()).thenReturn(remoteAddr);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Forwarded-For", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(remoteAddr);
    }

    @Test
    @DisplayName("getHostIp should handle whitespace-only header values")
    void getHostIp_WhenHeaderValueIsWhitespaceOnly_ShouldIgnoreHeader() {
        // Arrange
        String remoteAddr = "*************";
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("   ");
        when(mockRequest.getRemoteAddr()).thenReturn(remoteAddr);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Forwarded-For", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(remoteAddr);
    }

    @Test
    @DisplayName("getHostIp should handle invalid IP format")
    void getHostIp_WhenIpFormatIsInvalid_ShouldFallBackToRemoteAddress() {
        // Arrange
        String remoteAddr = "*************";
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("invalid.ip.format");
        when(mockRequest.getRemoteAddr()).thenReturn(remoteAddr);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Forwarded-For", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(remoteAddr);
    }

    @Test
    @DisplayName("getHostIp should prefer first public IP from comma-separated list")
    void getHostIp_WhenCommaSeparatedListWithMixedIps_ShouldPreferFirstPublicIp() {
        // Arrange
        String forwardedFor = "***********, *************, *************";
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(forwardedFor);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Real-IP", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo("*************");
    }

    @Test
    @DisplayName("getHostIp should return first valid IP when no public IP in list")
    void getHostIp_WhenNoPublicIpInList_ShouldReturnFirstValidIp() {
        // Arrange
        String forwardedFor = "***********, ********, **********";
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn(forwardedFor);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Real-IP", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isNull(); // Should return null as no public IP found
    }

    @Test
    @DisplayName("IPUtils should be a utility class")
    void ipUtils_ShouldBeUtilityClass() {
        // Arrange
        Constructor<?>[] constructors = IPUtils.class.getDeclaredConstructors();

        // Assert - Should have only one constructor (default)
        assertThat(constructors).hasSize(1);
        
        // The default constructor should be public
        Constructor<?> defaultConstructor = constructors[0];
        assertThat(defaultConstructor.getParameterCount()).isEqualTo(0);
        assertThat(Modifier.isPublic(defaultConstructor.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("All public methods should be static")
    void allPublicMethods_ShouldBeStatic() {
        // Arrange
        Method[] methods = IPUtils.class.getDeclaredMethods();

        // Assert
        for (Method method : methods) {
            if (Modifier.isPublic(method.getModifiers())) {
                assertThat(Modifier.isStatic(method.getModifiers()))
                        .as("Public method %s should be static", method.getName())
                        .isTrue();
            }
        }
    }

    @Test
    @DisplayName("getHostIp should handle null request gracefully")
    void getHostIp_WhenRequestIsNull_ShouldThrowException() {
        // Act & Assert
        assertThatThrownBy(() -> IPUtils.getHostIp(null))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("getHostIp should handle valid IPv6 addresses")
    void getHostIp_WhenValidIpv6Address_ShouldReturnIp() {
        // Arrange
        String ipv6Address = "2001:db8:85a3::8a2e:370:7334";
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(ipv6Address);

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(ipv6Address);
    }

    @Test
    @DisplayName("getHostIp should handle malformed IPv6 addresses")
    void getHostIp_WhenMalformedIpv6Address_ShouldFallBackToRemoteAddress() {
        // Arrange
        String malformedIpv6 = "2001:db8:85a3::8a2e:370:7334:extra";
        String remoteAddr = "*************";
        when(mockRequest.getHeader("X-Real-IP")).thenReturn(malformedIpv6);
        when(mockRequest.getRemoteAddr()).thenReturn(remoteAddr);
        
        // Mock other headers as null
        for (String header : new String[]{"CF-Connecting-IP", "True-Client-IP", "X-Original-Forwarded-For", 
                                         "X-Client-IP", "X-Forwarded-For", "Proxy-Client-IP", 
                                         "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_For"}) {
            when(mockRequest.getHeader(header)).thenReturn(null);
        }

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo(remoteAddr);
    }

    @Test
    @DisplayName("getHostIp should check headers in correct priority order")
    void getHostIp_ShouldCheckHeadersInCorrectPriorityOrder() {
        // Arrange
        when(mockRequest.getHeader("CF-Connecting-IP")).thenReturn(null);
        when(mockRequest.getHeader("True-Client-IP")).thenReturn("*************");
        when(mockRequest.getHeader("X-Real-IP")).thenReturn("*************");

        // Act
        String result = IPUtils.getHostIp(mockRequest);

        // Assert
        assertThat(result).isEqualTo("*************"); // Should return True-Client-IP as it has higher priority
        verify(mockRequest).getHeader("CF-Connecting-IP");
        verify(mockRequest).getHeader("True-Client-IP");
        verify(mockRequest, never()).getHeader("X-Real-IP"); // Should not check lower priority header
    }
}
package com.les.its.open.utils;

import com.les.its.open.protocol.openles.message.OpenLesMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Crc16Utils Tests")
class Crc16UtilsTest {

    @Mock
    private OpenLesMessage mockOpenLesMessage;

    @Test
    @DisplayName("calculate with OpenLesMessage should return correct CRC value")
    void calculate_WithOpenLesMessage_ShouldReturnCorrectCrcValue() {
        // Arrange
        byte[] testData = "Hello World".getBytes();
        when(mockOpenLesMessage.genSendBodyWithOutParity()).thenReturn(testData);

        // Act
        int result = Crc16Utils.calculate(mockOpenLesMessage);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
        verify(mockOpenLesMessage).genSendBodyWithOutParity();
    }

    @Test
    @DisplayName("calculate with OpenLesMessage should handle null message gracefully")
    void calculate_WithNullOpenLesMessage_ShouldThrowException() {
        // Act & Assert
        assertThatThrownBy(() -> Crc16Utils.calculate((OpenLesMessage) null))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("calculate with byte array should return consistent CRC values")
    void calculate_WithByteArray_ShouldReturnConsistentCrcValues() {
        // Arrange
        byte[] testData = "Hello World".getBytes();

        // Act
        int result1 = Crc16Utils.calculate(testData);
        int result2 = Crc16Utils.calculate(testData);

        // Assert
        assertThat(result1).isEqualTo(result2); // Should be consistent
        assertThat(result1).isNotNegative();
        assertThat(result1).isLessThanOrEqualTo(0xFFFF); // 16-bit value
    }

    @Test
    @DisplayName("calculate with byte array parameters should return correct CRC value")
    void calculate_WithByteArrayParameters_ShouldReturnCorrectCrcValue() {
        // Arrange
        byte[] testData = "Hello World".getBytes();
        int src = 0;
        int length = testData.length;

        // Act
        int result = Crc16Utils.calculate(testData, src, length);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
    }

    @Test
    @DisplayName("calculate with different byte arrays should return different CRC values")
    void calculate_WithDifferentByteArrays_ShouldReturnDifferentCrcValues() {
        // Arrange
        byte[] testData1 = "Hello World".getBytes();
        byte[] testData2 = "Hello Universe".getBytes();

        // Act
        int result1 = Crc16Utils.calculate(testData1);
        int result2 = Crc16Utils.calculate(testData2);

        // Assert
        assertThat(result1).isNotEqualTo(result2); // Different data should produce different CRC
    }

    @Test
    @DisplayName("calculate with empty byte array should return valid CRC value")
    void calculate_WithEmptyByteArray_ShouldReturnValidCrcValue() {
        // Arrange
        byte[] emptyData = new byte[0];

        // Act
        int result = Crc16Utils.calculate(emptyData);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
    }

    @Test
    @DisplayName("calculate with null byte array should throw exception")
    void calculate_WithNullByteArray_ShouldThrowException() {
        // Act & Assert
        assertThatThrownBy(() -> Crc16Utils.calculate((byte[]) null))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("calculate with partial byte array should work correctly")
    void calculate_WithPartialByteArray_ShouldWorkCorrectly() {
        // Arrange
        byte[] testData = "Hello World".getBytes();
        int src = 6; // Start from "World"
        int length = 5; // "World".length()

        // Act
        int result = Crc16Utils.calculate(testData, src, length);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
        
        // Should be same as calculating "World" directly
        int directResult = Crc16Utils.calculate("World".getBytes());
        assertThat(result).isEqualTo(directResult);
    }

    @Test
    @DisplayName("calculate with invalid array bounds should throw exception")
    void calculate_WithInvalidArrayBounds_ShouldThrowException() {
        // Arrange
        byte[] testData = "Hello".getBytes();

        // Act & Assert
        assertThatThrownBy(() -> Crc16Utils.calculate(testData, 0, 10))
                .isInstanceOf(ArrayIndexOutOfBoundsException.class);
    }

    @Test
    @DisplayName("calculate with negative source index should throw exception")
    void calculate_WithNegativeSourceIndex_ShouldThrowException() {
        // Arrange
        byte[] testData = "Hello".getBytes();

        // Act & Assert
        assertThatThrownBy(() -> Crc16Utils.calculate(testData, -1, 3))
                .isInstanceOf(ArrayIndexOutOfBoundsException.class);
    }

    @Test
    @DisplayName("calculate with negative length should throw exception")
    void calculate_WithNegativeLength_ShouldThrowException() {
        // Arrange
        byte[] testData = "Hello".getBytes();

        // Act & Assert
        assertThatThrownBy(() -> Crc16Utils.calculate(testData, 0, -1))
                .isInstanceOf(IndexOutOfBoundsException.class);
    }

    @Test
    @DisplayName("calculate with zero length should return valid CRC value")
    void calculate_WithZeroLength_ShouldReturnValidCrcValue() {
        // Arrange
        byte[] testData = "Hello".getBytes();

        // Act
        int result = Crc16Utils.calculate(testData, 0, 0);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
    }

    @Test
    @DisplayName("calculate should handle large byte arrays")
    void calculate_ShouldHandleLargeByteArrays() {
        // Arrange
        byte[] largeData = new byte[10000];
        for (int i = 0; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }

        // Act
        int result = Crc16Utils.calculate(largeData);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
    }

    @Test
    @DisplayName("overloaded calculate methods should return same result for same data")
    void overloadedCalculateMethods_ShouldReturnSameResultForSameData() {
        // Arrange
        byte[] testData = "Test Data".getBytes();

        // Act
        int result1 = Crc16Utils.calculate(testData);
        int result2 = Crc16Utils.calculate(testData, 0, testData.length);

        // Assert
        assertThat(result1).isEqualTo(result2);
    }

    @Test
    @DisplayName("main method should not throw exception")
    void mainMethod_ShouldNotThrowException() {
        // Arrange
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PrintStream originalOut = System.out;
        System.setOut(new PrintStream(outputStream));

        try {
            // Act
            assertThatCode(() -> Crc16Utils.main(new String[]{}))
                    .doesNotThrowAnyException();

            // Assert
            String output = outputStream.toString();
            assertThat(output).contains("CRC-16-IBM:");
            assertThat(output).contains("0x");
        } finally {
            System.setOut(originalOut);
        }
    }

    @Test
    @DisplayName("Crc16Utils should be a utility class")
    void crc16Utils_ShouldBeUtilityClass() {
        // Arrange
        Constructor<?>[] constructors = Crc16Utils.class.getDeclaredConstructors();

        // Assert - Should have only one constructor (default)
        assertThat(constructors).hasSize(1);
        
        // The default constructor should be public
        Constructor<?> defaultConstructor = constructors[0];
        assertThat(defaultConstructor.getParameterCount()).isEqualTo(0);
        assertThat(Modifier.isPublic(defaultConstructor.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("All methods should be static")
    void allMethods_ShouldBeStatic() {
        // Arrange
        Method[] methods = Crc16Utils.class.getDeclaredMethods();

        // Assert
        for (Method method : methods) {
            if (!method.getName().equals("main")) { // main method is always static
                assertThat(Modifier.isStatic(method.getModifiers()))
                        .as("Method %s should be static", method.getName())
                        .isTrue();
            }
        }
    }

    @Test
    @DisplayName("calculate should handle special byte values")
    void calculate_ShouldHandleSpecialByteValues() {
        // Arrange
        byte[] specialData = {0x00, (byte) 0xFF, 0x7F, (byte) 0x80, 0x01, (byte) 0xFE};

        // Act
        int result = Crc16Utils.calculate(specialData);

        // Assert
        assertThat(result).isNotNegative();
        assertThat(result).isLessThanOrEqualTo(0xFFFF); // 16-bit value
    }

    @Test
    @DisplayName("CRC calculation should be deterministic")
    void crcCalculation_ShouldBeDeterministic() {
        // Arrange
        byte[] testData = "Deterministic Test".getBytes();

        // Act - Calculate multiple times
        int result1 = Crc16Utils.calculate(testData);
        int result2 = Crc16Utils.calculate(testData);
        int result3 = Crc16Utils.calculate(testData);

        // Assert - All results should be identical
        assertThat(result1).isEqualTo(result2);
        assertThat(result2).isEqualTo(result3);
        assertThat(result1).isEqualTo(result3);
    }

    @Test
    @DisplayName("CRC calculation should detect single bit changes")
    void crcCalculation_ShouldDetectSingleBitChanges() {
        // Arrange
        byte[] originalData = "Test Data".getBytes();
        byte[] modifiedData = originalData.clone();
        modifiedData[0] = (byte) (modifiedData[0] ^ 0x01); // Flip one bit

        // Act
        int originalCrc = Crc16Utils.calculate(originalData);
        int modifiedCrc = Crc16Utils.calculate(modifiedData);

        // Assert
        assertThat(originalCrc).isNotEqualTo(modifiedCrc);
    }
}
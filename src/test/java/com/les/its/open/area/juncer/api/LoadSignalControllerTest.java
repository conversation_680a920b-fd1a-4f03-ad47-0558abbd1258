package com.les.its.open.area.juncer.api;

import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabLoadService;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LoadSignalController Tests")
class LoadSignalControllerTest {

    @Mock
    private TabLoadService tabLoadService;

    @InjectMocks
    private LoadSignalController loadSignalController;

    private static final String TEST_CONTROLLER_ID = "TEST_CONTROLLER_001";
    private static final String VALID_PARAM_TYPE_HEX = "0200";
    private static final String INVALID_PARAM_TYPE_HEX = "0000";
    private static final String TEST_DATA = "{\"param1\":\"value1\",\"param2\":\"value2\"}";

    @BeforeEach
    void setUp() {
        // Setup can be extended as needed
    }

    @Test
    @DisplayName("loadSignalController should return success when service loads data successfully")
    void loadSignalController_WhenServiceLoadsDataSuccessfully_ShouldReturnSuccess() {
        // Arrange
        Object expectedResult = "Load operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getErrorCode()).isEqualTo("20000");
            assertThat(result.getMessage()).isEqualTo("获取信号参数正常");
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA);
        }
    }

    @Test
    @DisplayName("loadSignalController should return error when service fails to load")
    void loadSignalController_WhenServiceFailsToLoad_ShouldReturnError() {
        // Arrange
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA))
                    .thenReturn(Optional.empty());

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getMessage()).isEqualTo(ResultCode.FAILED_LOAD.message());
            
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA);
        }
    }

    @Test
    @DisplayName("loadSignalController should return error when param type is PARAM_UNKNOWN")
    void loadSignalController_WhenParamTypeIsUnknown_ShouldReturnError() {
        // Arrange
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0000))
                    .thenReturn(ParamMsgType.PARAM_UNKNOWN);

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, INVALID_PARAM_TYPE_HEX, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getMessage()).isEqualTo(ResultCode.NOT_SUPPORT_LOOK_TYPE.message());
            
            verify(tabLoadService, never()).load(anyString(), any(ParamMsgType.class), anyString());
        }
    }

    @Test
    @DisplayName("loadSignalController should handle invalid hex format in parameter type")
    void loadSignalController_WhenInvalidHexFormat_ShouldThrowNumberFormatException() {
        // Arrange
        String invalidHexFormat = "INVALID_HEX";

        // Act & Assert
        assertThatThrownBy(() -> 
            loadSignalController.loadSignalController(TEST_CONTROLLER_ID, invalidHexFormat, TEST_DATA)
        ).isInstanceOf(NumberFormatException.class);
        
        verify(tabLoadService, never()).load(anyString(), any(ParamMsgType.class), anyString());
    }

    @Test
    @DisplayName("loadSignalController should handle null parameter type")
    void loadSignalController_WhenParameterTypeIsNull_ShouldThrowNumberFormatException() {
        // Act & Assert
        assertThatThrownBy(() -> 
            loadSignalController.loadSignalController(TEST_CONTROLLER_ID, null, TEST_DATA)
        ).isInstanceOf(NumberFormatException.class);
        
        verify(tabLoadService, never()).load(anyString(), any(ParamMsgType.class), anyString());
    }

    @Test
    @DisplayName("loadSignalController should handle empty parameter type")
    void loadSignalController_WhenParameterTypeIsEmpty_ShouldThrowNumberFormatException() {
        // Act & Assert
        assertThatThrownBy(() -> 
            loadSignalController.loadSignalController(TEST_CONTROLLER_ID, "", TEST_DATA)
        ).isInstanceOf(NumberFormatException.class);
        
        verify(tabLoadService, never()).load(anyString(), any(ParamMsgType.class), anyString());
    }

    @Test
    @DisplayName("loadSignalController should handle null controller ID")
    void loadSignalController_WhenControllerIdIsNull_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Load operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(null, mockParamType, TEST_DATA))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    null, VALID_PARAM_TYPE_HEX, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLoadService).load(null, mockParamType, TEST_DATA);
        }
    }

    @Test
    @DisplayName("loadSignalController should handle empty controller ID")
    void loadSignalController_WhenControllerIdIsEmpty_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Load operation successful";
        String emptyControllerId = "";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(emptyControllerId, mockParamType, TEST_DATA))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    emptyControllerId, VALID_PARAM_TYPE_HEX, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLoadService).load(emptyControllerId, mockParamType, TEST_DATA);
        }
    }

    @Test
    @DisplayName("loadSignalController should handle null data")
    void loadSignalController_WhenDataIsNull_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Load operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, null))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, null);
        }
    }

    @Test
    @DisplayName("loadSignalController should handle empty data")
    void loadSignalController_WhenDataIsEmpty_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Load operation successful";
        String emptyData = "";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, emptyData))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, emptyData);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, emptyData);
        }
    }

    @Test
    @DisplayName("loadSignalController should handle service throwing exception")
    void loadSignalController_WhenServiceThrowsException_ShouldPropagateException() {
        // Arrange
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA))
                    .thenThrow(new RuntimeException("Service error"));

            // Act & Assert
            assertThatThrownBy(() -> 
                loadSignalController.loadSignalController(TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_DATA)
            ).isInstanceOf(RuntimeException.class)
             .hasMessage("Service error");
        }
    }

    @Test
    @DisplayName("loadSignalController should parse hex parameter type correctly")
    void loadSignalController_ShouldParseHexParameterTypeCorrectly() {
        // Arrange
        String hexParamType = "ABCD";
        Object expectedResult = "Load operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0xABCD))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, hexParamType, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            
            mockedParamMsgType.verify(() -> ParamMsgType.fromCode(0xABCD));
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA);
        }
    }

    @Test
    @DisplayName("loadSignalController should handle lowercase hex parameter type")
    void loadSignalController_ShouldHandleLowercaseHexParameterType() {
        // Arrange
        String lowercaseHexParamType = "abc";
        Object expectedResult = "Load operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0xABC))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, lowercaseHexParamType, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            
            mockedParamMsgType.verify(() -> ParamMsgType.fromCode(0xABC));
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA);
        }
    }

    @Test
    @DisplayName("Controller should have correct REST mapping annotations")
    void controller_ShouldHaveCorrectRestMappingAnnotations() {
        // Assert
        assertThat(LoadSignalController.class.isAnnotationPresent(RestController.class)).isTrue();
        assertThat(LoadSignalController.class.isAnnotationPresent(RequestMapping.class)).isTrue();
        
        RequestMapping requestMapping = LoadSignalController.class.getAnnotation(RequestMapping.class);
        assertThat(requestMapping.value()).containsExactly("/openles/load");
    }

    @Test
    @DisplayName("loadSignalController method should have correct POST mapping")
    void loadSignalControllerMethod_ShouldHaveCorrectPostMapping() throws NoSuchMethodException {
        // Arrange
        var method = LoadSignalController.class.getMethod("loadSignalController", String.class, String.class, String.class);

        // Assert
        assertThat(method.isAnnotationPresent(PostMapping.class)).isTrue();
        
        PostMapping postMapping = method.getAnnotation(PostMapping.class);
        assertThat(postMapping.value()).containsExactly("/{controllerId}/{paramMsgType}");
    }

    @Test
    @DisplayName("loadSignalController should handle complex JSON data")
    void loadSignalController_WhenComplexJsonData_ShouldHandleCorrectly() {
        // Arrange
        String complexJsonData = "{\"array\":[1,2,3],\"nested\":{\"prop\":\"value\"},\"number\":123}";
        Object expectedResult = "Complex load successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, complexJsonData))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, complexJsonData);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, complexJsonData);
        }
    }

    @Test
    @DisplayName("loadSignalController should handle very large hex values")
    void loadSignalController_WhenVeryLargeHexValues_ShouldHandleCorrectly() {
        // Arrange
        String largeHexParamType = "FFFF";
        Object expectedResult = "Load operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0xFFFF))
                    .thenReturn(mockParamType);
            
            when(tabLoadService.load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = loadSignalController.loadSignalController(
                    TEST_CONTROLLER_ID, largeHexParamType, TEST_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            
            mockedParamMsgType.verify(() -> ParamMsgType.fromCode(0xFFFF));
            verify(tabLoadService).load(TEST_CONTROLLER_ID, mockParamType, TEST_DATA);
        }
    }
}
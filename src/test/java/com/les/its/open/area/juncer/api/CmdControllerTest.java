package com.les.its.open.area.juncer.api;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.cmd.TabSetStage;
import com.les.its.open.area.net.proc.TabCmdService;
import com.les.its.open.utils.ResultCode;
import com.les.its.open.utils.ResultVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CmdController Tests")
class CmdControllerTest {

    @Mock
    private TabCmdService tabCmdService;

    @InjectMocks
    private CmdController cmdController;

    private MockMvc mockMvc;

    private static final String TEST_CROSSING_ID = "TEST_CROSSING_001";
    private static final String VALID_JSON_DATA = "{\"stageNo\":1,\"subCrossingNo\":1}";
    private static final String INVALID_JSON_DATA = "{invalid json}";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(cmdController).build();
    }

    @Test
    @DisplayName("setStage should return success when service returns result")
    void setStage_WhenServiceReturnsResult_ShouldReturnSuccess() {
        // Arrange
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setStageNo(1);
        tabSetStage.setCrossingSeqNo(1);

        Object expectedResult = new Object();
        
        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(VALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(tabSetStage);
            
            when(tabCmdService.setStage(TEST_CROSSING_ID, tabSetStage))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, VALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
            assertThat(result.getData()).isEqualTo(expectedResult);
            verify(tabCmdService).setStage(TEST_CROSSING_ID, tabSetStage);
        }
    }

    @Test
    @DisplayName("setStage should return failure when service returns empty")
    void setStage_WhenServiceReturnsEmpty_ShouldReturnFailure() {
        // Arrange
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setStageNo(1);
        tabSetStage.setCrossingSeqNo(1);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(VALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(tabSetStage);
            
            when(tabCmdService.setStage(TEST_CROSSING_ID, tabSetStage))
                    .thenReturn(Optional.empty());

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, VALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_CMD.getCode());
            verify(tabCmdService).setStage(TEST_CROSSING_ID, tabSetStage);
        }
    }

    @Test
    @DisplayName("setStage should return parameter error when JSON parsing fails")
    void setStage_WhenJsonParsingFails_ShouldReturnParameterError() {
        // Arrange
        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(INVALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(null);

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, INVALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_SET_STAGE_PARAM.getCode());
            verify(tabCmdService, never()).setStage(anyString(), any(TabSetStage.class));
        }
    }

    @Test
    @DisplayName("setStage should return parameter error when JSON parsing returns null")
    void setStage_WhenJsonParsingReturnsNull_ShouldReturnParameterError() {
        // Arrange
        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(anyString(), eq(TabSetStage.class)))
                    .thenReturn(null);

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, VALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_SET_STAGE_PARAM.getCode());
            verify(tabCmdService, never()).setStage(anyString(), any(TabSetStage.class));
        }
    }

    @Test
    @DisplayName("setStage should handle empty crossing ID")
    void setStage_WhenCrossingIdIsEmpty_ShouldStillCallService() {
        // Arrange
        String emptyCrossingId = "";
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setStageNo(1);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(VALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(tabSetStage);
            
            when(tabCmdService.setStage(emptyCrossingId, tabSetStage))
                    .thenReturn(Optional.empty());

            // Act
            ResultVo<?> result = cmdController.setStage(emptyCrossingId, VALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_CMD.getCode());
            verify(tabCmdService).setStage(emptyCrossingId, tabSetStage);
        }
    }

    @Test
    @DisplayName("setStage should handle null crossing ID")
    void setStage_WhenCrossingIdIsNull_ShouldStillCallService() {
        // Arrange
        String nullCrossingId = null;
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setStageNo(1);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(VALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(tabSetStage);
            
            when(tabCmdService.setStage(nullCrossingId, tabSetStage))
                    .thenReturn(Optional.empty());

            // Act
            ResultVo<?> result = cmdController.setStage(nullCrossingId, VALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_CMD.getCode());
            verify(tabCmdService).setStage(nullCrossingId, tabSetStage);
        }
    }

    @Test
    @DisplayName("setStage should handle service throwing exception")
    void setStage_WhenServiceThrowsException_ShouldPropagateException() {
        // Arrange
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setStageNo(1);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(VALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(tabSetStage);
            
            when(tabCmdService.setStage(TEST_CROSSING_ID, tabSetStage))
                    .thenThrow(new RuntimeException("Service error"));

            // Act & Assert
            assertThatThrownBy(() -> cmdController.setStage(TEST_CROSSING_ID, VALID_JSON_DATA))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessage("Service error");
        }
    }

    @Test
    @DisplayName("setStage should handle empty JSON data")
    void setStage_WhenJsonDataIsEmpty_ShouldReturnParameterError() {
        // Arrange
        String emptyData = "";

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(emptyData, TabSetStage.class))
                    .thenReturn(null);

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, emptyData);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_SET_STAGE_PARAM.getCode());
            verify(tabCmdService, never()).setStage(anyString(), any(TabSetStage.class));
        }
    }

    @Test
    @DisplayName("setStage should handle null JSON data")
    void setStage_WhenJsonDataIsNull_ShouldReturnParameterError() {
        // Arrange
        String nullData = null;

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(nullData, TabSetStage.class))
                    .thenReturn(null);

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, nullData);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.FAILED_SET_STAGE_PARAM.getCode());
            verify(tabCmdService, never()).setStage(anyString(), any(TabSetStage.class));
        }
    }

    @Test
    @DisplayName("Controller should have correct REST mapping annotations")
    void controller_ShouldHaveCorrectRestMappingAnnotations() {
        // Assert
        assertThat(CmdController.class.isAnnotationPresent(RestController.class)).isTrue();
        assertThat(CmdController.class.isAnnotationPresent(RequestMapping.class)).isTrue();
        
        RequestMapping requestMapping = CmdController.class.getAnnotation(RequestMapping.class);
        assertThat(requestMapping.value()).containsExactly("/openles/cmd");
    }

    @Test
    @DisplayName("setStage method should have correct GET mapping")
    void setStageMethod_ShouldHaveCorrectGetMapping() throws NoSuchMethodException {
        // Arrange
        var method = CmdController.class.getMethod("setStage", String.class, String.class);

        // Assert
        assertThat(method.isAnnotationPresent(GetMapping.class)).isTrue();
        
        GetMapping getMapping = method.getAnnotation(GetMapping.class);
        assertThat(getMapping.value()).containsExactly("/{crossingId}");
    }

    @Test
    @DisplayName("setStage should work with valid TabSetStage object")
    void setStage_WithValidTabSetStageObject_ShouldWorkCorrectly() {
        // Arrange
        TabSetStage tabSetStage = new TabSetStage();
        tabSetStage.setStageNo(5);
        tabSetStage.setCrossingSeqNo(2);
        
        Object serviceResult = "Command executed successfully";

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(VALID_JSON_DATA, TabSetStage.class))
                    .thenReturn(tabSetStage);
            
            when(tabCmdService.setStage(TEST_CROSSING_ID, tabSetStage))
                    .thenReturn(Optional.of(serviceResult));

            // Act
            ResultVo<?> result = cmdController.setStage(TEST_CROSSING_ID, VALID_JSON_DATA);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getCode()).isEqualTo(ResultCode.SUCCESS.getCode());
            assertThat(result.getData()).isEqualTo(serviceResult);
            verify(tabCmdService).setStage(TEST_CROSSING_ID, tabSetStage);
        }
    }
}
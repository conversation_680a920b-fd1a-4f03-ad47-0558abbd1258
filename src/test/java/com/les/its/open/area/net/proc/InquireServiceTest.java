package com.les.its.open.area.net.proc;

import com.les.its.open.area.juncer.bean.ControllerAgent;
import com.les.its.open.area.juncer.msg.TabInBases;
import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.event.MessagePublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class InquireServiceTest {

    @Mock
    private ControllerAgentService controllerAgentService;

    @Mock
    private MessagePublisher messagePublisher;

    @InjectMocks
    private InquireService inquireService;

    @Captor
    private ArgumentCaptor<TabInBases> tabInBasesCaptor;

    private ControllerAgent mockControllerAgent;

    @BeforeEach
    void setUp() {
        mockControllerAgent = new ControllerAgent("controller1");
        mockControllerAgent.setHeartBeatCountSnd(new AtomicInteger(0));
        mockControllerAgent.setHeartBeatCountRev(new AtomicInteger(0));
    }

    @Test
    void inquire_WhenControllerAgentNotFound_ShouldSkipProcessing() {
        // Arrange
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.empty());

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    void inquire_WhenLinkIsDown_ShouldSkipProcessing() {
        // Arrange
        mockControllerAgent.setLink(false);
        mockControllerAgent.setLogicLink(true);
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    void inquire_WhenLogicLinkIsDown_ShouldSkipProcessing() {
        // Arrange
        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(false);
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    void inquire_WhenHeartbeatCount14_ShouldSendInquiryMessage() {
        // Arrange
        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(true);
        mockControllerAgent.getHeartBeatCountSnd().set(13); // Will become 14 after increment
        mockControllerAgent.getHeartBeatCountRev().set(1);
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher).publishMessage(tabInBasesCaptor.capture());
        TabInBases capturedMessage = tabInBasesCaptor.getValue();
        assertThat(capturedMessage).isNotNull();
        assertThat(capturedMessage.getTabInBases()).hasSize(1);
    }

    @Test
    void inquire_WhenHeartbeatRevExceeds3_ShouldSetLogicLinkToFalse() {
        // Arrange
        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(true);
        mockControllerAgent.getHeartBeatCountSnd().set(13); // Will become 14 after increment
        mockControllerAgent.getHeartBeatCountRev().set(2); // Will become 3 after increment
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        assertThat(mockControllerAgent.isLogicLink()).isFalse();
        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    void inquire_WhenHeartbeatCountReachesMax_ShouldReset() {
        // Arrange
        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(true);
        mockControllerAgent.getHeartBeatCountSnd().set(14 * 1000 - 1);
        mockControllerAgent.getHeartBeatCountRev().set(1);
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        assertThat(mockControllerAgent.getHeartBeatCountSnd().get()).isEqualTo(0);
    }

    @Test
    void inquire_WithMultipleControllers_ShouldProcessAllControllers() {
        // Arrange
        ControllerAgent mockControllerAgent2 = new ControllerAgent("controller2");
        mockControllerAgent2.setHeartBeatCountSnd(new AtomicInteger(0));
        mockControllerAgent2.setHeartBeatCountRev(new AtomicInteger(0));
        mockControllerAgent2.setLink(true);
        mockControllerAgent2.setLogicLink(true);
        mockControllerAgent2.setHeartBeatCountSnd(new AtomicInteger(13));
        mockControllerAgent2.setHeartBeatCountRev(new AtomicInteger(1));

        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(true);
        mockControllerAgent.getHeartBeatCountSnd().set(13);
        mockControllerAgent.getHeartBeatCountRev().set(1);
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1", "controller2"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));
        when(controllerAgentService.getControllerAgent("controller2")).thenReturn(Optional.of(mockControllerAgent2));

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher, times(2)).publishMessage(any());
    }

    @Test
    void inquire_WhenHeartbeatCountNotDivisibleBy14_ShouldNotSendMessage() {
        // Arrange
        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(true);
        mockControllerAgent.getHeartBeatCountSnd().set(10); // Not divisible by 14
        mockControllerAgent.getHeartBeatCountRev().set(1);
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    void inquire_WhenHeartbeatRevCounterResets_ShouldContinueNormally() {
        // Arrange
        mockControllerAgent.setLink(true);
        mockControllerAgent.setLogicLink(true);
        mockControllerAgent.getHeartBeatCountSnd().set(13);
        mockControllerAgent.getHeartBeatCountRev().set(Integer.MAX_VALUE); // Simulate counter about to overflow
        
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of("controller1"));
        when(controllerAgentService.getControllerAgent("controller1")).thenReturn(Optional.of(mockControllerAgent));

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher).publishMessage(any());
        assertThat(mockControllerAgent.getHeartBeatCountRev().get()).isEqualTo(Integer.MIN_VALUE);
    }

    @Test
    void inquire_WhenNoControllerAgents_ShouldHandleEmptyList() {
        // Arrange
        when(controllerAgentService.getControllerAgentIds()).thenReturn(Set.of());

        // Act
        inquireService.inquire();

        // Assert
        verify(messagePublisher, never()).publishMessage(any());
        verify(controllerAgentService, never()).getControllerAgent(any());
    }

} 
package com.les.its.open.area.juncer.api;

import com.les.its.open.area.juncer.msg.param.TabLookParamErr;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabLookService;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AllLookSignalController Tests")
class AllLookSignalControllerTest {

    @Mock
    private TabLookService tabLookService;

    @Mock
    private ControllerService controllerService;

    @InjectMocks
    private AllLookSignalController allLookSignalController;

    private static final String TEST_CONTROLLER_ID = "TEST_CONTROLLER_001";
    private ControllerBaseInfo mockControllerInfo;
    private List<String> exportTypes;

    @BeforeEach
    void setUp() {
        mockControllerInfo = new ControllerBaseInfo();
        exportTypes = Arrays.asList("0x0200", "0x0201", "0x0300");
    }

    @Test
    @DisplayName("lookSignalController should return success when controller exists and parameters found")
    void lookSignalController_WhenControllerExistsAndParametersFound_ShouldReturnSuccess() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        List<Object> mockData = Arrays.asList("param1", "param2", "param3");
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(mockData));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, exportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getErrorCode()).isEqualTo("20000");
        assertThat(result.getMessage()).isEqualTo("获取信号参数正常");
        assertThat(result.getData()).isInstanceOf(List.class);
        
        verify(controllerService).getSignalInfo(TEST_CONTROLLER_ID);
    }

    @Test
    @DisplayName("lookSignalController should return error when controller does not exist")
    void lookSignalController_WhenControllerDoesNotExist_ShouldReturnError() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.empty());

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, exportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getErrorCode()).isEqualTo(String.valueOf(ResultCode.FAILED_NO_CONTROLLER.code()));
        assertThat(result.getMessage()).isEqualTo(ResultCode.FAILED_NO_CONTROLLER.message());
        assertThat(result.getData()).isNull();

        verify(controllerService).getSignalInfo(TEST_CONTROLLER_ID);
        verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle empty export types list")
    void lookSignalController_WhenExportTypesEmpty_ShouldReturnEmptyResult() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        List<String> emptyExportTypes = Collections.emptyList();

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, emptyExportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getErrorCode()).isEqualTo("20000");
        assertThat(result.getData()).isInstanceOf(List.class);
        
        @SuppressWarnings("unchecked")
        List<Map<String, List<Object>>> allParams = (List<Map<String, List<Object>>>) result.getData();
        assertThat(allParams).isEmpty();

        verify(controllerService).getSignalInfo(TEST_CONTROLLER_ID);
        verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle null export types")
    void lookSignalController_WhenExportTypesNull_ShouldThrowException() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        // Act & Assert
        assertThatThrownBy(() -> 
            allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, null)
        ).isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("lookSignalController should filter out PARAM_UNKNOWN type")
    void lookSignalController_ShouldFilterOutParamUnknownType() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        // Include PARAM_UNKNOWN code in export types
        List<String> exportTypesWithUnknown = Arrays.asList("0x0000", "0x0200");

        List<Object> mockData = Arrays.asList("param1", "param2");
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(mockData));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, exportTypesWithUnknown);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        // Should not call look service for PARAM_UNKNOWN
        verify(tabLookService, never()).look(eq(TEST_CONTROLLER_ID), eq(ParamMsgType.PARAM_UNKNOWN), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle TabLookParamErr response")
    void lookSignalController_WhenServiceReturnsTabLookParamErr_ShouldIncludeInResult() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        TabLookParamErr paramErr = new TabLookParamErr();
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(paramErr));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, exportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        List<Map<String, List<Object>>> allParams = (List<Map<String, List<Object>>>) result.getData();
        assertThat(allParams).isNotEmpty();
        
        // Verify that TabLookParamErr is included in the data
        boolean foundParamErr = allParams.stream()
                .flatMap(map -> map.values().stream())
                .flatMap(List::stream)
                .anyMatch(obj -> obj instanceof TabLookParamErr);
        assertThat(foundParamErr).isTrue();
    }

    @Test
    @DisplayName("lookSignalController should handle service returning empty optional")
    void lookSignalController_WhenServiceReturnsEmpty_ShouldLogErrorAndContinue() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.empty());

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, exportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        verify(controllerService).getSignalInfo(TEST_CONTROLLER_ID);
        verify(tabLookService, atLeastOnce()).look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle multiple parameter types")
    void lookSignalController_WhenMultipleParameterTypes_ShouldProcessAll() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        List<String> multipleExportTypes = Arrays.asList("0x0200", "0x0201", "0x0300", "0x0400");
        
        List<Object> mockData = Arrays.asList("param1", "param2");
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(mockData));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, multipleExportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        List<Map<String, List<Object>>> allParams = (List<Map<String, List<Object>>>) result.getData();
        assertThat(allParams).isNotEmpty();
        
        verify(tabLookService, atLeastOnce()).look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should format parameter names correctly")
    void lookSignalController_ShouldFormatParameterNamesCorrectly() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        List<String> singleExportType = Arrays.asList("0x0200");
        
        List<Object> mockData = Arrays.asList("param1");
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(mockData));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, singleExportType);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        List<Map<String, List<Object>>> allParams = (List<Map<String, List<Object>>>) result.getData();
        assertThat(allParams).isNotEmpty();
        
        // Verify parameter name format: "01-0x0200-Description"
        Map<String, List<Object>> firstParam = allParams.get(0);
        String paramName = firstParam.keySet().iterator().next();
        assertThat(paramName).matches("\\d{2}-0x\\d{4}-.*");
    }

    @Test
    @DisplayName("lookSignalController should handle invalid export type format")
    void lookSignalController_WhenInvalidExportTypeFormat_ShouldFilterOut() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        List<String> invalidExportTypes = Arrays.asList("invalid", "0x200", "200");

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, invalidExportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        List<Map<String, List<Object>>> allParams = (List<Map<String, List<Object>>>) result.getData();
        assertThat(allParams).isEmpty();

        verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle service throwing exception")
    void lookSignalController_WhenServiceThrowsException_ShouldPropagateException() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        assertThatThrownBy(() -> 
            allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, exportTypes)
        ).isInstanceOf(RuntimeException.class)
         .hasMessage("Service error");
    }

    @Test
    @DisplayName("Controller should have correct REST mapping annotations")
    void controller_ShouldHaveCorrectRestMappingAnnotations() {
        // Assert
        assertThat(AllLookSignalController.class.isAnnotationPresent(RestController.class)).isTrue();
        assertThat(AllLookSignalController.class.isAnnotationPresent(RequestMapping.class)).isTrue();
        
        RequestMapping requestMapping = AllLookSignalController.class.getAnnotation(RequestMapping.class);
        assertThat(requestMapping.value()).containsExactly("/openles/lookAll");
    }

    @Test
    @DisplayName("lookSignalController method should have correct POST mapping")
    void lookSignalControllerMethod_ShouldHaveCorrectPostMapping() throws NoSuchMethodException {
        // Arrange
        var method = AllLookSignalController.class.getMethod("lookSignalController", String.class, List.class);

        // Assert
        assertThat(method.isAnnotationPresent(PostMapping.class)).isTrue();
        
        PostMapping postMapping = method.getAnnotation(PostMapping.class);
        assertThat(postMapping.value()).containsExactly("/{controllerId}");
    }

    @Test
    @DisplayName("lookSignalController should handle concurrent parameter lookups")
    void lookSignalController_WhenConcurrentParameterLookups_ShouldHandleCorrectly() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        List<String> concurrentExportTypes = Arrays.asList("0x0200", "0x0201", "0x0202", "0x0203");
        
        List<Object> mockData = Arrays.asList("param1", "param2");
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(mockData));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, concurrentExportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        verify(controllerService).getSignalInfo(TEST_CONTROLLER_ID);
        verify(tabLookService, atLeast(concurrentExportTypes.size())).look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should sort parameters by code")
    void lookSignalController_ShouldSortParametersByCode() {
        // Arrange
        when(controllerService.getSignalInfo(TEST_CONTROLLER_ID))
                .thenReturn(Optional.of(mockControllerInfo));

        // Provide export types in random order
        List<String> unsortedExportTypes = Arrays.asList("0x0300", "0x0200", "0x0400", "0x0201");
        
        List<Object> mockData = Arrays.asList("param1");
        when(tabLookService.look(eq(TEST_CONTROLLER_ID), any(ParamMsgType.class), anyInt(), anyInt()))
                .thenReturn(Optional.of(mockData));

        // Act
        JsonResult<?> result = allLookSignalController.lookSignalController(TEST_CONTROLLER_ID, unsortedExportTypes);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        
        @SuppressWarnings("unchecked")
        List<Map<String, List<Object>>> allParams = (List<Map<String, List<Object>>>) result.getData();
        assertThat(allParams).isNotEmpty();
        
        // Verify that parameters are sorted by their index (which corresponds to code order)
        for (int i = 0; i < allParams.size(); i++) {
            Map<String, List<Object>> param = allParams.get(i);
            String paramName = param.keySet().iterator().next();
            assertThat(paramName).startsWith(String.format("%02d-", i + 1));
        }
    }
}
package com.les.its.open.area.juncer.api;

import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.area.net.proc.TabLookService;
import com.les.its.open.utils.ResultCode;
import com.myweb.commons.persistence.JsonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("LookSignalController Tests")
class LookSignalControllerTest {

    @Mock
    private TabLookService tabLookService;

    @InjectMocks
    private LookSignalController lookSignalController;

    private static final String TEST_CONTROLLER_ID = "TEST_CONTROLLER_001";
    private static final String VALID_PARAM_TYPE_HEX = "0200";
    private static final String INVALID_PARAM_TYPE_HEX = "0000";
    private static final int TEST_OFFSET = 1;
    private static final int TEST_COUNT = 5;

    @BeforeEach
    void setUp() {
        // Setup can be extended as needed
    }

    @Test
    @DisplayName("lookSignalController should return success when service returns data")
    void lookSignalController_WhenServiceReturnsData_ShouldReturnSuccess() {
        // Arrange
        Object expectedResult = "Look operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getErrorCode()).isEqualTo("20000");
            assertThat(result.getMessage()).isEqualTo("加载信号参数返回");
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should return error when service returns empty")
    void lookSignalController_WhenServiceReturnsEmpty_ShouldReturnError() {
        // Arrange
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.empty());

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getMessage()).isEqualTo(ResultCode.FAILED_LOOK.message());
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should return error when param type is PARAM_UNKNOWN")
    void lookSignalController_WhenParamTypeIsUnknown_ShouldReturnError() {
        // Arrange
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0000))
                    .thenReturn(ParamMsgType.PARAM_UNKNOWN);

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, INVALID_PARAM_TYPE_HEX, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getMessage()).isEqualTo(ResultCode.NOT_SUPPORT_LOOK_TYPE.message());
            
            verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
        }
    }

    @Test
    @DisplayName("lookSignalController should handle invalid hex format in parameter type")
    void lookSignalController_WhenInvalidHexFormat_ShouldThrowNumberFormatException() {
        // Arrange
        String invalidHexFormat = "INVALID_HEX";

        // Act & Assert
        assertThatThrownBy(() -> 
            lookSignalController.lookSignalController(TEST_CONTROLLER_ID, invalidHexFormat, TEST_OFFSET, TEST_COUNT)
        ).isInstanceOf(NumberFormatException.class);
        
        verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle null parameter type")
    void lookSignalController_WhenParameterTypeIsNull_ShouldThrowNumberFormatException() {
        // Act & Assert
        assertThatThrownBy(() -> 
            lookSignalController.lookSignalController(TEST_CONTROLLER_ID, null, TEST_OFFSET, TEST_COUNT)
        ).isInstanceOf(NumberFormatException.class);
        
        verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle empty parameter type")
    void lookSignalController_WhenParameterTypeIsEmpty_ShouldThrowNumberFormatException() {
        // Act & Assert
        assertThatThrownBy(() -> 
            lookSignalController.lookSignalController(TEST_CONTROLLER_ID, "", TEST_OFFSET, TEST_COUNT)
        ).isInstanceOf(NumberFormatException.class);
        
        verify(tabLookService, never()).look(anyString(), any(ParamMsgType.class), anyInt(), anyInt());
    }

    @Test
    @DisplayName("lookSignalController should handle null controller ID")
    void lookSignalController_WhenControllerIdIsNull_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Look operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(null, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    null, VALID_PARAM_TYPE_HEX, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(null, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle empty controller ID")
    void lookSignalController_WhenControllerIdIsEmpty_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Look operation successful";
        String emptyControllerId = "";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(emptyControllerId, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    emptyControllerId, VALID_PARAM_TYPE_HEX, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(emptyControllerId, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle zero offset")
    void lookSignalController_WhenOffsetIsZero_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Look operation successful";
        int zeroOffset = 0;
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, zeroOffset, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, zeroOffset, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, zeroOffset, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle negative offset")
    void lookSignalController_WhenOffsetIsNegative_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Look operation successful";
        int negativeOffset = -1;
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, negativeOffset, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, negativeOffset, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, negativeOffset, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle zero count")
    void lookSignalController_WhenCountIsZero_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Look operation successful";
        int zeroCount = 0;
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, zeroCount))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_OFFSET, zeroCount);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, zeroCount);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle negative count")
    void lookSignalController_WhenCountIsNegative_ShouldStillCallService() {
        // Arrange
        Object expectedResult = "Look operation successful";
        int negativeCount = -1;
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, negativeCount))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_OFFSET, negativeCount);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, negativeCount);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle service throwing exception")
    void lookSignalController_WhenServiceThrowsException_ShouldPropagateException() {
        // Arrange
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenThrow(new RuntimeException("Service error"));

            // Act & Assert
            assertThatThrownBy(() -> 
                lookSignalController.lookSignalController(TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, TEST_OFFSET, TEST_COUNT)
            ).isInstanceOf(RuntimeException.class)
             .hasMessage("Service error");
        }
    }

    @Test
    @DisplayName("lookSignalController should parse hex parameter type correctly")
    void lookSignalController_ShouldParseHexParameterTypeCorrectly() {
        // Arrange
        String hexParamType = "ABCD";
        Object expectedResult = "Look operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0xABCD))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, hexParamType, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            
            mockedParamMsgType.verify(() -> ParamMsgType.fromCode(0xABCD));
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle lowercase hex parameter type")
    void lookSignalController_ShouldHandleLowercaseHexParameterType() {
        // Arrange
        String lowercaseHexParamType = "abc";
        Object expectedResult = "Look operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0xABC))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, lowercaseHexParamType, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            
            mockedParamMsgType.verify(() -> ParamMsgType.fromCode(0xABC));
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle very large hex values")
    void lookSignalController_WhenVeryLargeHexValues_ShouldHandleCorrectly() {
        // Arrange
        String largeHexParamType = "FFFF";
        Object expectedResult = "Look operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0xFFFF))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, largeHexParamType, TEST_OFFSET, TEST_COUNT);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            
            mockedParamMsgType.verify(() -> ParamMsgType.fromCode(0xFFFF));
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, TEST_OFFSET, TEST_COUNT);
        }
    }

    @Test
    @DisplayName("lookSignalController should handle large offset and count values")
    void lookSignalController_WhenLargeOffsetAndCount_ShouldHandleCorrectly() {
        // Arrange
        int largeOffset = Integer.MAX_VALUE;
        int largeCount = Integer.MAX_VALUE;
        Object expectedResult = "Look operation successful";
        
        try (MockedStatic<ParamMsgType> mockedParamMsgType = mockStatic(ParamMsgType.class)) {
            ParamMsgType mockParamType = ParamMsgType.PARAM_BASE_INFO;
            mockedParamMsgType.when(() -> ParamMsgType.fromCode(0x0200))
                    .thenReturn(mockParamType);
            
            when(tabLookService.look(TEST_CONTROLLER_ID, mockParamType, largeOffset, largeCount))
                    .thenReturn(Optional.of(expectedResult));

            // Act
            JsonResult<?> result = lookSignalController.lookSignalController(
                    TEST_CONTROLLER_ID, VALID_PARAM_TYPE_HEX, largeOffset, largeCount);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isEqualTo(expectedResult);
            
            verify(tabLookService).look(TEST_CONTROLLER_ID, mockParamType, largeOffset, largeCount);
        }
    }

    @Test
    @DisplayName("Controller should have correct REST mapping annotations")
    void controller_ShouldHaveCorrectRestMappingAnnotations() {
        // Assert
        assertThat(LookSignalController.class.isAnnotationPresent(RestController.class)).isTrue();
        assertThat(LookSignalController.class.isAnnotationPresent(RequestMapping.class)).isTrue();
        
        RequestMapping requestMapping = LookSignalController.class.getAnnotation(RequestMapping.class);
        assertThat(requestMapping.value()).containsExactly("/openles/look");
    }

    @Test
    @DisplayName("lookSignalController method should have correct GET mapping")
    void lookSignalControllerMethod_ShouldHaveCorrectGetMapping() throws NoSuchMethodException {
        // Arrange
        var method = LookSignalController.class.getMethod("lookSignalController", String.class, String.class, int.class, int.class);

        // Assert
        assertThat(method.isAnnotationPresent(GetMapping.class)).isTrue();
        
        GetMapping getMapping = method.getAnnotation(GetMapping.class);
        assertThat(getMapping.value()).containsExactly("/{controllerId}/{paramMsgType}/{offset}/{count}");
    }
}
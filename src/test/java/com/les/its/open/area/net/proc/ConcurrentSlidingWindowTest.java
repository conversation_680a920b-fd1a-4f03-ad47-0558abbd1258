package com.les.its.open.area.net.proc;

import com.les.its.open.area.message.param.status.TabRTT;
import com.les.its.open.bussiness.bean.rtt.ConcurrentSlidingWindow;
import org.junit.jupiter.api.Test;

public class ConcurrentSlidingWindowTest {

    @Test
    public void test() throws InterruptedException {
        System.out.println("=== 时间滑动窗口使用示例 ===");

        // 示例4: 并发窗口
        System.out.println("\n4. 并发滑动窗口示例:");
        ConcurrentSlidingWindow<TabRTT> concurrentWindow = new ConcurrentSlidingWindow<>(10000);

        // 模拟多线程并发添加
        for (int i = 0; i < 3; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < 5; j++) {
                    long data = threadId * 100 + j;
                    concurrentWindow.add( TabRTT.builder().rtt(data).diff(data).timeStamp(System.currentTimeMillis()).build());
                    System.out.println("线程" + threadId + " 添加: " + data +
                            ", 窗口大小: " + concurrentWindow.size());
                    try {
                        Thread.sleep(300);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }).start();
        }

        Thread.sleep(2000); // 等待所有线程完成
        System.out.println("最终窗口快照: " + concurrentWindow.getSnapshot());
    }

}

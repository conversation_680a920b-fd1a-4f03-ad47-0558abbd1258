package com.les.its.open.area.juncer.bean;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.*;

@DisplayName("ControllerAgent Tests")
class ControllerAgentTest {

    private ControllerAgent controllerAgent;
    private static final String TEST_CONTROLLER_ID = "TEST_CONTROLLER_001";

    @BeforeEach
    void setUp() {
        controllerAgent = new ControllerAgent(TEST_CONTROLLER_ID);
    }

    @Test
    @DisplayName("Constructor should initialize all required fields")
    void constructor_ShouldInitializeAllRequiredFields() {
        // Assert
        assertThat(controllerAgent.getControllerId()).isEqualTo(TEST_CONTROLLER_ID);
        assertThat(controllerAgent.isLink()).isFalse();
        assertThat(controllerAgent.isLogicLink()).isFalse();
        assertThat(controllerAgent.getLinkChgTime()).isNotNull();
        assertThat(controllerAgent.getLogicLinkChgTime()).isNotNull();
        assertThat(controllerAgent.getHeartBeatCountSnd()).isNotNull();
        assertThat(controllerAgent.getHeartBeatCountRev()).isNotNull();
        assertThat(controllerAgent.getHeartBeatCountSnd().get()).isEqualTo(0);
        assertThat(controllerAgent.getHeartBeatCountRev().get()).isEqualTo(0);
    }

    @Test
    @DisplayName("initStatus should initialize all status lists with correct sizes")
    void initStatus_ShouldInitializeAllStatusListsWithCorrectSizes() {
        // Act
        controllerAgent.initStatus();

        // Assert
        assertThat(controllerAgent.getControllerStatusList()).hasSize(OpenLesConst.MAX_SUB_CROSS_NUM);
        assertThat(controllerAgent.getTabEmergencyList()).hasSize(16); // PARAM_EMERGENCY.getMax()
        assertThat(controllerAgent.getTabPriorityList()).hasSize(16); // PARAM_PRIORITY.getMax()
        assertThat(controllerAgent.getTabShieldList()).hasSize(3);
        assertThat(controllerAgent.getTabProhibit0List()).hasSize(3);
        assertThat(controllerAgent.getTabProhibit1List()).hasSize(3);
        assertThat(controllerAgent.getTabEmergencyPriorityShieldList()).hasSize(2);
    }

    @Test
    @DisplayName("initStatus should set correct emergency numbers")
    void initStatus_ShouldSetCorrectEmergencyNumbers() {
        // Act
        controllerAgent.initStatus();

        // Assert
        for (int i = 0; i < controllerAgent.getTabEmergencyList().size(); i++) {
            assertThat(controllerAgent.getTabEmergencyList().get(i).getEmergencyNo()).isEqualTo(i + 1);
        }
    }

    @Test
    @DisplayName("initStatus should set correct priority numbers")
    void initStatus_ShouldSetCorrectPriorityNumbers() {
        // Act
        controllerAgent.initStatus();

        // Assert
        for (int i = 0; i < controllerAgent.getTabPriorityList().size(); i++) {
            assertThat(controllerAgent.getTabPriorityList().get(i).getPriorityNo()).isEqualTo(i + 1);
        }
    }

    @Test
    @DisplayName("initStatus should set correct shield types")
    void initStatus_ShouldSetCorrectShieldTypes() {
        // Act
        controllerAgent.initStatus();

        // Assert
        for (int i = 0; i < controllerAgent.getTabShieldList().size(); i++) {
            assertThat(controllerAgent.getTabShieldList().get(i).getType()).isEqualTo(i + 1);
        }
    }

    @Test
    @DisplayName("initStatus should set correct prohibit types for both lists")
    void initStatus_ShouldSetCorrectProhibitTypesForBothLists() {
        // Act
        controllerAgent.initStatus();

        // Assert
        for (int i = 0; i < controllerAgent.getTabProhibit0List().size(); i++) {
            assertThat(controllerAgent.getTabProhibit0List().get(i).getType()).isEqualTo(i + 1);
        }
        for (int i = 0; i < controllerAgent.getTabProhibit1List().size(); i++) {
            assertThat(controllerAgent.getTabProhibit1List().get(i).getType()).isEqualTo(i + 1);
        }
    }

    @Test
    @DisplayName("initStatus should set correct emergency priority shield types")
    void initStatus_ShouldSetCorrectEmergencyPriorityShieldTypes() {
        // Act
        controllerAgent.initStatus();

        // Assert
        for (int i = 0; i < controllerAgent.getTabEmergencyPriorityShieldList().size(); i++) {
            assertThat(controllerAgent.getTabEmergencyPriorityShieldList().get(i).getType()).isEqualTo(i + 1);
        }
    }

    @Test
    @DisplayName("setLink to true should update link status and change time")
    void setLink_ToTrue_ShouldUpdateLinkStatusAndChangeTime() {
        // Arrange
        LocalDateTime beforeTime = LocalDateTime.now();

        // Act
        controllerAgent.setLink(true);

        // Assert
        assertThat(controllerAgent.isLink()).isTrue();
        assertThat(controllerAgent.getLinkChgTime()).isAfter(beforeTime);
    }

    @Test
    @DisplayName("setLink to false should update link status and set logic link to false")
    void setLink_ToFalse_ShouldUpdateLinkStatusAndSetLogicLinkToFalse() {
        // Arrange
        controllerAgent.setLogicLink(true);
        LocalDateTime beforeTime = LocalDateTime.now();

        // Act
        controllerAgent.setLink(false);

        // Assert
        assertThat(controllerAgent.isLink()).isFalse();
        assertThat(controllerAgent.isLogicLink()).isFalse();
        assertThat(controllerAgent.getLinkChgTime()).isAfter(beforeTime);
        assertThat(controllerAgent.getLogicLinkChgTime()).isAfter(beforeTime);
    }

    @Test
    @DisplayName("setLogicLink should update logic link status and reset heartbeat counters")
    void setLogicLink_ShouldUpdateLogicLinkStatusAndResetHeartbeatCounters() {
        // Arrange
        controllerAgent.getHeartBeatCountSnd().set(10);
        controllerAgent.getHeartBeatCountRev().set(5);
        LocalDateTime beforeTime = LocalDateTime.now();

        // Act
        controllerAgent.setLogicLink(true);

        // Assert
        assertThat(controllerAgent.isLogicLink()).isTrue();
        assertThat(controllerAgent.getLogicLinkChgTime()).isAfter(beforeTime);
        assertThat(controllerAgent.getHeartBeatCountSnd().get()).isEqualTo(0);
        assertThat(controllerAgent.getHeartBeatCountRev().get()).isEqualTo(0);
    }

    @Test
    @DisplayName("updateHbSndTime should return true and update time when heartBeatCountRev is 1")
    void updateHbSndTime_WhenHeartBeatCountRevIs1_ShouldReturnTrueAndUpdateTime() {
        // Arrange
        controllerAgent.getHeartBeatCountRev().set(1);
        LocalDateTime beforeTime = LocalDateTime.now();

        // Act
        boolean result = controllerAgent.updateHbSndTime();

        // Assert
        assertThat(result).isTrue();
        assertThat(controllerAgent.getLstHbSndTime()).isAfter(beforeTime);
    }

    @Test
    @DisplayName("updateHbSndTime should return false when heartBeatCountRev is not 1")
    void updateHbSndTime_WhenHeartBeatCountRevIsNot1_ShouldReturnFalse() {
        // Arrange
        controllerAgent.getHeartBeatCountRev().set(0);
        LocalDateTime originalTime = controllerAgent.getLstHbSndTime();

        // Act
        boolean result = controllerAgent.updateHbSndTime();

        // Assert
        assertThat(result).isFalse();
        assertThat(controllerAgent.getLstHbSndTime()).isEqualTo(originalTime);
    }

    @Test
    @DisplayName("updateHbSndTime should return false when heartBeatCountRev is greater than 1")
    void updateHbSndTime_WhenHeartBeatCountRevIsGreaterThan1_ShouldReturnFalse() {
        // Arrange
        controllerAgent.getHeartBeatCountRev().set(2);
        LocalDateTime originalTime = controllerAgent.getLstHbSndTime();

        // Act
        boolean result = controllerAgent.updateHbSndTime();

        // Assert
        assertThat(result).isFalse();
        assertThat(controllerAgent.getLstHbSndTime()).isEqualTo(originalTime);
    }

    @Test
    @DisplayName("heartbeat counters should be thread-safe AtomicInteger instances")
    void heartbeatCounters_ShouldBeThreadSafeAtomicIntegerInstances() {
        // Assert
        assertThat(controllerAgent.getHeartBeatCountSnd()).isInstanceOf(AtomicInteger.class);
        assertThat(controllerAgent.getHeartBeatCountRev()).isInstanceOf(AtomicInteger.class);
    }

    @Test
    @DisplayName("multiple setLink operations should update change time each time")
    void multipleSetLinkOperations_ShouldUpdateChangeTimeEachTime() throws InterruptedException {
        // Arrange
        controllerAgent.setLink(true);
        LocalDateTime firstChangeTime = controllerAgent.getLinkChgTime();
        
        Thread.sleep(1); // Ensure time difference
        
        // Act
        controllerAgent.setLink(false);
        LocalDateTime secondChangeTime = controllerAgent.getLinkChgTime();

        // Assert
        assertThat(secondChangeTime).isAfter(firstChangeTime);
    }

    @Test
    @DisplayName("multiple setLogicLink operations should update change time each time")
    void multipleSetLogicLinkOperations_ShouldUpdateChangeTimeEachTime() throws InterruptedException {
        // Arrange
        controllerAgent.setLogicLink(true);
        LocalDateTime firstChangeTime = controllerAgent.getLogicLinkChgTime();
        
        Thread.sleep(1); // Ensure time difference
        
        // Act
        controllerAgent.setLogicLink(false);
        LocalDateTime secondChangeTime = controllerAgent.getLogicLinkChgTime();

        // Assert
        assertThat(secondChangeTime).isAfter(firstChangeTime);
    }

    @Test
    @DisplayName("constructor should call initStatus automatically")
    void constructor_ShouldCallInitStatusAutomatically() {
        // Arrange & Act
        ControllerAgent newAgent = new ControllerAgent("NEW_CONTROLLER");

        // Assert
        assertThat(newAgent.getControllerStatusList()).isNotNull().isNotEmpty();
        assertThat(newAgent.getTabEmergencyList()).isNotNull().isNotEmpty();
        assertThat(newAgent.getTabPriorityList()).isNotNull().isNotEmpty();
    }

    @Test
    @DisplayName("link changes should be independent of logic link when setting to true")
    void linkChanges_ShouldBeIndependentOfLogicLinkWhenSettingToTrue() {
        // Arrange
        controllerAgent.setLogicLink(false);

        // Act
        controllerAgent.setLink(true);

        // Assert
        assertThat(controllerAgent.isLink()).isTrue();
        assertThat(controllerAgent.isLogicLink()).isFalse(); // Should remain false
    }

    @Test
    @DisplayName("logic link should reset heartbeat counters regardless of their previous values")
    void logicLink_ShouldResetHeartbeatCountersRegardlessOfPreviousValues() {
        // Arrange
        controllerAgent.getHeartBeatCountSnd().set(Integer.MAX_VALUE);
        controllerAgent.getHeartBeatCountRev().set(Integer.MAX_VALUE);

        // Act
        controllerAgent.setLogicLink(true);

        // Assert
        assertThat(controllerAgent.getHeartBeatCountSnd().get()).isEqualTo(0);
        assertThat(controllerAgent.getHeartBeatCountRev().get()).isEqualTo(0);
    }
}
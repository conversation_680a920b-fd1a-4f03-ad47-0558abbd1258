package com.les.its.open.area.net.utils;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.TabInBase;
import com.les.its.open.area.net.log.IPBasedLogger;
import com.les.its.open.bussiness.utils.LesUtils;
import com.les.its.open.config.JuncerConfigure;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.les.its.open.event.AckManager.NeedAck;
import com.les.its.open.event.AckManager.response.ResponseMessage;
import com.les.its.open.event.AckManager.response.ResponseStatus;
import com.les.its.open.event.MessageOuterPublisher;
import com.les.its.open.protocol.common.InterProtocol;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("JuncerUtils Tests")
class JuncerUtilsTest {

    @Mock
    private MessageOuterPublisher messageOuterPublisher;

    @Mock
    private JuncerConfigure juncerConfigure;

    @Mock
    private TabInBase mockTabInBase;

    @Mock
    private InvokeFuture mockInvokeFuture;

    @Mock
    private InterProtocol mockInterProtocol;

    @Mock
    private ResponseMessage mockResponseMessage;

    @InjectMocks
    private JuncerUtils juncerUtils;

    private static final String TEST_IP = "*************";
    private static final int TEST_NO_AREA = 1;
    private static final int TEST_NO_JUNC = 2;

    // Test class that implements both TabInBase and NeedAck
    private static class TestTabInBaseWithAck extends TabInBase implements NeedAck {

        public Object fromInner(Object o) {
            return o;
        }


        public Object toInner() {
            return this;
        }

        @Override
        public String getAckKey() {
            return "";
        }
    }

    // Test class that only extends TabInBase (no ack needed)
    private static class TestTabInBaseNoAck extends TabInBase {

        public Object fromInner(Object o) {
            return o;
        }


        public Object toInner() {
            return this;
        }
    }

    @BeforeEach
    void setUp() {
        // Setup mock behavior for basic TabInBase properties
        when(mockTabInBase.getNoArea()).thenReturn(TEST_NO_AREA);
        when(mockTabInBase.getNoJunc()).thenReturn(TEST_NO_JUNC);
    }

    @Test
    @DisplayName("SIZE_CHANGE_ABLE constant should have correct value")
    void sizeChangeAbleConstant_ShouldHaveCorrectValue() {
        // Assert
        assertThat(JuncerUtils.SIZE_CHANGE_ABLE).isEqualTo(-1);
    }

    @Test
    @DisplayName("send2Controller should send message and return InvokeFuture when logging enabled")
    void send2Controller_WhenLoggingEnabled_ShouldSendMessageAndReturnInvokeFuture() {
        // Arrange
        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, mockTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(mockTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            Optional<InvokeFuture> result = juncerUtils.send2Controller(TEST_IP, mockTabInBase, false, true);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get()).isEqualTo(mockInvokeFuture);
            
            mockedLogger.verify(() -> IPBasedLogger.logMessage(eq(TEST_IP), eq(TEST_NO_AREA), eq(TEST_NO_JUNC), anyString(), anyString()));
            verify(messageOuterPublisher).sendMessageOuter(mockInterProtocol);
        }
    }

    @Test
    @DisplayName("send2Controller should send message without logging when message type disabled")
    void send2Controller_WhenMessageTypeDisabled_ShouldSendMessageWithoutLogging() {
        // Arrange
        List<Integer> disabledMsgTypes = Arrays.asList(100, 200);
        when(juncerConfigure.getDisableLogMsgs()).thenReturn(disabledMsgTypes);
        when(mockTabInBase.getMsgType()).thenReturn(mock(com.les.its.open.area.net.msg.MsgType.class));
        when(mockTabInBase.getMsgType().getCode()).thenReturn(100);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, mockTabInBase, true, false))
                    .thenReturn(mockInterProtocol);

            // Act
            Optional<InvokeFuture> result = juncerUtils.send2Controller(TEST_IP, mockTabInBase, true, false);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get()).isEqualTo(mockInvokeFuture);
            
            mockedLogger.verifyNoInteractions();
            verify(messageOuterPublisher).sendMessageOuter(mockInterProtocol);
        }
    }

    @Test
    @DisplayName("send2Controller should handle null IP gracefully")
    void send2Controller_WhenIpIsNull_ShouldHandleGracefully() {
        // Arrange
        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(null, mockTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(mockTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            Optional<InvokeFuture> result = juncerUtils.send2Controller(null, mockTabInBase, false, true);

            // Assert
            assertThat(result).isPresent();
            verify(messageOuterPublisher).sendMessageOuter(mockInterProtocol);
        }
    }

    @Test
    @DisplayName("send2ControllerAndWaitForAck should return empty when TabInBase is not NeedAck")
    void send2ControllerAndWaitForAck_WhenTabInBaseNotNeedAck_ShouldReturnEmpty() {
        // Arrange
        TestTabInBaseNoAck noAckTabInBase = new TestTabInBaseNoAck();

        // Act
        Optional<Object> result = juncerUtils.send2ControllerAndWaitForAck(TEST_IP, noAckTabInBase, false, true);

        // Assert
        assertThat(result).isEmpty();
        verify(messageOuterPublisher, never()).sendMessageOuter(any());
    }

    @Test
    @DisplayName("send2ControllerAndWaitForAck should wait for response when TabInBase implements NeedAck")
    void send2ControllerAndWaitForAck_WhenTabInBaseIsNeedAck_ShouldWaitForResponse() throws InterruptedException {
        // Arrange
        TestTabInBaseWithAck ackTabInBase = new TestTabInBaseWithAck();
        Object expectedResponse = "Test Response";

        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));
        when(mockInvokeFuture.waitResponse()).thenReturn(mockResponseMessage);
        when(mockResponseMessage.getResponseStatus()).thenReturn(ResponseStatus.SUCCESS);
        when(mockResponseMessage.getResponseObject()).thenReturn(expectedResponse);
        when(mockResponseMessage.getRequestInterProtocol()).thenReturn(mockInterProtocol);
        when(mockInterProtocol.getOuterTimeStamp()).thenReturn(System.currentTimeMillis());

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, ackTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(ackTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            Optional<Object> result = juncerUtils.send2ControllerAndWaitForAck(TEST_IP, ackTabInBase, false, true);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get()).isEqualTo(expectedResponse);
            verify(mockInvokeFuture).waitResponse();
        }
    }

    @Test
    @DisplayName("send2ControllerAndWaitForAck should return empty when response status is not SUCCESS")
    void send2ControllerAndWaitForAck_WhenResponseStatusNotSuccess_ShouldReturnEmpty() throws InterruptedException {
        // Arrange
        TestTabInBaseWithAck ackTabInBase = new TestTabInBaseWithAck();

        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));
        when(mockInvokeFuture.waitResponse()).thenReturn(mockResponseMessage);
        when(mockResponseMessage.getResponseStatus()).thenReturn(ResponseStatus.TIMEOUT);
        when(mockResponseMessage.getRequestInterProtocol()).thenReturn(mockInterProtocol);
        when(mockInterProtocol.getOuterTimeStamp()).thenReturn(System.currentTimeMillis());

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, ackTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(ackTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            Optional<Object> result = juncerUtils.send2ControllerAndWaitForAck(TEST_IP, ackTabInBase, false, true);

            // Assert
            assertThat(result).isEmpty();
            verify(mockInvokeFuture).waitResponse();
        }
    }

    @Test
    @DisplayName("send2ControllerAndWaitForAck should handle InterruptedException gracefully")
    void send2ControllerAndWaitForAck_WhenInterrupted_ShouldHandleGracefully() throws InterruptedException {
        // Arrange
        TestTabInBaseWithAck ackTabInBase = new TestTabInBaseWithAck();

        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));
        when(mockInvokeFuture.waitResponse()).thenThrow(new InterruptedException("Thread interrupted"));

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, ackTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(ackTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            Optional<Object> result = juncerUtils.send2ControllerAndWaitForAck(TEST_IP, ackTabInBase, false, true);

            // Assert
            assertThat(result).isEmpty();
            verify(mockInvokeFuture).waitResponse();
        }
    }

    @Test
    @DisplayName("send2ControllerAndWaitForAck should handle when send2Controller returns empty")
    void send2ControllerAndWaitForAck_WhenSend2ControllerReturnsEmpty_ShouldThrowException() {
        // Arrange
        TestTabInBaseWithAck ackTabInBase = new TestTabInBaseWithAck();

        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.empty());

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, ackTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(ackTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act & Assert
            assertThatThrownBy(() -> 
                juncerUtils.send2ControllerAndWaitForAck(TEST_IP, ackTabInBase, false, true)
            ).isInstanceOf(RuntimeException.class);
        }
    }

    @Test
    @DisplayName("Class should have correct Spring annotations")
    void class_ShouldHaveCorrectSpringAnnotations() {
        // Assert
        assertThat(JuncerUtils.class.isAnnotationPresent(Service.class)).isTrue();
    }

    @Test
    @DisplayName("send2Controller should handle empty disabled message list")
    void send2Controller_WhenDisabledMessageListEmpty_ShouldLogMessage() {
        // Arrange
        when(juncerConfigure.getDisableLogMsgs()).thenReturn(Arrays.asList());
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, mockTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(mockTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            Optional<InvokeFuture> result = juncerUtils.send2Controller(TEST_IP, mockTabInBase, false, true);

            // Assert
            assertThat(result).isPresent();
            mockedLogger.verify(() -> IPBasedLogger.logMessage(eq(TEST_IP), eq(TEST_NO_AREA), eq(TEST_NO_JUNC), anyString(), anyString()));
        }
    }

    @Test
    @DisplayName("send2Controller should handle LesUtils throwing exception")
    void send2Controller_WhenLesUtilsThrowsException_ShouldPropagateException() {
        // Arrange
        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, mockTabInBase, false, true))
                    .thenThrow(new RuntimeException("LesUtils error"));
            mockedJSONObject.when(() -> JSONObject.toJSONString(mockTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act & Assert
            assertThatThrownBy(() -> 
                juncerUtils.send2Controller(TEST_IP, mockTabInBase, false, true)
            ).isInstanceOf(RuntimeException.class)
             .hasMessage("LesUtils error");
        }
    }

    @Test
    @DisplayName("send2Controller should handle different combinations of isSimSend and ackDirect")
    void send2Controller_WithDifferentFlags_ShouldCallLesUtilsWithCorrectParameters() {
        // Arrange
        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, mockTabInBase, true, false))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(mockTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act
            juncerUtils.send2Controller(TEST_IP, mockTabInBase, true, false);

            // Assert
            mockedLesUtils.verify(() -> LesUtils.buildMessage(TEST_IP, mockTabInBase, true, false));
        }
    }

    @Test
    @DisplayName("send2ControllerAndWaitForAck should handle null response message")
    void send2ControllerAndWaitForAck_WhenResponseMessageIsNull_ShouldReturnEmpty() throws InterruptedException {
        // Arrange
        TestTabInBaseWithAck ackTabInBase = new TestTabInBaseWithAck();

        when(juncerConfigure.getDisableLogMsgs()).thenReturn(null);
        when(messageOuterPublisher.sendMessageOuter(any(InterProtocol.class))).thenReturn(Optional.of(mockInvokeFuture));
        when(mockInvokeFuture.waitResponse()).thenReturn(null);

        try (MockedStatic<LesUtils> mockedLesUtils = mockStatic(LesUtils.class);
             MockedStatic<IPBasedLogger> mockedLogger = mockStatic(IPBasedLogger.class);
             MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            
            mockedLesUtils.when(() -> LesUtils.buildMessage(TEST_IP, ackTabInBase, false, true))
                    .thenReturn(mockInterProtocol);
            mockedJSONObject.when(() -> JSONObject.toJSONString(ackTabInBase))
                    .thenReturn("{\"test\":\"data\"}");

            // Act & Assert
            assertThatThrownBy(() -> 
                juncerUtils.send2ControllerAndWaitForAck(TEST_IP, ackTabInBase, false, true)
            ).isInstanceOf(NullPointerException.class);
        }
    }
}
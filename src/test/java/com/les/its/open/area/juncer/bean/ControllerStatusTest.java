package com.les.its.open.area.juncer.bean;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.assertj.core.api.Assertions.*;

@DisplayName("ControllerStatus Tests")
class ControllerStatusTest {

    @Test
    @DisplayName("Default constructor should initialize all fields to 255")
    void defaultConstructor_ShouldInitializeAllFieldsTo255() {
        // Act
        ControllerStatus status = new ControllerStatus();

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(255);
        assertThat(status.getCharacter()).isEqualTo(255);
        assertThat(status.getPlanNo()).isEqualTo(255);
        assertThat(status.getDayPlanNo()).isEqualTo(255);
        assertThat(status.getSegmentNo()).isEqualTo(255);
        assertThat(status.getScheduleNo()).isEqualTo(255);
        assertThat(status.getStageNoPre()).isEqualTo(255);
        assertThat(status.getStageNo()).isEqualTo(255);
    }

    @Test
    @DisplayName("AllArgsConstructor should set all fields correctly")
    void allArgsConstructor_ShouldSetAllFieldsCorrectly() {
        // Act
        ControllerStatus status = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(1);
        assertThat(status.getCharacter()).isEqualTo(2);
        assertThat(status.getPlanNo()).isEqualTo(3);
        assertThat(status.getDayPlanNo()).isEqualTo(4);
        assertThat(status.getSegmentNo()).isEqualTo(5);
        assertThat(status.getScheduleNo()).isEqualTo(6);
        assertThat(status.getStageNoPre()).isEqualTo(7);
        assertThat(status.getStageNo()).isEqualTo(8);
    }

    @Test
    @DisplayName("Setters should update field values correctly")
    void setters_ShouldUpdateFieldValuesCorrectly() {
        // Arrange
        ControllerStatus status = new ControllerStatus();

        // Act
        status.setControlModeNo(100);
        status.setCharacter(200);
        status.setPlanNo(50);
        status.setDayPlanNo(75);
        status.setSegmentNo(25);
        status.setScheduleNo(150);
        status.setStageNoPre(10);
        status.setStageNo(20);

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(100);
        assertThat(status.getCharacter()).isEqualTo(200);
        assertThat(status.getPlanNo()).isEqualTo(50);
        assertThat(status.getDayPlanNo()).isEqualTo(75);
        assertThat(status.getSegmentNo()).isEqualTo(25);
        assertThat(status.getScheduleNo()).isEqualTo(150);
        assertThat(status.getStageNoPre()).isEqualTo(10);
        assertThat(status.getStageNo()).isEqualTo(20);
    }

    @Test
    @DisplayName("Getters should return correct field values")
    void getters_ShouldReturnCorrectFieldValues() {
        // Arrange
        ControllerStatus status = new ControllerStatus(10, 20, 30, 40, 50, 60, 70, 80);

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(10);
        assertThat(status.getCharacter()).isEqualTo(20);
        assertThat(status.getPlanNo()).isEqualTo(30);
        assertThat(status.getDayPlanNo()).isEqualTo(40);
        assertThat(status.getSegmentNo()).isEqualTo(50);
        assertThat(status.getScheduleNo()).isEqualTo(60);
        assertThat(status.getStageNoPre()).isEqualTo(70);
        assertThat(status.getStageNo()).isEqualTo(80);
    }

    @Test
    @DisplayName("equals should work correctly for same values")
    void equals_ShouldWorkCorrectlyForSameValues() {
        // Arrange
        ControllerStatus status1 = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);
        ControllerStatus status2 = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);

        // Assert
        assertThat(status1).isEqualTo(status2);
    }

    @Test
    @DisplayName("equals should work correctly for different values")
    void equals_ShouldWorkCorrectlyForDifferentValues() {
        // Arrange
        ControllerStatus status1 = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);
        ControllerStatus status2 = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 9);

        // Assert
        assertThat(status1).isNotEqualTo(status2);
    }

    @Test
    @DisplayName("hashCode should be consistent")
    void hashCode_ShouldBeConsistent() {
        // Arrange
        ControllerStatus status1 = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);
        ControllerStatus status2 = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);

        // Assert
        assertThat(status1.hashCode()).isEqualTo(status2.hashCode());
    }

    @Test
    @DisplayName("toString should contain all field values")
    void toString_ShouldContainAllFieldValues() {
        // Arrange
        ControllerStatus status = new ControllerStatus(1, 2, 3, 4, 5, 6, 7, 8);

        // Act
        String toString = status.toString();

        // Assert
        assertThat(toString).contains("controlModeNo=1");
        assertThat(toString).contains("character=2");
        assertThat(toString).contains("planNo=3");
        assertThat(toString).contains("dayPlanNo=4");
        assertThat(toString).contains("segmentNo=5");
        assertThat(toString).contains("scheduleNo=6");
        assertThat(toString).contains("stageNoPre=7");
        assertThat(toString).contains("stageNo=8");
    }

    @Test
    @DisplayName("Should handle negative values correctly")
    void shouldHandleNegativeValuesCorrectly() {
        // Act
        ControllerStatus status = new ControllerStatus(-1, -2, -3, -4, -5, -6, -7, -8);

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(-1);
        assertThat(status.getCharacter()).isEqualTo(-2);
        assertThat(status.getPlanNo()).isEqualTo(-3);
        assertThat(status.getDayPlanNo()).isEqualTo(-4);
        assertThat(status.getSegmentNo()).isEqualTo(-5);
        assertThat(status.getScheduleNo()).isEqualTo(-6);
        assertThat(status.getStageNoPre()).isEqualTo(-7);
        assertThat(status.getStageNo()).isEqualTo(-8);
    }

    @Test
    @DisplayName("Should handle maximum integer values correctly")
    void shouldHandleMaximumIntegerValuesCorrectly() {
        // Act
        ControllerStatus status = new ControllerStatus(
            Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE,
            Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE
        );

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getCharacter()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getPlanNo()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getDayPlanNo()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getSegmentNo()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getScheduleNo()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getStageNoPre()).isEqualTo(Integer.MAX_VALUE);
        assertThat(status.getStageNo()).isEqualTo(Integer.MAX_VALUE);
    }

    @Test
    @DisplayName("Should handle zero values correctly")
    void shouldHandleZeroValuesCorrectly() {
        // Act
        ControllerStatus status = new ControllerStatus(0, 0, 0, 0, 0, 0, 0, 0);

        // Assert
        assertThat(status.getControlModeNo()).isEqualTo(0);
        assertThat(status.getCharacter()).isEqualTo(0);
        assertThat(status.getPlanNo()).isEqualTo(0);
        assertThat(status.getDayPlanNo()).isEqualTo(0);
        assertThat(status.getSegmentNo()).isEqualTo(0);
        assertThat(status.getScheduleNo()).isEqualTo(0);
        assertThat(status.getStageNoPre()).isEqualTo(0);
        assertThat(status.getStageNo()).isEqualTo(0);
    }

    @Test
    @DisplayName("Default constructor values should represent uninitialized state")
    void defaultConstructorValues_ShouldRepresentUninitializedState() {
        // Arrange
        ControllerStatus status = new ControllerStatus();

        // Assert - 255 typically represents an uninitialized or invalid state in protocols
        assertThat(status.getControlModeNo()).isEqualTo(255);
        assertThat(status.getCharacter()).isEqualTo(255);
        assertThat(status.getPlanNo()).isEqualTo(255);
        assertThat(status.getDayPlanNo()).isEqualTo(255);
        assertThat(status.getSegmentNo()).isEqualTo(255);
        assertThat(status.getScheduleNo()).isEqualTo(255);
        assertThat(status.getStageNoPre()).isEqualTo(255);
        assertThat(status.getStageNo()).isEqualTo(255);
    }
}
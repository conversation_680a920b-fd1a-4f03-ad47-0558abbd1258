
content_type  text/plain
content_encoding  UTF-8
delivery_mode  2

{
  "errorCode": "0",
  "errorInfo": "",
  "mapKey": 1,
  "objectId": "10001",
  "objectList": [
    {
      "controlledJunctionNum": 1,
      "hostIpv4s": [
        {
          "commType": 1,
          "enabled": 1,
          "ip": "*******",
          "port": 1,
          "protoType": 2
        },
        {
          "commType": 1,
          "enabled": 0,
          "ip": "********",
          "port": 1,
          "protoType": 2
        },
        {
          "commType": 1,
          "enabled": 0,
          "ip": "*******",
          "port": 1,
          "protoType": 2
        },
        {
          "commType": 2,
          "enabled": 1,
          "ip": "*******",
          "port": 1,
          "protoType": 3
        }
      ],
      "hostIpv6s": [
        {
          "commType": 2,
          "enabled": 1,
          "ip": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00",
          "port": 1,
          "protoType": 3
        },
        {
          "commType": 2,
          "enabled": 1,
          "ip": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00",
          "port": 1,
          "protoType": 3
        },
        {
          "commType": 2,
          "enabled": 1,
          "ip": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00",
          "port": 1,
          "protoType": 3
        },
        {
          "commType": 2,
          "enabled": 1,
          "ip": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00",
          "port": 1,
          "protoType": 3
        }
      ],
      "ipEnabled": 1,
      "ipv4": {
        "gateway": "*******",
        "ip": "*******",
        "mask": "*************"
      },
      "ipv6": {
        "gateway": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00",
        "ip": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00",
        "mask": "0102:0304:0506:0708:090a:0b0c:0d0e:0f00"
      },
      "lat": 39.932061,
      "lon": 11.65134592,
      "macAddress": "77-88-99-66-55-44",
      "noArea": 1,
      "noJunc": 1,
      "productionDate": "2025-04-10",
      "type": "LES-TSC",
      "installIntersection": "测试路口",
       "cmmType": 1
    }
  ],
  "operator": 1,
  "sequenceCode": 788,
  "signalControllerID": "320100LS00010",
  "source": 0,
  "timeStamp": 0,
  "timeStampRev": 0,
  "type": 1
}

FD FD FD FD FD
01 00
01
17 00
02
12
02
00
01
02
03 00
3d 3e 75 b6 95 01 00 00
00
00
08 09

FD FD FD FD FD
01 00
01
17 00
02
12
02
02
01
02
03 00
3d 3e 75 b6 95 01 00 00
00
00
08 09

FD FD FD FD FD
01 00
01
17 00
02
12
02
03
01
02
03 00
3d 3e 75 b6 95 01 00 00
00
00
08 09





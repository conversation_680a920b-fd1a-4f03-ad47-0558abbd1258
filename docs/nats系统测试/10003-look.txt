
content_type  text/plain
content_encoding  UTF-8
delivery_mode  2

{
  "version": null,
  "token": null,
  "type": 1,
  "operator": 2,
  "sequenceCode": "1",
  "objectId": "10003",
  "signalControllerID": "320100LS00010",
  "source" : 1,
  "timeStamp": 1678496000000,
  "errorCode": null,
  "errorInfo": null,
  "objectList": [
     1
  ]
}


正确应答

{
  "errorCode": "0",
  "errorInfo": "",
  "mapKey": 1,
  "objectId": "10003",
  "objectList": [
    {
      "lampFaultThres": [
        {
          "greenThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "lampGroupNo": 1,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 2,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 3,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 4,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 5,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 6,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 7,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 8,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 9,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 10,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 11,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 12,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 13,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 14,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 15,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 16,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 17,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 18,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 19,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 20,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 21,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 22,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 23,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 24,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 25,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 26,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 27,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 28,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 29,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 30,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 31,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 32,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 33,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 34,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 35,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 36,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 37,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 38,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 39,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 40,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 41,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 42,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 43,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 44,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 45,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 46,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 47,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 48,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 49,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 50,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 51,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 52,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 53,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 54,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 55,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 56,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 57,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 58,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 59,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 60,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 61,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 62,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 63,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        },
        {
          "greenThreshold": {
            "currentLower": 1,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 1
          },
          "lampGroupNo": 64,
          "redThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          },
          "yellowThreshold": {
            "currentLower": 3,
            "currentUpper": 4,
            "voltageLower": 1,
            "voltageUpper": 2
          }
        }
      ],
      "lampGroups": [
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 1,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 2,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 3,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 4,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 5,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 6,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 7,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 8,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 9,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 10,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 11,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 12,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 13,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 14,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 15,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 16,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 17,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 18,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 19,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 20,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 21,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 22,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 23,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 24,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 25,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 26,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 27,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 28,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 29,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 30,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 31,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 32,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 33,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 34,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 35,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 36,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 37,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 38,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 39,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 40,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 41,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 42,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 43,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 44,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 45,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 46,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 47,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 48,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 49,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 50,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 51,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 52,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 53,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 54,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 55,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 56,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 57,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 58,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 1,
          "direction": 1,
          "lampGroupNo": 59,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 60,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 61,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 62,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "detectFlag": 0,
          "direction": 1,
          "lampGroupNo": 63,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        },
        {
          "crossingSeqNo": 1,
          "direction": 1,
          "lampGroupNo": 64,
          "name": "左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转\u0000\u0000\u0000\u0000左转",
          "type": 1
        }
      ],
      "uiThreshold": {
        "currentThresholdLower": 2,
        "currentThresholdUpper": 9,
        "voltageThresholdLower": 1,
        "voltageThresholdUpper": 8
      }
    }
  ],
  "operator": 2,
  "sequenceCode": 1,
  "signalControllerID": "320100LS00010",
  "source": 0,
  "timeStamp": 0,
  "timeStampRev": 0,
  "type": 2
}
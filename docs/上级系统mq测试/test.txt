
content_type  text/plain
content_encoding  UTF-8
delivery_mode  2



-- 控制命令
{
    "msg1": 4,
    "msg2": 5,
    "msg3": 0,
    "msg4": 0,
    "noArea": 0,
    "noJunc": 10,
    "source": 3,
    "timeStamp": 22222,
    "data": {
      "crossingSeqNo": 1,
      "stageNo": 2,
      "stageSeq": 3,
      "duration": 60,
      "laggingTime": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
      "earlyCutOffTime": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
    }
}

-- 加载参数
{
    "msg1": 2,
    "msg2": 17,
    "msg3": 1,
    "msg4": 0,
    "noArea": 0,
    "noJunc": 10,
    "source": 3,
    "timeStamp": 22222,
  "data":  {
        "installIntersection": "东风路与建设路交叉口",
        "tscId": 1001,
        "controlledJunctionNum": 1,
        "longitude": 116.5134592,
        "latitude": 39.932061
      }
}

-- 调看参数
{
    "msg1": 2,
    "msg2": 1,
    "msg3":0,
    "msg4": 0,
    "noArea": 0,
    "noJunc": 10,
    "source": 3,
    "timeStamp": 22222,
    "data":  {
         "offset" : 1,
         "count" : 1
    }
}
-- 指定阶段控制
{
  "crossingSeqNo": 1,
  "stageNo": 2,
  "stageSeq": 3,
  "duration": 60,
  "laggingTime": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
  "earlyCutOffTime": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
}

-- 指定阶段控制应答
FD FD FD FD FD
01 00
01
9C 00
04
05
01
00
01
02
03 00
3d 3e 75 b6 95 01 00 00
 04
 04
 01
 02
 03
 3c 00
 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05 05
 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03
 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03
 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03
 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03 03
B2 C3


----- 事务交易成功应答
FD FD FD FD FD
01 00
01
9C 00
04
12
01
00
01
02
03 00
3d 3e 75 b6 95 01 00 00



B2 C3
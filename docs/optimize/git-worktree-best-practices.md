# Git Worktree 最佳实践指南

## 概述

Git Worktree 是 Git 2.5+ 引入的功能，允许你在同一个仓库中同时检出多个分支到不同的工作目录。这对于需要同时在多个分支上工作的场景特别有用。

## 基本概念

- **主工作树（Main Worktree）**: 包含 `.git` 目录的原始仓库
- **链接工作树（Linked Worktree）**: 通过 `git worktree add` 创建的额外工作目录
- **工作树列表**: 所有工作树的集合，可通过 `git worktree list` 查看

## 常用命令

### 1. 添加工作树

```bash
# 基本语法
git worktree add <path> <branch>

# 示例：为 develop 分支创建工作树
git worktree add ../openles-develop develop

# 创建并检出新分支
git worktree add ../openles-feature-auth -b feature/auth

# 从远程分支创建工作树
git worktree add ../openles-hotfix -b hotfix/critical-fix origin/main
```

### 2. 查看工作树

```bash
# 列出所有工作树
git worktree list

# 详细信息
git worktree list --porcelain
```

### 3. 删除工作树

```bash
# 先删除工作目录
rm -rf ../openles-develop

# 清理工作树记录
git worktree prune

# 或者直接删除（推荐）
git worktree remove ../openles-develop
```

### 4. 移动工作树

```bash
# 移动工作树到新位置
git worktree move ../openles-develop ../projects/openles-develop
```

## 实际使用场景

### 场景1：同时开发多个功能

```bash
# 当前在 main 分支开发
cd /home/<USER>/Desktop/workspace/openles

# 需要同时开发新功能
git worktree add ../openles-signal-optimization -b feature/signal-optimization

# 需要修复紧急 bug
git worktree add ../openles-hotfix -b hotfix/connection-issue

# 现在可以在三个目录中同时工作
# /home/<USER>/Desktop/workspace/openles (main)
# /home/<USER>/Desktop/workspace/openles-signal-optimization (feature/signal-optimization)
# /home/<USER>/Desktop/workspace/openles-hotfix (hotfix/connection-issue)
```

### 场景2：代码审查和测试

```bash
# 创建工作树用于审查 PR
git worktree add ../openles-review pr/123

# 在审查目录中测试
cd ../openles-review
mvn clean test

# 审查完成后删除
cd /home/<USER>/Desktop/workspace/openles
git worktree remove ../openles-review
```

### 场景3：并行构建不同版本

```bash
# 为不同环境创建工作树
git worktree add ../openles-dev -b develop
git worktree add ../openles-staging -b staging
git worktree add ../openles-prod -b production

# 同时构建不同版本
cd ../openles-dev && mvn clean package -Pdev &
cd ../openles-staging && mvn clean package -Ptest &
cd ../openles-prod && mvn clean package -Pprod &
```

## 最佳实践

### 1. 目录组织

```bash
# 推荐的目录结构
projects/
├── openles/                    # 主工作树
├── openles-develop/           # develop 分支
├── openles-feature-xxx/       # 功能分支
├── openles-hotfix-xxx/        # 热修复分支
└── openles-release-xxx/       # 发布分支
```

### 2. 命名约定

```bash
# 功能开发
git worktree add ../openles-feature-auth -b feature/auth
git worktree add ../openles-feature-payment -b feature/payment

# 修复
git worktree add ../openles-fix-memory-leak -b fix/memory-leak
git worktree add ../openles-hotfix-security -b hotfix/security-patch

# 发布
git worktree add ../openles-release-v2.1 -b release/v2.1.0
```

### 3. 配置同步

```bash
# 为新工作树应用相同的配置
cd ../openles-feature-auth

# 复制 IDE 配置（如果需要）
cp ../openles/.vscode/settings.json .vscode/
cp ../openles/.idea/codeStyleSettings.xml .idea/

# 复制本地配置文件
cp ../openles/application-local.yml src/main/resources/
```

### 4. 分支管理脚本

创建便捷脚本 `scripts/worktree-helper.sh`:

```bash
#!/bin/bash

# 创建功能分支工作树
create_feature() {
    local feature_name=$1
    if [ -z "$feature_name" ]; then
        echo "Usage: create_feature <feature-name>"
        return 1
    fi
    
    git worktree add "../openles-feature-$feature_name" -b "feature/$feature_name"
    echo "Created feature worktree: ../openles-feature-$feature_name"
}

# 创建修复分支工作树
create_hotfix() {
    local fix_name=$1
    if [ -z "$fix_name" ]; then
        echo "Usage: create_hotfix <fix-name>"
        return 1
    fi
    
    git worktree add "../openles-hotfix-$fix_name" -b "hotfix/$fix_name" origin/main
    echo "Created hotfix worktree: ../openles-hotfix-$fix_name"
}

# 清理已合并的工作树
cleanup_merged() {
    git worktree list --porcelain | grep -E '^worktree' | while read -r line; do
        worktree_path=$(echo "$line" | cut -d' ' -f2)
        if [ "$worktree_path" != "$(pwd)" ]; then
            cd "$worktree_path"
            branch=$(git branch --show-current)
            if git merge-base --is-ancestor "$branch" main; then
                echo "Removing merged worktree: $worktree_path"
                cd - > /dev/null
                git worktree remove "$worktree_path"
            fi
            cd - > /dev/null
        fi
    done
}

# 使用示例
# source scripts/worktree-helper.sh
# create_feature "signal-optimization"
# create_hotfix "connection-timeout"
# cleanup_merged
```

## 注意事项

### 1. 资源使用

- 每个工作树都是完整的工作副本，会占用额外磁盘空间
- 大型项目建议限制同时存在的工作树数量
- 及时清理不再需要的工作树

### 2. IDE 配置

```bash
# VSCode 工作区配置
# .vscode/openles.code-workspace
{
    "folders": [
        {
            "name": "Main",
            "path": "."
        },
        {
            "name": "Develop",
            "path": "../openles-develop"
        },
        {
            "name": "Feature",
            "path": "../openles-feature-auth"
        }
    ],
    "settings": {
        "java.configuration.workspaces": [
            ".",
            "../openles-develop",
            "../openles-feature-auth"
        ]
    }
}
```

### 3. 构建工具配置

```bash
# Maven: 避免并行构建冲突
# 为不同工作树使用不同的构建目录
cd ../openles-develop
mvn clean package -Dmaven.build.directory=target-develop

cd ../openles-feature-auth  
mvn clean package -Dmaven.build.directory=target-feature
```

### 4. 数据库配置

```yaml
# application-dev.yml - 为不同工作树使用不同数据库
spring:
  datasource:
    url: jdbc:h2:mem:openles-develop
    
# application-feature.yml
spring:
  datasource:
    url: jdbc:h2:mem:openles-feature
```

## 常见问题

### Q: 工作树之间能否共享未提交的更改？
A: 不能。每个工作树都有独立的工作区和暂存区。

### Q: 如何在工作树之间同步配置？
A: 可以使用符号链接或脚本自动同步配置文件。

### Q: 工作树删除后如何恢复？
A: 如果分支还存在，可以重新创建工作树。如果需要恢复未提交的更改，需要依赖备份。

### Q: 是否可以在工作树中创建子工作树？
A: 不推荐。应该在主仓库中管理所有工作树。

## 性能优化建议

1. **定期清理**: 使用 `git worktree prune` 清理无效记录
2. **限制数量**: 建议同时存在的工作树不超过 5 个
3. **磁盘空间**: 监控磁盘使用情况，及时删除不需要的工作树
4. **网络同步**: 避免在多个工作树中同时进行大量网络操作

## 总结

Git Worktree 是一个强大的工具，特别适合：
- 需要同时在多个分支工作
- 并行开发和测试
- 代码审查和比较
- CI/CD 流水线中的并行构建

合理使用 Git Worktree 可以显著提高开发效率，但需要注意资源管理和配置同步。
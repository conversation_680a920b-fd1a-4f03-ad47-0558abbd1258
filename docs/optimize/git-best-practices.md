# Git 最佳实践指南

## 概述

本指南基于 OpenLES 项目的实际需求，提供 Git 使用的最佳实践，帮助团队提高代码管理效率和协作质量。

## 分支管理策略

### 1. 分支模型 (Git Flow)

```bash
# 主要分支
main        # 生产环境分支，只包含稳定版本
develop     # 开发主分支，集成所有新功能

# 支持分支
feature/*   # 功能开发分支
hotfix/*    # 紧急修复分支
release/*   # 发布准备分支
```

### 2. 分支命名规范

```bash
# 功能分支
feature/signal-optimization      # 信号优化功能
feature/api-authentication      # API 认证功能
feature/performance-monitoring  # 性能监控功能

# 修复分支
hotfix/connection-timeout       # 连接超时修复
hotfix/memory-leak             # 内存泄漏修复
hotfix/security-patch          # 安全补丁

# 发布分支
release/v2.1.0                 # 版本发布
release/v2.1.1                 # 补丁版本

# 实验分支
experiment/netty-upgrade       # Netty 升级实验
experiment/redis-cluster       # Redis 集群实验
```

### 3. 分支操作示例

```bash
# 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/signal-optimization

# 开发完成后合并
git checkout develop
git pull origin develop
git merge --no-ff feature/signal-optimization
git push origin develop
git branch -d feature/signal-optimization

# 创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/v2.1.0

# 发布完成后
git checkout main
git merge --no-ff release/v2.1.0
git tag -a v2.1.0 -m "Release version 2.1.0"
git push origin main --tags

git checkout develop
git merge --no-ff release/v2.1.0
git push origin develop
git branch -d release/v2.1.0
```

## 提交规范

### 1. 提交信息格式

```bash
# 格式：<type>(<scope>): <subject>
# 
# <body>
# 
# <footer>

# 示例
feat(netty): add connection pool management

Implement connection pool for better resource utilization:
- Add ConnectionPool class with configurable size
- Implement connection reuse strategy
- Add monitoring metrics for pool status

Closes #123
```

### 2. 提交类型

```bash
feat:     # 新功能
fix:      # 修复 bug
docs:     # 文档更新
style:    # 代码格式调整
refactor: # 重构
perf:     # 性能优化
test:     # 测试相关
build:    # 构建相关
ci:       # CI/CD 相关
chore:    # 其他杂项
```

### 3. 提交示例

```bash
# 功能开发
git add .
git commit -m "feat(protocol): implement LES message validation

Add comprehensive validation for LES protocol messages:
- Validate message structure and required fields
- Add CRC checksum verification
- Implement message length validation
- Add error handling for malformed messages

Refs #456"

# 修复 bug
git commit -m "fix(netty): resolve connection timeout issue

Fix connection timeout in high-load scenarios:
- Increase default timeout from 5s to 30s
- Add configurable timeout settings
- Improve error logging for timeout cases

Fixes #789"

# 重构
git commit -m "refactor(service): reorganize message processing logic

Separate message processing into dedicated handlers:
- Extract command handlers to separate classes
- Implement handler registry pattern
- Improve error handling and logging

No functional changes"
```

## 代码审查流程

### 1. Pull Request 流程

```bash
# 1. 创建功能分支并开发
git checkout -b feature/rate-limiting
# ... 开发代码 ...

# 2. 推送分支
git push -u origin feature/rate-limiting

# 3. 创建 Pull Request
gh pr create --title "feat(api): implement rate limiting for signal controllers" \
  --body "$(cat <<'EOF'
## 功能描述
为信号机控制器 API 实现速率限制功能

## 主要变更
- 添加 Bucket4j 速率限制器
- 实现基于 IP 的限流策略
- 添加限流配置和监控
- 更新 API 文档

## 测试计划
- [x] 单元测试覆盖核心逻辑
- [x] 集成测试验证限流效果
- [x] 压力测试验证性能影响
- [ ] 生产环境配置验证

## 相关问题
Closes #123
EOF
)"

# 4. 代码审查通过后合并
git checkout develop
git pull origin develop
git merge --no-ff feature/rate-limiting
git push origin develop
git branch -d feature/rate-limiting
```

### 2. 代码审查检查清单

```markdown
## 代码质量检查
- [ ] 代码符合项目编码规范
- [ ] 没有明显的性能问题
- [ ] 错误处理完整
- [ ] 日志记录合理
- [ ] 单元测试覆盖主要逻辑

## 功能检查
- [ ] 功能实现符合需求
- [ ] 边界情况处理正确
- [ ] 配置参数合理
- [ ] 向后兼容性保证

## 安全检查
- [ ] 输入验证完整
- [ ] 权限控制正确
- [ ] 敏感信息保护
- [ ] SQL 注入防护

## 文档检查
- [ ] 代码注释充分
- [ ] API 文档更新
- [ ] 变更日志记录
```

## 标签和版本管理

### 1. 语义化版本控制

```bash
# 版本号格式：MAJOR.MINOR.PATCH
# 2.1.3
# │ │ │
# │ │ └── 补丁版本（向后兼容的问题修正）
# │ └──── 次版本号（向后兼容的功能性新增）
# └────── 主版本号（不向后兼容的 API 修改）

# 示例
v2.0.0  # 重大重构，不向后兼容
v2.1.0  # 新增功能，向后兼容
v2.1.1  # 修复 bug，向后兼容
```

### 2. 标签创建和管理

```bash
# 创建标签
git tag -a v2.1.0 -m "Release version 2.1.0

New Features:
- Rate limiting for API endpoints
- Enhanced logging with structured format
- Performance monitoring dashboard

Bug Fixes:
- Fixed connection timeout issues
- Resolved memory leak in message processing
- Improved error handling in protocol parsing

Breaking Changes:
None"

# 推送标签
git push origin v2.1.0
git push origin --tags

# 查看标签
git tag -l
git show v2.1.0

# 删除标签
git tag -d v2.1.0
git push origin :refs/tags/v2.1.0
```

### 3. 发布脚本

创建 `scripts/release.sh`:

```bash
#!/bin/bash

# 发布脚本
set -e

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "Usage: $0 <version>"
    echo "Example: $0 2.1.0"
    exit 1
fi

echo "Creating release $VERSION..."

# 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "develop" ]; then
    echo "Error: Must be on develop branch"
    exit 1
fi

# 确保工作目录干净
if [ -n "$(git status --porcelain)" ]; then
    echo "Error: Working directory is not clean"
    exit 1
fi

# 更新 develop 分支
git pull origin develop

# 创建发布分支
git checkout -b "release/v$VERSION"

# 更新版本号
echo "Updating version to $VERSION..."
# 这里可以添加具体的版本号更新逻辑
# 例如更新 pom.xml 中的版本号

# 提交版本更新
git add .
git commit -m "chore(release): bump version to $VERSION"

# 运行测试
echo "Running tests..."
mvn clean test

# 创建标签
git tag -a "v$VERSION" -m "Release version $VERSION"

# 合并到 main
git checkout main
git pull origin main
git merge --no-ff "release/v$VERSION"

# 推送到远程
git push origin main
git push origin "v$VERSION"

# 合并回 develop
git checkout develop
git merge --no-ff "release/v$VERSION"
git push origin develop

# 删除发布分支
git branch -d "release/v$VERSION"

echo "Release $VERSION completed successfully!"
```

## 配置管理

### 1. 全局配置

```bash
# 用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 默认分支
git config --global init.defaultBranch main

# 编辑器
git config --global core.editor "code --wait"

# 合并工具
git config --global merge.tool vimdiff

# 推送策略
git config --global push.default simple

# 自动换行
git config --global core.autocrlf input  # Linux/Mac
git config --global core.autocrlf true   # Windows

# 别名配置
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
```

### 2. 项目配置

```bash
# 在项目根目录创建 .gitconfig
[core]
    ignorecase = false
    filemode = true

[branch "main"]
    remote = origin
    merge = refs/heads/main

[branch "develop"]
    remote = origin
    merge = refs/heads/develop

# 钩子配置
[core]
    hooksPath = .githooks
```

### 3. .gitignore 配置

```bash
# 创建 .gitignore
cat > .gitignore << 'EOF'
# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# IDE
.idea/
*.iml
.vscode/
*.swp
*.swo
*~

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# Application specific
application-local.yml
application-*.yml
!application-dev.yml
!application-test.yml
!application-prod.yml

# Runtime
*.pid
*.tmp
EOF
```

## 高级技巧

### 1. 交互式 Rebase

```bash
# 整理提交历史
git rebase -i HEAD~3

# 在编辑器中：
# pick abc1234 feat(api): add new endpoint
# squash def5678 fix(api): fix typo
# squash ghi9012 fix(api): improve error handling

# 结果：三个提交合并为一个
```

### 2. 选择性提交

```bash
# 交互式添加
git add -p

# 选择性提交文件的部分更改
git add -p src/main/java/com/openles/service/MessageService.java
```

### 3. 搜索和诊断

```bash
# 搜索提交历史
git log --grep="performance"
git log --author="john.doe"
git log --since="2023-01-01" --until="2023-12-31"

# 搜索代码
git grep "ConnectionPool" -- "*.java"

# 查看文件历史
git log -p -- src/main/java/com/openles/config/NettyConfig.java

# 找出引入 bug 的提交
git bisect start
git bisect bad HEAD
git bisect good v2.0.0
# Git 会自动切换到中间提交，测试后标记 good 或 bad
git bisect good  # 或 git bisect bad
# 重复直到找到问题提交
```

### 4. 协作技巧

```bash
# 同步远程分支
git fetch --all --prune

# 清理已合并的分支
git branch --merged main | grep -v "main\|develop" | xargs -n 1 git branch -d

# 查看分支状态
git for-each-ref --format='%(refname:short) %(upstream:short) %(upstream:track)' refs/heads

# 提交统计
git shortlog -s -n --since="2023-01-01"
```

## 性能优化

### 1. 大型仓库优化

```bash
# 浅克隆
git clone --depth 1 https://github.com/company/openles.git

# 部分克隆
git clone --filter=blob:none https://github.com/company/openles.git

# 稀疏检出
git config core.sparseCheckout true
echo "src/main/java/com/openles/core/*" > .git/info/sparse-checkout
git read-tree -m -u HEAD
```

### 2. 缓存优化

```bash
# 启用文件系统监控
git config core.fsmonitor true

# 启用 untracked 缓存
git config core.untrackedCache true

# 维护仓库
git gc --aggressive --prune=now
```

## 团队协作规范

### 1. 日常工作流程

```bash
# 1. 开始工作前同步
git checkout develop
git pull origin develop

# 2. 创建功能分支
git checkout -b feature/task-description

# 3. 开发过程中定期提交
git add .
git commit -m "feat(module): implement specific functionality"

# 4. 推送分支（首次）
git push -u origin feature/task-description

# 5. 后续推送
git push

# 6. 创建 Pull Request
gh pr create --title "feat(module): task description" --body "详细描述"

# 7. 代码审查通过后合并
git checkout develop
git pull origin develop
git branch -d feature/task-description
```

### 2. 冲突解决

```bash
# 合并冲突
git merge develop
# 解决冲突后
git add .
git commit -m "resolve merge conflicts"

# 变基冲突
git rebase develop
# 解决冲突后
git add .
git rebase --continue
```

### 3. 紧急修复流程

```bash
# 1. 从 main 创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/critical-issue

# 2. 修复问题
# ... 修复代码 ...
git add .
git commit -m "fix(critical): resolve production issue"

# 3. 测试验证
mvn clean test

# 4. 合并到 main
git checkout main
git merge --no-ff hotfix/critical-issue
git tag -a v2.1.1 -m "Hotfix release 2.1.1"
git push origin main --tags

# 5. 合并到 develop
git checkout develop
git merge --no-ff hotfix/critical-issue
git push origin develop

# 6. 清理分支
git branch -d hotfix/critical-issue
```

## 常见问题解决

### 1. 撤销操作

```bash
# 撤销最后一次提交（保留更改）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃更改）
git reset --hard HEAD~1

# 撤销文件更改
git checkout -- filename

# 撤销已暂存的文件
git reset HEAD filename

# 修改最后一次提交
git commit --amend -m "new commit message"
```

### 2. 历史清理

```bash
# 删除敏感文件的历史记录
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch path/to/sensitive/file' \
  --prune-empty --tag-name-filter cat -- --all

# 清理引用
git for-each-ref --format='delete %(refname)' refs/original | git update-ref --stdin
git reflog expire --expire=now --all
git gc --prune=now
```

### 3. 数据恢复

```bash
# 恢复已删除的分支
git reflog
git checkout -b recovered-branch <commit-hash>

# 恢复已删除的提交
git reflog
git cherry-pick <commit-hash>
```

## 监控和分析

### 1. 仓库分析脚本

创建 `scripts/git-stats.sh`:

```bash
#!/bin/bash

echo "=== Git Repository Statistics ==="
echo

echo "Total commits: $(git rev-list --all --count)"
echo "Total branches: $(git branch -r | wc -l)"
echo "Total tags: $(git tag | wc -l)"
echo

echo "=== Top Contributors ==="
git shortlog -s -n | head -10
echo

echo "=== Recent Activity ==="
git log --oneline --since="1 week ago" | head -10
echo

echo "=== Repository Size ==="
git count-objects -vH
echo

echo "=== Branch Status ==="
git for-each-ref --format='%(refname:short) %(upstream:track)' refs/heads
```

### 2. 提交质量检查

创建 `scripts/commit-quality.sh`:

```bash
#!/bin/bash

# 检查提交消息格式
check_commit_message() {
    local commit_msg="$1"
    if [[ ! $commit_msg =~ ^(feat|fix|docs|style|refactor|perf|test|build|ci|chore)(\(.+\))?: .+ ]]; then
        echo "Invalid commit message format: $commit_msg"
        return 1
    fi
    return 0
}

# 检查最近的提交
git log --oneline -10 | while read commit; do
    msg=$(echo "$commit" | cut -d' ' -f2-)
    if ! check_commit_message "$msg"; then
        echo "Commit $(echo "$commit" | cut -d' ' -f1) has invalid message format"
    fi
done
```

## 总结

遵循这些 Git 最佳实践可以：

1. **提高代码质量**：通过规范的提交和审查流程
2. **增强协作效率**：清晰的分支策略和工作流程
3. **降低风险**：完善的测试和发布流程
4. **便于维护**：良好的历史记录和文档

关键要点：
- 保持提交粒度合适，每次提交只包含相关更改
- 使用描述性的提交消息，遵循约定的格式
- 定期同步远程分支，避免大规模冲突
- 合理使用分支，保持主分支稳定
- 及时清理无用分支和标签
- 重视代码审查，确保代码质量